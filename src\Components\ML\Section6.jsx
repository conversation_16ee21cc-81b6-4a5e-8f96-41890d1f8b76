import React from "react";
import ServiceLifecycleCard from "../Services/ServiceLifecycleCard_2";
import ServiceCount from "../Services/ServiceCount";

const Section6 = () => {
  const researchAndPlan = [
    "In-depth exploration of the structured and unstructured data",
    "Perform proper research and analysis",
    "Step-by-step planning and strategizing of the process",
  ];

  const develop = [
    "Develop smart ML models",
    "Carry out training to test their efficiencies",
    "Perform iteration",
  ];

  const deploy = [
    "Perform necessary testing",
    "Make the code error and bug-free",
    "Ask for your feedback",
    "Deploy the final product onto the platform that best suits your software requirements.",
  ];

  const supportAndMaintenance = [
    "Provide <a href='/Maintenance_and_Support' class='text-[#7716BC] hover:underline'> continuous support and maintenance</a> post-deployment",
    "Make sure the application is always performing as per the standards",
  ];

  return (
    <section className="bg-pink-100 py-8 my-24 ">
      <div className="w-[85vw] mx-auto ">
        <h2 className="text-2xl md:text-4xl text-center font-semibold mb-2 md:mb-4">
          Our Innovative{" "}
          <span className="text-[#F245A1]">
            Machine Learning <br /> Development 
          </span>{" "}
          Process
        </h2>
        <div className="relative my-10">
          <div className="w-auto md:w-[75%] flex flex-col md:flex-row justify-between items-center gap-16 mb-20">
            <div className="flex flex-row md:flex-col justify-center items-center">
              <ServiceLifecycleCard
                title="Research and Plan"
                items={researchAndPlan}
              />
              <ServiceCount count={"1"} />
            </div>
            <div className="flex flex-row md:flex-col justify-center items-center">
              
              <ServiceLifecycleCard title="Develop" items={develop} />
              <ServiceCount count={"2"} />
            </div>
          </div>

          {/* Dotted arrow lines for larger screens */}
          <div className="hidden md:block absolute top-[50%] left-[27%]">
            <div
              className=" w-[180px] h-[107px] border-dashed border-t-2 border-r-2 border-gray-700 absolute"
              style={{
                top: "50%",
                left: "50%",
                transform: "translate(-50%, -50%)",
              }}
            />
          </div>

          {/* Dotted arrow lines for larger screens */}
          <div className="hidden md:block absolute top-[53%] left-[50%]">
            <div
              className="w-[280px] h-[90px] border-dashed border-b-2 border-r-2 border-gray-700 absolute"
              style={{
                top: "50%",
                left: "50%",
                transform: "translate(-50%, -50%)",
              }}
            />
          </div>

          {/* Dotted arrow lines for larger screens */}
          <div className=" hidden md:block absolute top-[51%] left-[74%]">
            <div
              className="w-[315px] h-[100px] border-dashed border-t-2 border-r-2 border-gray-700 absolute"
              style={{
                top: "50%",
                left: "50%",
                transform: "translate(-50%, -50%)",
              }}
            />
          </div>

          {/* Dotted lines for Mobile Screens */}
          <div className="block md:hidden absolute top-[25%] left-[97%]">
            <div
              className=" w-[20px] h-[215px] border-dashed border-t-2 border-r-2 border-b-2 border-gray-700 absolute"
              style={{
                top: "50%",
                left: "50%",
                transform: "translate(-50%, -50%)",
              }}
            />
          </div>
          {/* Dotted lines for Mobile Screens */}
          <div className="block md:hidden absolute top-[55%] right-[98%]">
            <div
              className=" w-[25px] h-[215px] border-dashed border-t-2 border-l-2 border-b-2 border-gray-700 absolute"
              style={{
                top: "50%",
                left: "50%",
                transform: "translate(-50%, -50%)",
              }}
            />
          </div>

          {/* Dotted lines for Mobile Screens */}
          <div className="block md:hidden absolute top-[77%] left-[98%]">
            <div
              className=" w-[25px] h-[215px] border-dashed border-t-2 border-r-2 border-b-2 border-gray-700 absolute"
              style={{
                top: "50%",
                left: "50%",
                transform: "translate(-50%, -50%)",
              }}
            />
          </div>

          <div className="w-auto md:w-[75%] flex flex-col md:flex-row justify-between items-center gap-8 mt-20 ml-0 md:ml-[25%]">
            <div className="flex flex-row md:flex-col justify-center items-center">
              <ServiceCount count={"3"} />
              <ServiceLifecycleCard title="Deploy" items={deploy} />
            </div>
            <div className="flex flex-row-reverse md:flex-col justify-center items-center">
              <ServiceCount count={"4"} />
              <ServiceLifecycleCard
                title="Support and Maintenance"
                items={supportAndMaintenance}
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Section6;
