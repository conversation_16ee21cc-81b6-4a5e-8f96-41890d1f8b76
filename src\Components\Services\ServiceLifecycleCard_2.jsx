const ServiceLifecycleCard = ({ title, items = [] }) => {
  return (
    <div className="block max-w-sm p-6 bg-white border border-gray-200 rounded-lg shadow ">
      <h5 className="mb-1 md:mb-2 text-sm md:text-base font-bold tracking-tight text-gray-900 ">
        {title}
      </h5>
      <ul className="list-disc list-inside text-gray-700 text-xs md:text-sm mt-1 md:mt-2 space-y-1">
        {items.map((item, index) => (
          <li key={index} dangerouslySetInnerHTML={{
            __html: item,
          }}></li>
        ))}
      </ul>
    </div>
  );
};

export default ServiceLifecycleCard;
