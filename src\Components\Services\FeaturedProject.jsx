import React from "react";
import PortfolioCard from "../Card/PortfolioCards";

const FeaturedProject = () => {
  return (
    <div className="w-[90%] mx-auto my-24">
      <h2 className="text-center text-4xl font-semibold">
        Our Featured <span className="text-[#7716BC]">Project</span>
      </h2>
      <p className="text-center text-xl font-normal leading-7 my-10">
        Get to know us more by checking our portfolio and learning more about
        Job Worth, a customized software meticulously developed for employers
        and job seekers to find a perfect match. From ideation to deployment and
        designing to testing, we specialize in everything.
      </p>
      <div className="flex  justify-center items-center gap-10">
        <PortfolioCard
          imageSrc={"/Images/somatic.png"}
          title={"Somatic Fitness App"}
          description={
            "Experience the ultimate fitness journey with the Somatic Fitness Corporate app, a cutting-edge mobile solution designed exclusively for our client's …"
          }
        />
        <PortfolioCard
          imageSrc={"/Images/somatic.png"}
          title={"Somatic Fitness App"}
          description={
            "Cutting Edge Painting LLC stands out in the industry with our collaborative success, marked by the development of Paint Ready, a custom software …"
          }
        />
        <PortfolioCard
          imageSrc={"/Images/somatic.png"}
          title={"Somatic Fitness App"}
          description={
            "Experience the ultimate fitness journey with the Somatic Fitness Corporate app, a cutting-edge mobile solution designed exclusively for our client's …"
          }
        />
      </div>
    </div>
  );
};

export default FeaturedProject;
