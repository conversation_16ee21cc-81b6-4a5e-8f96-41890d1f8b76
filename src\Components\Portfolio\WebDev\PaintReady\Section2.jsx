import React from "react";
import Image from "next/image";

const Section2 = ({ heading, paragraph1, paragraph2, image, rowReverse }) => {
  return (
    <div className="w-[90%] mx-auto mb-10 md:mb-24 my-12">
      <div className={`flex flex-col ${rowReverse? rowReverse : "md:flex-row"}  justify-between gap-8`}>
        <div className="md:w-[60%]">
          <h1 className="text-2xl md:text-3xl font-semibold mb-3">{heading}</h1>
          <p className="text-base md:text-lg text-justify">{paragraph1}</p>
          <p className="text-base md:text-lg text-justify">{paragraph2}</p>
        </div>
        <div className="md:w-[40%] min-h-[250px] md:min-h-0 w-full relative">
          <Image
            src={image}
            alt="AI"
            layout="fill" // Use layout="fill" to take up the entire space of the div
            objectFit="cover" // Ensures the image covers the entire space
          />
        </div>
      </div>
    </div>
  );
};

export default Section2;
