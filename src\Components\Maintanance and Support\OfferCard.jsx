import Image from "next/image";
import React from "react";

const Card = ({ title, description, imgSrc, imgAlt, points }) => {
  return (
    <div className="max-w-lg h-fit border border-pink-500 rounded-md p-2 md:p-4">
      <div >
        <div className="flex items-start flex-shrink-0 gap-2">
        <Image src={imgSrc} alt={imgAlt} width={32} height={32} className="flex-shrink-0" />
        <p className="font-bold text-sm md:text-lg">
          {title}
          
          </p>
        </div>
        <p
        className="text-justify md:ml-8"
        dangerouslySetInnerHTML={{ __html: description }}
      />
      </div>
      <div className="py-5 flex flex-col gap-3 md:gap-5">
        {points.map((point, index) => (
          <div className="flex items-start gap-4" key={index}>
                <div className="flex-shrink-0 w-4 md:w-5 h-4 mt-1 md:h-5 bg-[#F245A1]"></div>
                <p className="text-sm md:text-base">{point}</p>
              </div>
        ))}
      </div>
    </div>
  );
};

export default Card;
