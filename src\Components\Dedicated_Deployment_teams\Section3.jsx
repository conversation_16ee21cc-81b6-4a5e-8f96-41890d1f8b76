import React from "react";

const Section3 = () => {
  return (
    <section className="bg-pink-100 mb-10 md:mb-24 p-4 md:p-8">
      <div className="w-[90%] mx-auto flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6">
        <div className="flex-1">
          <h2 className="text-base md:text-3xl md:leading-[40px] font-semibold">
            Solutions for Secure, Scalable
            <span className="text-[#7716BC]"> Development Teams</span>
          </h2>
          <p className="text-base md:text-xl capitalize mt-4 md:mt-8">
            It's not always possible to expand and develop effectively with traditional in-house personnel. The answer? Dedicated development teams.  
          </p>
        </div>
        <div className="flex-1">
          <section className="w-full  mx-auto p-8 border border-purple-800 rounded-2xl shadow-md">
            <h3 className="font-semibold text-base md:text-2xl ">
              With a team of dedicated software developers at Valueans, you can
              get:
            </h3>
            <div className="py-5 flex flex-col gap-3 text-sm md:text-lg  md:gap-5">
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 mt-2 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className=" ">
                  Thorough knowledge in your field because our talent is skilled
                  in over 30 sectors.
                </p>
              </div>

              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 mt-2 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className="">
                  Easy team scalability (from 0.5 FTE to 150+ FTE) allows for
                  flexible collaboration.
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 mt-2 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className="">
                  Strong team-assembling procedures allow for a quick ramp-up of
                  the outsourced team (2 days to 2 weeks).
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 mt-2 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className="">
                  Due to advanced project management and software engineering
                  techniques, development speeds can increase by up to 40%.
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 mt-2 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className="">
                  Ownership of the entire product, including backlog control.
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 mt-2 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className="">
                  The SDLC, priorities and strategy, and overall product
                  roadmap.
                </p>
              </div>
            </div>
          </section>
        </div>
      </div>
    </section>
  );
};

export default Section3;
