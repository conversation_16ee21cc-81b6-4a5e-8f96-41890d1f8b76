import React from "react";
import Image from "next/image";

const Section1 = () => {
  return (
    <section className="mb-10 md:mb-24 flex flex-col justify-center">
      <div className="flex flex-col-reverse md:flex-row justify-center md:justify-between items-center gap-5">
        <h2 className="text-[28px] md:text-[62px]  md:leading-[75px] text-white md:text-black font-semibold md:ml-10 z-10">
          <span className="text-[#F245A1]">Data Engineering</span> <br />{" "}
          Solutions
        </h2>

        {/* Container for Images */}
        <div className="relative w-screen h-[60vh] md:w-[600px] md:h-[600px] right-0">
          {/* Purple Frame */}
          <div className="absolute inset-0 z-0">
            <Image
              src="/Images/purple_frame.png"
              alt="purple-block"
              layout="fill"
              objectFit="cover"
            />
          </div>

          {/* Cloud Services Image Centered Inside Purple Image */}
          <div className="absolute inset-0 flex items-center justify-center z-10">
            <Image
              src="/Images/Data_Engineering_Frame.png"
              alt="Data Engineering"
              width={400}
              height={400}
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default Section1;
