"use client";
import React, { useState } from "react";

const services = [
  {
    title: "Software development",
    content: "Software development content goes here...",
  },
  { title: "IT consulting", content: "IT consulting content goes here..." },
  {
    title: "Application services",
    content: "Application services content goes here...",
  },
  { title: "Testing & QA", content: "Testing & QA content goes here..." },
  { title: "Data analytics", content: "Data analytics content goes here..." },
  {
    title: "Help desk services",
    content: "Help desk services content goes here...",
  },
  {
    title: "Infrastructure services",
    content: "Infrastructure services content goes here...",
  },
  {
    title: "Cybersecurity services",
    content: "Cybersecurity services content goes here...",
  },
];

const ResponsiveAccordionPage = () => {
  const [selectedServiceIndex, setSelectedServiceIndex] = useState(0);
  const [mobileAccordionIndex, setMobileAccordionIndex] = useState(null);

  const toggleAccordion = (index) => {
    setMobileAccordionIndex(mobileAccordionIndex === index ? null : index);
  };

  return (
    <div className="flex flex-col md:flex-row w-full h-screen bg-gray-50">
      {/* Sidebar */}
      <div className="w-full md:w-1/4 bg-white shadow-md">
        <h2 className="font-bold text-lg text-center p-4 bg-blue-100">
          Services
        </h2>
        <ul className="p-4">
          {services.map((service, index) => (
            <li key={index} className="border-b mb-2">
              {/* Heading */}
              <div
                onClick={() => {
                  setSelectedServiceIndex(index); // For desktop
                  toggleAccordion(index); // For mobile
                }}
                className={`cursor-pointer p-2 hover:bg-blue-100 transition-all ${
                  selectedServiceIndex === index
                    ? "text-blue-500 font-semibold"
                    : ""
                }`}
              >
                {service.title}
              </div>

              {/* Accordion Content on Mobile */}
              <div
                className={`md:hidden p-2 text-gray-600 ${
                  mobileAccordionIndex === index ? "block" : "hidden"
                }`}
              >
                {service.content}
              </div>
            </li>
          ))}
        </ul>
      </div>

      {/* Content Area for Desktop */}
      <div className="hidden md:flex-1 md:block bg-white p-6 shadow-inner">
        <h2 className="text-2xl font-bold mb-4">
          {services[selectedServiceIndex].title}
        </h2>
        <p className="text-gray-700">
          {services[selectedServiceIndex].content}
        </p>
      </div>
    </div>
  );
};

export default ResponsiveAccordionPage;
