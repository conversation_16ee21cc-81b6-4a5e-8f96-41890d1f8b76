import React from "react";
import Image from "next/image";

const InfoCard = ({ title, description }) => {
  return (
   <div className="block w-full md:max-w-xl border border-pink-500  p-2 md:p-4 rounded-2xl shadow">
        <div className="w-full  flex items-start gap-1 mb-4">
          <div className="flex-shrink-0 w-6 h-6 sm:w-8 sm:h-8">
            <Image
              src="/Images/service_frame.png"
              alt="Tick"
              width={32}
              height={32}
              className="w-full h-full flex-shrink-0"
            />
          </div>
          <div className="w-[100%]">
            <h3 className="text-base md:text-lg font-semibold">{title}</h3>
            <p className="text-sm md:text-base text-justify">
              {description}
            </p>
          </div>
        </div>
      </div>
  );
};

const Section6 = () => {
  return (
    <section className="bg-blue-100 mb-10 md:mb-24 py-4 md:py-10">
      <div className="w-[85%] mx-auto ">
        <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
          Why Choose <span className="text-[#F245A1]">Valueans</span> for
          Software Development in Healthcare
        </h2>
        <div className=" flex flex-col justify-center items-center gap-3 md:gap-6 my-3 md:my-6 ">
          <div className="flex flex-col md:flex-row justify-center md:justify-between items-center gap-3 md:gap-6">
            <InfoCard
              title={"Simplify Workflows in Healthcare"}
              description={
                "To expedite and improve your operations, our development and project management teams will ascertain your precise needs and provide solutions."
              }
            />
            <InfoCard
              title={"Personalize Patient Experience"}
              description={
                "Through immersive medical apps, OSP may assist doctors in providing better health care and guaranteeing that each patient receives individualized attention."
              }
            />
          </div>
          <div className="flex flex-col md:flex-row justify-center md:justify-between  items-center gap-3 md:gap-6">
            <InfoCard 
              title={"Solutions Compliant with HIPAA"}
              description={
                "To make sure that all of our apps adhere to HIPAA requirements, we thoroughly test them against predetermined criteria."
              }
            />
            <InfoCard
              title={"Team of Health Tech Experts"}
              description={
                "OSP employs a group of seasoned healthcare technologists who use their enthusiasm to create innovative solutions to tackle problems in the medical sector."
              }
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default Section6;
