import React from "react";
import ServiceCard from "../Services/ServiceDevCard";

const WhyChoose = () => {
  return (
    <div className="w-[80%] mx-auto my-24">
      <h2 className="text-center text-4xl font-semibold my-10">
        Why choose Valueans for{" "}
        <span className="text-[#F245A1]">Fintech software development?</span>{" "}
      </h2>
      <div className="flex justify-center items-center my-4 gap-8">
        <ServiceCard
          title={"Innovative Financial Solutions"}
          imageSrc={"/Images/tailor.png"}
          altText={"tailor"}
          description={
            "From effortless payment systems to trusted investment platforms Valueans builds products that redefine Fintech. Our unique solutions introduce innovation and creativity into your business making daily transactions and payments easier than ever."
          }
        />
        <ServiceCard
          title={"Custom Development"}
          imageSrc={"/Images/tailor.png"}
          altText={"tailor"}
          description={
            "No two businesses are alike, and neither are our solutions, which is why Valuens offers you meticulously designed solutions that perfectly align with your vision and goals. From payment processes to mobile banking and stock trading, all our services are designed and developed carefully considering your needs."
          }
        />
        <ServiceCard
          title={"Advanced Security Measures"}
          imageSrc={"/Images/tailor.png"}
          altText={"tailor"}
          description={
            "At Valueans, we prioritize your security as our development services include the highest standards of cyber security. We make sure your data and transactions are protected against all threats."
          }
        />
      </div>
      <div className="flex justify-center items-center mt-4 gap-8">
        <ServiceCard
          title={"User-Centric Designs"}
          imageSrc={"/Images/tailor.png"}
          altText={"tailor"}
          description={
            "Maintaining aesthetics and functionality in our product to enhance user experience and loyalty. We create attractive and engaging interfaces that are easy to use and interact with."
          }
        />
        <ServiceCard
          title={"Customization"}
          imageSrc={"/Images/tailor.png"}
          altText={"tailor"}
          description={
            "We provide the perfect blend of customization and creativity. While we understand the importance of personalization, we also acknowledge the need to stay up-to-date and modern. We showcase our talents at each step whether it is development, design, or management while keeping you in the loop. We use the latest frameworks, coding techniques, and automation to bring a perfect Web App for you."
          }
        />
        <ServiceCard
          title={"Customization"}
          imageSrc={"/Images/tailor.png"}
          altText={"tailor"}
          description={
            "We provide the perfect blend of customization and creativity. While we understand the importance of personalization, we also acknowledge the need to stay up-to-date and modern. We showcase our talents at each step whether it is development, design, or management while keeping you in the loop. We use the latest frameworks, coding techniques, and automation to bring a perfect Web App for you."
          }
        />
      </div>
    </div>
  );
};

export default WhyChoose;
