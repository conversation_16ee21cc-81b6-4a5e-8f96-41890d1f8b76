"use client";
import Image from "next/image";
import React from "react";

const Technologies = ({ onNavigate }) => {
  return (
    <div className="md:container md:mx-auto md:p-10 md:border md:rounded-lg">
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        {/* Image Section */}
        <div className="col-span-1 md:col-span-2">
          <Image className="hidden md:block" src={"/Images/tech-nav.jpeg"} width={400} height={400} alt="Technologies" />
        </div>

        {/* Links Section */}
        <div className="col-span-1 md:col-span-3 space-y-4 md:space-y-0 md:flex md:justify-between">
          {/* Column 1 */}
          <div className="flex flex-col gap-3 md:w-[30%]">
            <button onClick={() => onNavigate("/Technologies/Progressive_Web_Apps")} className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Progressive Web Apps
            </button>
            <button onClick={() => onNavigate("/Technologies/NLP")} className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              NLP
            </button>
            <button onClick={() => onNavigate("/Technologies/Low_Code_Development")} className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Low Code Deployment
            </button>
          </div>

          {/* Column 2 */}
          <div className="flex flex-col gap-3 md:w-[30%]">
            <button onClick={() => onNavigate("/Technologies/AR_VR")} className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              AR/VR
            </button>
            <button onClick={() => onNavigate("/Technologies/MicroServices")} className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Microservices
            </button>
            <button onClick={() => onNavigate("/Technologies/Predictive_Analysis")} className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Predictive Analytics
            </button>
          </div>

          {/* Column 3 */}
          <div className="flex flex-col gap-3 md:w-[30%]">
            <button onClick={() => onNavigate("/Technologies/IOT")} className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              IoT
            </button>
            <button onClick={() => onNavigate("/Technologies/AI_ML")} className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              AI/ML
            </button>
            <button onClick={() => onNavigate("/Technologies/Cross_Platform_And_Hybrid_Development")} className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Cross-platform and Hybrid Development
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Technologies;
