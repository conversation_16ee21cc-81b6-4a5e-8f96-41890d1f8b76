// "use client";

// import * as React from "react";
// import Card from "./Card";
// import {
//   Carousel,
//   CarouselContent,
//   CarouselItem,
//   CarouselNext,
//   CarouselPrevious,
// } from "@/components/ui/carousel";

// const dummyData = [
//   { title: "First Card", description: "This is the first card's description." },
//   {
//     title: "Second Card",
//     description: "Here's the second card with some info.",
//   },
//   { title: "Third Card", description: "The third card brings more content." },
//   { title: "Fourth Card", description: "Card number four with dummy text." },
//   { title: "Fifth Card", description: "And here's the fifth and final card." },
// ];

// export function CardCarousel() {
//   return (
//     <Carousel
//       opts={{
//         align: "start",
//       }}
//       className="w-full max-w-sm md:max-w-2xl lg:max-w-4xl mx-auto"
//     >
//       <CarouselContent>
//         {dummyData.map((item, index) => (
//           <CarouselItem key={index} className="md:basis-1/2 lg:basis-1/3">
//             <div className="p-1">
//               <Card title={item.title} description={item.description} />
//             </div>
//           </CarouselItem>
//         ))}
//       </CarouselContent>
//       <CarouselPrevious />
//       <CarouselNext />
//     </Carousel>
//   );
// }
