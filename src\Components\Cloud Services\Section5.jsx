import React from "react";
import Card from "./Card_3";

const Section5 = () => {
  return (
    <div className="w-[85%] mx-auto my-10 md:my-24">
      <h2 className=" w-auto md:w-[80%] md:mx-auto text-xl md:text-3xl md:leading-[40px] text-center font-semibold capitalize mb-1 md:mb-3">
        What You Get with Valueans{" "}
        <span className="text-[#F245A1]">Hybrid Cloud Management</span> Services
      </h2>
      <p className="w-auto md:w-[80%] md:mx-auto text-lg md:text-xl text-center capitalize">
        Here’s how Valueans cloud managed network and cloud consulting services
        can help you drive business success:
      </p>

      <div className="flex flex-col md:flex-row justify-center md:justify-between items-center gap-3 md:gap-6 my-3 md:my-6">
        <div className="flex flex-col justify-center items-center gap-3 md:gap-6">
          <Card>
            Quick cloud installations that are tailored to your requirements and
            financial objectives.
          </Card>
          <Card>
            Improved effectiveness and performance that frees up resources to
            concentrate on generating company value instead of handling daily
            cloud complexity.
          </Card>
          <Card>
            Faster adoption of public clouds and cloud environment transition
            Optimized workloads that can expand and contract without requiring
            resource hiring or training.
          </Card>
        </div>
        <div className="flex flex-col justify-center  items-center gap-3 md:gap-6">
          <Card>
            A possible 25% or more increase in CSP spending, lower or
            reorganized expenses, and pay-as-you-go models that provide
            flexibility and scalability.
          </Card>
          <Card>Improved compliance and security features for comfort.</Card>
          <Card>
            Constant direction and assistance from planning to deployment to
            ongoing operations.
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Section5;
