import React from "react";

const PinkBgcard = ({ title, description }) => {
  return (
    <div className="bg-[#F245A126] p-4 rounded-lg shadow-md ">
      <h2 className="text-base md:text-lg font-semibold text-[#7716BC]">
        {title}
      </h2>

      {description && (
        <p
          className="text-sm md:text-base text-justify mt-2"
          dangerouslySetInnerHTML={{ __html: description }}
        />
      )}
    </div>
  );
};

export default PinkBgcard;
