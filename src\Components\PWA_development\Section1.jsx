import Image from "next/image";
import React from "react";
import Button from "../Buttons/Button";

const Section1 = ({backgroundImage, heading, bannerText}) => {
  return (
    <section
      className="relative h-[60vh] md:min-h-screen"
      style={{
        backgroundImage: `url(${backgroundImage})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
      }}
    >
      {/* Overlay div */}
      <div className="absolute inset-0 bg-[#F245A166] z-0"></div>

      {/* Content */}
      <div className="flex flex-col justify-center items-center gap-6 h-full relative z-10 ">
        <h1 className="font-titillium text-3xl md:mb-5 font-bold md:text-5xl text-white text-center">
          {heading}
        </h1>

        {/* Full width h3 */}
        <h3 className="w-[70%] text-center font-semibold font-titillium text-sm md:text-2xl py-3 md:py-4 text-white">
          {bannerText}
        </h3>
        <Button to={"/contact"} bgColor="bg-[#F245A1]" hoverColor="opacity-90">
          Connect With Us
        </Button>
      </div>
    </section>
  );
};

export default Section1;
