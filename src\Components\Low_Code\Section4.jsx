const Card = ({ title, description }) => {
  return (
    <div className="max-w-sm relative ">
      <div className="bg-pink-100 p-4 rounded-t-md absolute top-[-20px] w-full z-10 shadow-md text-center">
        <h3 className="text-base md:text-lg font-semibold">{title}</h3>
      </div>
      <div className="w-full border border-purple-500 p-6 rounded-md shadow-sm pt-20 h-auto md:h-[33vh]">
        <p className="text-sm md:text-base">{description}</p>
      </div>
    </div>
  );
};

const cardData = [
  {
    title: "Rapid Productivity",
    description:
      "We transform the process of creating applications by providing prebuilt connections and visual, model-driven development. Simplify Integration and Streamline Your Development.",
  },
  {
    title: "Speed & Code",
    description:
      "With a visual IDE for user interface, business processes, logic, and data models, Valueans speeds up development. Change management is automated for quick and safe deployments.",
  },
  {
    title: "Development of Multiple Pipelines",
    description:
      "Cross-platform programming is supported by our Low Code Automation without sacrificing native responsiveness. Create high-quality apps only once, then quickly and affordably distribute them across a range of hardware and operating systems.",
  },
  {
    title: "Open-Source Platform",
    description:
      "Standards-based, flexible application development is made possible by our Low Code Automation. Apps are compiled and delivered in server settings using visual development languages.",
  },
  {
    title: "Enterprise-Grade Platform",
    description:
      "Valueans Low Code Website Builder is a reliable platform for businesses looking to create and oversee installations with several services and applications. It provides a flexible setting, strong governance, and support for rapid expansion.",
  },
  {
    title: "Lower Expenses",
    description:
      "Continuous deployment is made possible by low code automation software development services and solutions that handle complicated lifecycles. The manual administration and operation of mission-critical apps is reduced with error-free applications and high-level security governance.",
  },
];



const Section4 = () => {
  return (
    <div className="mx-[75px] mb-10 md:mb-24">
      <h2 className="text-xl md:text-[28px] text-center font-semibold mb-[42px]">
        <span className="text-[#7716BC]">Low Code</span> Process Automation
        Solutions at Valueans 
      </h2>
      <div className="max-w-fit mx-auto grid md:grid-cols-2 lg:grid-cols-3 gap-8 mt-6 md:mt-[0px]">
        {cardData.map((card, index) => (
          <Card key={index} title={card.title} description={card.description} />
        ))}
      </div>
    </div>
  );
};

export default Section4;
