import React from "react";

const ServiceCard = ({ title, description, bgColor = "bg-white" }) => {
  return (
    <>
      <div
        className={`w-auto h-auto md:h-[45vh]  md:w-[15vw] block overflow-hidden  max-w-sm p-3 md:p-6 pb-4 md:pb-7 border border-gray-200 rounded-lg shadow-lg ${bgColor}`}
      >
        <h5 className="mb-1 md:mb-2 text-lg md:text-2xl font-bold tracking-tight text-[#7716BC]">
          {title}
        </h5>
        <p className="text-sm md:text-base font-normal text-[#232222]">
          {description}
        </p>
      </div>
    </>
  );
};

export default ServiceCard;
