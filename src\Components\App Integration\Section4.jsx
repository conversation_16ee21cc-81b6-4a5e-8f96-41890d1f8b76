import React from "react";
import Card from "./Card";
const Section4 = () => {
  const cardData = [
    {
      title: "Software Integration Services",
      description:
        "Use our <a href='/saas-app-development' class='text-[#7716BC] hover:underline'>SaaS application</a> integration services to integrate databases, business software, and business intelligence infrastructure smoothly to boost your company's growth and ensure quicker and more efficient operations.",
    },
    {
      title: "Business SaaS Application Integrations",
      description:
        "Enable smooth communication across mission-critical platforms, such as accounting, enterprise resource planning, customer relationship management, content management systems, and business intelligence systems.",
    },
    {
      title: "API Integration Services",
      description:
        "Use specially designed APIs to synchronize data formats between apps, add web service functionality to apps, and connect disparate systems and processes. It helps enable your teams to deliver web APIs securely.",
    },
    {
      title: "Service-Oriented Architecture",
      description:
        "We create Service-Oriented Architectures (SOA) that are designed for web service interoperability, quick data retrieval, and integrated system reusability. With our SOA, we deliver functional, scalable software systems that derive from individual components.",
    },
    {
      title: "Application Management Services (AMS)",
      description:
        "We offer high-quality application lifecycle management that can swiftly expand to accommodate contemporary operations. Our Application Management Services (AMS) will assist you in monitoring, optimization, improvement, and support to keep your apps running efficiently.",
    },
    {
      title: "Process Automation",
      description:
        "With our <a href='/Cloud_Services' class='text-[#7716BC] hover:underline'>cloud app integration</a> services, you can swiftly automate approval procedures across your ERP applications. Use low-code designers, prebuilt connectors, and reusable business rules to streamline tedious procedures.",
    },
  ];

  return (
    <section className="w-[85%] mx-auto my-10 md:my-24">
      <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold capitalize">
        Our{" "}
        <span className="text-[#F245A1]">
          application integration  solutions
        </span>{" "}
        help you improve  Business workflows
      </h2>
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 place-items-center">
          {cardData.map((item, index) => (
            <Card
              headingFont={"text-lg md:text-xl text-[#7716BC]"}
              height={"h-auto md:h-[200px]"}
              key={index}
              title={item.title}
              description={item.description}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default Section4;
