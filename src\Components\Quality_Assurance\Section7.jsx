import React from "react";
import Card from "@/Components/App Integration/Card";

const Section7 = () => {
  // Array of card data
  const cards = [
    {
      title: "E-commerce and Retail",
      description:
        "Our staff assists you in improving your supply chain management as well as the customer journey optimization, from the moment they interact with your goods to the point at which they take the desired action.",
    },
    {
      title: "Financial Services",
      description:
        "We support the seamless operation of the connected systems, the complete functioning of your  <a href='/financial-app-development' class='text-[#7716BC] hover:underline'>financial app or system</a> for all potential users, and the complete protection of their data.",
    },
    {
      title: "Software & Hi-Tech",
      description:
        "Manual software testing services are essential to releasing a fiercely competitive product that satisfies user requests while meeting the high levels of quality that the market requires today.",
    },
    {
      title: "Autonomous Driving & Logistics",
      description:
        "With Valueans’ experience in manual testing, <a href='/Industries/Travel' class='text-[#7716BC] hover:underline'> transport management services</a>, vessel scheduling, tracking systems, and cost management may all be made even more efficient and intuitive. We constantly work on next generation solutions such as <a href='/Industries/Automotive' class='text-[#7716BC] hover:underline'> autonomous driving</a>, trying to create safe and smart transport solutions.",
    },
    {
      title: "Entertainment & Media",
      description:
        "Before releasing your product, use functional, usability, or performance testing to address any issues. Consequently, you will be able to promote an excellent product that will get remarkable results.",
    },
    {
      title: "Medical & Life Sciences",
      description:
        "Benefit from our software testing and elevate your brand with cutting-edge functionality, visually appealing user interface elements, loyalty plans, and tailored suggestions.",
    },
    {
      title: "Social Media",
      description:
        "Using the newest tools and methods, improve the quality of social media forums, blogs, image-sharing websites, social networks, and customer review sites.",
    },
  ];

  return (
    <div className="w-[85%] mx-auto mb-10 md:mb-24">
      <h2 className="text-2xl md:text-[38px] md:leading-[57px] text-center font-semibold">
        <span className="text-[#F245A1]">Valueans</span> Covers a Wide Range of
        Industries
      </h2>

      <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-6">
        {cards.map((card, index) => (
          <Card
            key={index}
            title={card.title}
            description={card.description}
            border="border-purple-500"
          />
        ))}
      </div>
    </div>
  );
};

export default Section7;
