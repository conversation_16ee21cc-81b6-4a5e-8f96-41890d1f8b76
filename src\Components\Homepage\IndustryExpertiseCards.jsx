import Image from "next/image";


const IndustryExpertiseCard = ({ imageSrc, altText, title, description }) => {
  return (
    <div className="relative w-[320px] h-[450px] mx-auto  overflow-hidden">
      <div className="relative w-full h-[200px]">
        <Image
          src={imageSrc}
          alt={altText}
          layout="fill" // Ensures the image fills the parent container
          objectFit="cover" // Maintains aspect ratio and fills the container
        />
      </div>
      <div className="absolute top-[89px] left-[16px] max-w-[calc(100%-32px)] p-4 bg-white rounded-lg shadow">
        <h5 className="mb-1 text-lg md:text-xl font-bold tracking-tight text-gray-900">
          {title}
        </h5>
        <p className="font-normal text-sm md:text-base">{description}</p>
      </div>
    </div>
  );
};

export default IndustryExpertiseCard;
