import React from "react";
import Button from "../Buttons/Button";
import Image from "next/image";

const HomeP1 = () => {
  return (
    <>
      <section
        className="relative min-h-[60vh] md:min-h-[85vh] w-full px-4 md:px-20 py-16 md:py-0 bg-cover bg-center bg-no-repeat flex items-center"
        style={{
          backgroundImage: `url('/Images/HomePage-bg.png')`,
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      >
        <div className="container mx-auto">
          <div className="flex flex-col md:flex-row items-center justify-between gap-8 md:gap-12">
            {/* Left Content */}
            <div className="w-full md:w-1/2 text-center md:text-left space-y-6 md:space-y-8">
              <h1 className="text-white text-4xl md:text-6xl lg:text-7xl font-trirong leading-tight">
                From Idea To{" "}
                <span
                  className="relative 
                        inline-block 
                        text-[#F245A1] 
                        font-aclonica 
                        font-bold
                        after:content-[''] 
                        after:absolute 
                        after:left-0 
                        after:-bottom-1 
                        after:w-full 
                        after:h-2 
                        after:bg-[url('/Images/underline.png')] 
                        after:bg-contain 
                        after:bg-no-repeat
                        animate-fadeIn"
                >
                  Industry
                </span>{" "}
                Leader, Faster
              </h1>

              <div className="mt-8 md:mt-12">
                <Button
                  bgColor="bg-[#F245A1]"
                  hoverColor="hover:bg-[#d93b8c] transition-all duration-300"
                  paddingX="px-12 md:px-16"
                  paddingY="py-3 md:py-4"
                  to={"/contact"}
                >
                  Let&apos;s talk
                </Button>
              </div>
            </div>

            {/* Right Content */}
            <div className="w-full md:w-1/2 flex flex-col items-center md:items-end space-y-8">
              <div className="relative w-full hidden md:block max-w-[387px] animate-float">
                <Image
                  src="/Images/banner_image.svg"
                  alt="banner image"
                  width={387}
                  height={313}
                  className="w-full h-auto object-contain"
                  priority
                />
              </div>
              <div className="w-full md:max-w-[80%]">
                <p className="text-[#F4F5F6] text-lg md:text-xl leading-relaxed font-semibold text-center  md:border-l-2 border-[#F245A1] pl-4">
                  Launch your application 3X faster <br/>
                 and more cost-effectively, with our<br/>
                  revolutionary ReOps framework<br/>
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default HomeP1;
