"use client";
import React from "react";
import dynamic from "next/dynamic";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import Card from "./Card"; // Importing the Card component
import { FaArrowLeft, FaArrowRight } from "react-icons/fa";

const Slider = dynamic(() => import("react-slick"), {
  ssr: false,
  loading: () => <p>Loading...</p>,
});

const CustomPrevArrow = ({ onClick }) => (
  <button
    onClick={onClick}
    className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-purple-500 text-white p-1 md:p-3 rounded-full hover:bg-purple-600"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      className="h-5 w-5"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
      strokeWidth={2}
    >
      <path strokeLinecap="round" strokeLinejoin="round" d="M15 19l-7-7 7-7" />
    </svg>
  </button>
);

const CustomNextArrow = ({ onClick }) => (
  <button
    onClick={onClick}
    className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-purple-500 text-white p-1 md:p-3 rounded-full hover:bg-purple-600"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      className="h-5 w-5"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
      strokeWidth={2}
    >
      <path strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7" />
    </svg>
  </button>
);

const Cardcarousel = () => {
  const expertiseData = [
    {
      title: "Healthcare",
      description:
        "The healthcare industry demands precision, security, & efficiency to manage sensitive patient data & streamline operations.",
    },
    {
      title: "Logistics",
      description:
        "In logistics, efficiency and real-time tracking are crucial for smooth supply chain operations.",
    },
    {
      title: "Travel",
      description:
        "The travel industry thrives on seamless booking experiences and personalized customer solutions.",
    },
    {
      title: "Construction",
      description:
        "The construction industry benefits from innovative project management tools, ensuring seamless collaboration.",
    },
    {
      title: "Manufacturing",
      description:
        "Optimize production processes, improve quality controls, and refine supply chain visibility with advanced solutions.",
    },
  ];

  const sliderSettings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 3,
    slidesToScroll: 1,
    swipeToSlide: true,
    nextArrow: <CustomNextArrow />,
    prevArrow: <CustomPrevArrow />,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 600,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
    ],
  };

  return (
    <div className="my-12 px-4">
      <h2 className="text-2xl md:text-4xl text-center font-semibold mb-10">
        Our <span className="text-purple-600">Industry</span> Expertise
      </h2>
      <div className="relative w-full max-w-6xl mx-auto overflow-hidden">
        <Slider {...sliderSettings}>
          {expertiseData.map((item, index) => (
            <div key={index} className="flex justify-center items-center px-2">
              <Card title={item.title} description={item.description} />
            </div>
          ))}
        </Slider>
      </div>
    </div>
  );
};

export default Cardcarousel;
