"use client";

import Link from "next/link";
import React, { Suspense } from "react";
import { usePathname, useSearchParams } from "next/navigation";

// Client component that uses useSearchParams
const TabContent = () => {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const tab = searchParams.get("tab") || "web-app";

  return (
    <ul className="flex flex-wrap text-lg font-medium text-center gap-5 text-gray-500 dark:text-gray-200">
      <li className="me-2">
        <Link
          href="/Portfolio/web-app"
          className={`inline-block py-2 px-4 rounded-lg ${
            pathname === "/Portfolio/web-app" 
              ? "text-white bg-[#F245A1]"
              : "bg-gray-200 text-black hover:bg-gray-300"
          }`}
        >
          Web App
        </Link>
      </li>

      <li className="me-2">
        <Link
          href="/Portfolio/mobile-app"
          className={`inline-block py-2 px-4 rounded-lg ${
            pathname === "/Portfolio/mobile-app" || tab === "mobile-app"
              ? "text-white bg-[#F245A1]"
              : "bg-gray-200 text-black hover:bg-gray-300"
          }`}
        >
          Mobile App
        </Link>
      </li>

      <li className="me-2">
        <Link
          href="/Portfolio/ui-ux"
          className={`inline-block py-2 px-4 rounded-lg ${
            pathname === "/Portfolio/ui-ux" || tab === "ui-ux"
              ? "text-white bg-[#F245A1]"
              : "bg-gray-200 text-black hover:bg-gray-300"
          }`}
        >
          UI/UX
        </Link>
      </li>

      <li className="me-2">
        <Link
          href="/Portfolio/hybrid"
          className={`inline-block py-2 px-4 rounded-lg ${
            pathname === "/Portfolio/hybrid" || tab === "hybrid"
              ? "text-white bg-[#F245A1]"
              : "bg-gray-200 text-black hover:bg-gray-300"
          }`}
        >
          Hybrid
        </Link>
      </li>
    </ul>
  );
};

// Loading fallback for Suspense
const TabLoading = () => {
  return (
    <ul className="flex flex-wrap text-lg font-medium text-center gap-5 text-gray-500 dark:text-gray-200">
      <li className="me-2">
        <div className="inline-block py-2 px-4 rounded-lg bg-gray-200 text-black">
          Web App
        </div>
      </li>
      <li className="me-2">
        <div className="inline-block py-2 px-4 rounded-lg bg-gray-200 text-black">
          Mobile App
        </div>
      </li>
      <li className="me-2">
        <div className="inline-block py-2 px-4 rounded-lg bg-gray-200 text-black">
          UI/UX
        </div>
      </li>
      <li className="me-2">
        <div className="inline-block py-2 px-4 rounded-lg bg-gray-200 text-black">
          Hybrid
        </div>
      </li>
    </ul>
  );
};

// Main Tab component that wraps TabContent in Suspense
const Tab = () => {
  return (
    <Suspense fallback={<TabLoading />}>
      <TabContent />
    </Suspense>
  );
};

export default Tab;
