import React from "react";
import Card from "../Cloud Services/Card";

const features = [
  {
    title: "Years Of Expertise",
    description:
      "With years of experience in the software development and maintenance industry, Valueans has built a reputation for delivering dependable, scalable, and efficient solutions to clients across diverse sectors.",
  },
  {
    title: "Top-Notch Services",
    description:
      "We provide high <a href='/Quality_Assurance' class='text-[#7716BC] hover:underline'> quality assurance services</a> by paying close attention to every detail, ensuring your software is ready for a smooth launch. Our team makes sure everything works perfectly, giving you peace of mind so you can focus on growing your business.",
  },
  {
    title: "Increasing System Performance",
    description:
      "We know that all businesses are different, so we always offer the support packages which are according to your business and budget. We make sure that you get the help which you need.",
  },
  {
    title: "Flexible Solutions",
    description:
      "We designed your application to grow with your business. We make sure that they perform well no matter how much your business expands. We provide ongoing updates to run them smoothly for future use.",
  },
  {
    title: "Fast Delivery",
    description:
      "We focus on delivering quick results by using the latest tools and technology to fix any parts of your software that are not working efficiently. This helps us complete projects faster by ensuring everything works well and meets your standards.",
  },
  {
    title: "Expert And Dedicated Team",
    description:
      "We have <a href='/Dedicated_Deployment_teams' class='text-[#7716BC] hover:underline'> dedicated development teams</a> and testers who use the latest tools and technologies to make sure your software runs smoothly and efficiently, with their expertise, we ensure your software stays in top shape, with minimum disruptions and maximum performance.",
  },
];

const TopChoiceSection = () => {
  return (
    <div className="w-[85%] mx-auto my-10 md:my-24">
      <h2 className="text-2xl md:text-3xl md:leading-[57px] font-semibold text-center">
        Why is <span className="text-[#F245A1]">Valueans</span> the Top Choice
        for Software Maintenance Services?
      </h2>
      <p className="text-base md:text-xl text-center my-2 md:my-5">
       Businesses everywhere rely on our software maintenance and support to keep their apps running smoothly. It helps them work more efficiently and stay ahead of the competition. Here’s why Valueans could be the perfect partner for your software maintenance.
      </p>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 px-4 md:px-0">
        {features.map((feature, index) => (
          <Card
            key={index}
            title={feature.title}
            description={feature.description}
          />
        ))}
      </div>
    </div>
  );
};

export default TopChoiceSection;
