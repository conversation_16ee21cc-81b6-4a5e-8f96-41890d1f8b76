import React from 'react'

const Section6 = ({heading1, heading2, paragraph1,paragraph2, mainHeading}) => {
  return (
    <div className="w-[90%] mx-auto mb-10 md:my-24 mt-20">
      <div className="mb-4">
        <h1 className="text-xl md:text-3xl md:leading-[40px] font-semibold text-center">{mainHeading}</h1>
        <p className="text-base md:text-lg text-justify "></p>
      </div>
      <div className="bg-[#F245A126] p-8 flex flex-col md:flex-row md:justify-between gap-8">
        <div className="md:w-[40%] bg-[#794CEC26] p-4 rounded-md">
          <div className="p-2">
            <h1 className="text-xl md:text-3xl md:leading-[40px] font-semibold">
              {heading1}
            </h1>
            
            <p
        className="text-base md:text-lg text-justify "
        dangerouslySetInnerHTML={{ __html: paragraph1 }}
      />
          </div>
          
        </div>
        <div className="md:w-[40%] bg-[#F245A126] p-4 rounded-md">
          <div className="p-2">
            <h1 className="text-xl md:text-3xl md:leading-[40px] font-semibold">
              {heading2}
            </h1>
             <p
        className="text-base md:text-lg text-justify "
        dangerouslySetInnerHTML={{ __html: paragraph2 }}
      />
          </div>
          
        </div>
      </div>
    </div>
  )
}

export default Section6