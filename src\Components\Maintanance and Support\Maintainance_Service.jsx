import React from "react";
import MaintenanceCard from "./Maintainance_Card";

const MaintenanceServices = () => {
  const bgColors = {
    pink: "bg-pink-100",
    purple: "bg-purple-100",
  };
  const maintenanceData = [
    {
      title: "SAP Support Services",
      description:
        "Experienced SAP professionals help you manage critical tasks effortlessly. Here's how:",
      containerBg: bgColors.pink,
      items: [
        "We make sure that your SAP solutions are delivered on time to meet your needs.",
        "We keep an eye on our fixes and continuously improve your process to keep everything running.",
        "We help you to get the most out of your SAP system, making sure it adds real value to your business.",
      ],
    },
    {
      title: "Web Maintenance Services",
      description:
        "Keeping your website in top shape is vital for user engagement and SEO performance. Our services include:",
      containerBg: bgColors.purple,
      items: [
        "We regularly check for vulnerabilities and make sure your site is safe from cyber threats.",
        "We create routine backups and have restore plans in place to prevent any data loss.",
        "We optimize your website's speed, so visitors enjoy a fast, seamless experience.",
      ],
    },
    {
      title: "Mobile App Maintenance Services",
      description:
        "We help your apps stay updated, secure, and engaging, ensuring a seamless experience for users. Here’s what our application maintenance and support services include: ",
      containerBg: bgColors.pink,
      items: [
        "We identify and quickly resolve bugs unique to your apps, we help your app’s platform to keep it running smoothly.",
        "We make sure your app works perfectly with the latest iOS and Android updates.",
        "We listen to your users and enhance your app’s features to meet their needs and expectations.",
      ],
    },
    {
      title: "Content Management System CMS Support",
      description:
        "Valueans ensures your CMS runs efficiently, so you can focus on creating great content Here’s how we help:",
      containerBg: bgColors.purple,
      items: [
        "We regularly update your plugins and themes to ensure your site remains secure and functions smoothly.",
        "We optimize your website to load quickly and handle traffic efficiently, giving users a broad experience.",
        "When urgent problems arise with your CMS, we are ready to jump in and resolve them quickly to minimize downtime.",
      ],
    },
    {
      title: "Remote IT Support Services",
      description:
        "Our remote IT support ensures your business stays on track by offering:",
      containerBg: bgColors.pink,
      items: [
        "We provide immediate support to resolve any technical issues and get your systems back on track.",
        "We continuously monitor your systems to catch and address potential problems before they escalate.",
        "Our resolute team is around the clock to assist you and handle your IT challenges.",
      ],
    },
  ];
  return (
    <div className="grid grid-cols-1 gap-6">
      {maintenanceData.map((service, index) => (
        <MaintenanceCard
          key={index}
          title={service.title}
          description={service.description}
          items={service.items}
          containerBg={service.containerBg}
        />
      ))}
    </div>
  );
};

export default MaintenanceServices;
