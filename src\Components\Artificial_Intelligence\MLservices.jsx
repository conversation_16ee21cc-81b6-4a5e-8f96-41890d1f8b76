import React from "react";
import Dropdown from "../Services/Dropdown";

const MLservices = () => {
  return (
    <div className="my-24">
      <h2 className="text-[#232536] text-4xl text-center font-semibold my-10">
        Machine Learning Development Services
      </h2>
      <div className="flex justify-between items-center mb-10">
        <Dropdown
          title="Web Application Development"
          content="Our skilled developers are trained to craft feature-rich Web Applications that deliver valuable information and interactive services resulting in higher user engagement. We provide the latest technology by combining AI and DevOps which saves your time and money ensuring increased scalability, maintainability, and flexibility for your applications."
        />
        <Dropdown
          title="Mobile Application Development"
          content="At Valueans, we develop feature-packed futuristic mobile applications with user-friendly and sleek front ends. Our skillful Android developers make the user's experience 10x better by blending the latest technology, efficient development process, and custom mobile app specifications."
        />
      </div>
      <div className="flex justify-between items-center mb-10">
        <Dropdown
          title="Web Application Development"
          content="Our skilled developers are trained to craft feature-rich Web Applications that deliver valuable information and interactive services resulting in higher user engagement. We provide the latest technology by combining AI and DevOps which saves your time and money ensuring increased scalability, maintainability, and flexibility for your applications."
        />
        <Dropdown
          title="Mobile Application Development"
          content="At Valueans, we develop feature-packed futuristic mobile applications with user-friendly and sleek front ends. Our skillful Android developers make the user's experience 10x better by blending the latest technology, efficient development process, and custom mobile app specifications."
        />
      </div>
    </div>
  );
};

export default MLservices;
