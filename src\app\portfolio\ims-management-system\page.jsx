import React from 'react'
import Section1 from "@/Components/Portfolio/WebDev/PaintReady/Section1";
import Section2 from "@/Components/Portfolio/WebDev/PaintReady/Section2";
import Section4 from "@/Components/Portfolio/WebDev/PaintReady/Section4";
import Section5 from "@/Components/Portfolio/WebDev/PaintReady/Section5";
export const metadata = {
  title: "Smart Inventory Management Software | Valueans",
  description: "A scalable inventory management solution with real-time analytics and intuitive dashboards. It helps businesses avoid stockouts, reduce overstocking, and improve sales performance.",
};

const page = () => {
 const images = ["/Images/IMS2.png", "/Images/IMS3.png", "/Images/IMS4.png"];
  return (
    <div>
      <Section1 backgroundImage={"/Images/IMS-bg.png"} />
      <Section2
        image={"/Images/IMS1.png"}
        heading={"IMS Management System "}
        paragraph1={
          "A well-known company, “IMS” approached us to provide them with a solution that can help them manage their inventory. With the large volume of sales and a variety of product variations, it has become a big challenge for companies to keep their inventory in stock all the time while not spending a lot on overstocking."
        }
        paragraph2={
          "We used the latest technology stacks to make this product user-friendly without any complex navigations, so the people using it can easily perform different processes. We developed its frontend using React technology, whereas we used Django for the backend development."
        }
      />
      <Section4
        paragraph1={
          "We also created a dashboard where they can check the analytics to understand different important metrics. This data helps them to make informed decisions about inventory levels so they can overcome the problem of going out of stock or overstocking."
        }
        paragraph2={
          "The IMS Management System has helped the company to increase efficiency specifically related to managing their inventory, which has resulted in increasing their sales and profits."
        }
      />
      <Section5 images={images} />
    </div>
  )
}

export default page