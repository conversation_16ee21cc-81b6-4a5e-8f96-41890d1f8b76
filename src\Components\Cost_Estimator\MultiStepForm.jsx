"use client";

import React, { useState, useRef } from "react";
import Button from "../Buttons/Button";
import Image from "next/image";
import CostEstimatorpoup from "../CostEstimatorpoup";
// List of industries based on the codebase
const industries = [
  { id: 1, name: "Travel/Hospitality" },
  { id: 2, name: "Healthcare" },
  { id: 3, name: "Fintech" },
  { id: 4, name: "E-commerce" },
  { id: 5, name: "Education/E-Learning" },
  { id: 6, name: "Real Estate" },
  { id: 7, name: "Social Networking" },
  { id: 8, name: "Gaming" },
  { id: 9, name: "Entertainment" },
  { id: 10, name: "Telecom" },
  { id: 11, name: "Construction" },
  { id: 12, name: "Manufacturing" },
  { id: 13, name: "Agriculture" },
  { id: 14, name: "Oil and Gas" },
  { id: 15, name: "Logistics" },
  { id: 16, name: "Banking" },
  { id: 17, name: "Automotive" },
  { id: 18, name: "Insurance" },
];

const MultiStepForm = ({ isVisible }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [showOtherIndustry, setShowOtherIndustry] = useState(false);
  const [showValidationMessage, setShowValidationMessage] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [costRange, setCostRange] = useState(["", ""]);
  const [formData, setFormData] = useState({
    projectType: [],
    platform: "",
    industry: "",
    otherIndustry: "",
    appType: "",
    otherAppType: "",
    developmentStage: "",
    complexityLevel: "",
    designPolish: "",
    designFiles: "",
    uploadedFiles: [],
    additionalInfo: "",
  });

  // Reference to file input
  const fileInputRef = useRef(null);

  // State to track if "Other" option is selected for app type
  const [showOtherAppType, setShowOtherAppType] = useState(false);

  // Reusable Option Card Component
  const OptionCard = ({
    option,
    isSelected,
    onClick,
    imageSrc,
    description,
    className = "",
    layout = "horizontal" // "horizontal" or "vertical"
  }) => (
    <div
      onClick={onClick}
      className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
        isSelected
          ? "border-[#F245A1] bg-pink-50"
          : "border-gray-200 hover:border-gray-300"
      } ${className}`}
    >
      {layout === "vertical" ? (
        <div className="flex flex-col items-center text-center">
          {imageSrc && (
            <div className="relative w-24 h-24 mb-4">
              <Image
                src={imageSrc}
                alt={option}
                layout="fill"
                objectFit="contain"
                className={isSelected ? "opacity-100" : "opacity-80"}
              />
            </div>
          )}
          <span className={`font-medium ${isSelected ? "text-[#F245A1]" : ""}`}>
            {option}
          </span>
          {description && (
            <p className="text-sm text-gray-600 mt-2">{description}</p>
          )}
        </div>
      ) : (
        <div className="flex items-center">
          {imageSrc && (
            <div className="relative w-10 h-10 mr-4 flex-shrink-0">
              <Image
                src={imageSrc}
                alt={option}
                layout="fill"
                objectFit="contain"
              />
            </div>
          )}
          <div className="flex-1">
            <span className={`font-medium ${isSelected ? "text-[#F245A1]" : ""}`}>
              {option}
            </span>
            {description && (
              <p className="text-sm text-gray-600 mt-1">{description}</p>
            )}
          </div>
        </div>
      )}
    </div>
  );

  // Handle multi-select options for step 1
  const handleProjectTypeChange = (option) => {
    if (formData.projectType.includes(option)) {
      setFormData(prev => ({
        ...prev,
        projectType: prev.projectType.filter((item) => item !== option),
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        projectType: [...prev.projectType, option],
      }));
    }
  };

  // Handle "Other" option selections
  const handleOtherOptionChange = (field, option, showOtherField, setShowOtherField) => {
    if (option === "Other") {
      setShowOtherField(!showOtherField);
      if (showOtherField) {
        setFormData(prev => ({
          ...prev,
          [field]: "",
          [`other${field.charAt(0).toUpperCase() + field.slice(1)}`]: "",
        }));
      }
    } else {
      setShowOtherField(false);
      setFormData(prev => ({
        ...prev,
        [field]: option,
        [`other${field.charAt(0).toUpperCase() + field.slice(1)}`]: "",
      }));
    }
  };

  // Handle input change for "Other" fields
  const handleOtherInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [`other${field.charAt(0).toUpperCase() + field.slice(1)}`]: value,
      [field]: value ? "Other" : "",
    }));
  };

  // Specific handlers using the generic functions
  const handleIndustryChange = (option) => {
    handleOtherOptionChange('industry', option, showOtherIndustry, setShowOtherIndustry);
  };

  const handleOtherIndustryChange = (e) => {
    handleOtherInputChange('industry', e.target.value);
  };

  const handleAppTypeChange = (option) => {
    handleOtherOptionChange('appType', option, showOtherAppType, setShowOtherAppType);
  };

  const handleOtherAppTypeChange = (e) => {
    handleOtherInputChange('appType', e.target.value);
  };

  // Handle additional information input
  const handleAdditionalInfoChange = (e) => {
    setFormData(prev => ({
      ...prev,
      additionalInfo: e.target.value,
    }));
  };

  // Handle file upload
  const handleFileUpload = (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
      setFormData({
        ...formData,
        uploadedFiles: [...formData.uploadedFiles, ...files],
      });
    }
  };

  // Handle file drop
  const handleFileDrop = (e) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      setFormData({
        ...formData,
        uploadedFiles: [...formData.uploadedFiles, ...files],
      });
    }
    setDragActive(false);
  };

  // Handle file removal
  const handleFileRemove = (index) => {
    const newFiles = [...formData.uploadedFiles];
    newFiles.splice(index, 1);
    setFormData({
      ...formData,
      uploadedFiles: newFiles,
    });
  };



  // State for drag and drop
  const [dragActive, setDragActive] = useState(false);

  // Get app type options based on selected industry
  const getAppTypeOptions = () => {
    // Default options if industry doesn't have specific options
    const defaultOptions = [
      "Mobile App",
      "Web Portal",
      "E-commerce Platform",
      "Content Management System",
      "Customer Relationship Management",
      "Enterprise Resource Planning",
      "Inventory Management",
      "Analytics Dashboard",
      "Booking System",
      "Payment Gateway Integration",
    ];

    // Industry-specific options
    const industryOptions = {
      "Travel/Hospitality": [
        "Hotel and Flight Booking Platform",
        "Local Tours and Activities Marketplace",
        "Travel Itinerary Planning Tool",
        "Ride-Sharing or Taxi Booking Platform",
        "Vacation Rental Management System",
        "Event Ticket Booking Portal",
        "Travel Insurance Enrollment Platform",
        "Group Trip Collaboration Tool",
        "Loyalty and Rewards Management Platform",
        "Adventure Travel Booking System",
      ],
      "Healthcare": [
        "Telemedicine Platform",
        "Health Tracking Solution",
        "Patient Engagement Portal",
        "Medical Billing System",
        "Electronic Health Records (EHR/EMR) Platform",
        "Remote Patient Monitoring Service",
        "Doctor Scheduling and Management Tool",
        "Clinical Trial Management System",
        "Pharmacy Ordering Platform",
        "Healthcare CRM",
      ],
      "E-commerce": [
        "Online Retail Storefront",
        "Multi-vendor Marketplace",
        "Subscription Commerce Platform",
        "B2B Wholesale Trading Portal",
        "Dropshipping Management System",
        "Rental Marketplace (Vehicles/Equipment)",
        "Grocery Delivery Solution",
        "Fashion Retail Platform",
        "Flash Sales Management Portal",
        "Vendor Control and CMS Platform",
      ],
      "Education/E-Learning": [
        "Online Course Marketplace",
        "Virtual Learning Environment (VLE)",
        "Learning Management System (LMS)",
        "Language Skill Development Platform",
        "Educational Game-Based Learning System",
        "Test Preparation Platform",
        "School Management Portal",
        "Corporate Training System",
        "Career Counseling Platform",
        "Edutainment (Education + Entertainment) Portal",
      ],
      "Fintech": [
        "Personal Finance Management Tool",
        "Mobile Banking Platform",
        "Cryptocurrency Wallet Service",
        "Payment Gateway System",
        "Stock/Investment Trading Platform",
        "Peer-to-Peer Lending Network",
        "Digital Insurance Management Platform",
        "Invoice and Billing Automation System",
        "Wealth Management Dashboard",
        "Buy Now Pay Later (BNPL) Solution",
      ],
      "Real Estate": [
        "Property Listing Portal",
        "Virtual Property Viewing Platform (AR/VR)",
        "Realtor CRM and Lead Management Tool",
        "Real Estate Buy/Sell Marketplace",
        "Rental Management Dashboard",
        "Tenant and Lease Tracking System",
        "Mortgage Application and Loan Platform",
        "Local Realtor Finder Solution",
        "Property Investment Portfolio Tracker",
        "Real Estate Bidding and Auction Platform",
      ],
      "Social Networking": [
        "Dating and Matchmaking Platform",
        "Professional Networking Portal",
        "Community Interest Group Platform",
        "Event Organization and Discovery Tool",
        "Private Corporate Social Network",
        "Fan Community Hub",
        "Content Sharing and Publishing Platform",
        "Audio-based Networking Solution (Voice Rooms)",
        "Visual-first Social Media Platform",
        "Social Content Aggregator",
      ],
      "Gaming": [
        "Casual Gaming Platform",
        "Real-time Multiplayer Gaming System",
        "Augmented Reality (AR) Gaming Solution",
        "Fantasy Sports League Management Platform",
        "Cloud Gaming Portal",
        "Social Gaming Community Hub",
        "In-app Tournament Management System",
        "Gamified Learning Platform",
        "Browser-Based Strategy Game",
        "NFT or Blockchain-integrated Gaming Platform",
      ],
     "Entertainment": [
        "Video Content Streaming Platform",
        "Music Streaming Service",
        "Podcast Discovery and Streaming Platform",
        "Live Event Broadcasting Portal",
        "Creator Subscription Management System",
        "Audiobook Listening Platform",
        "Short-form Video Content Hub",
        "Virtual Concert/Experience Platform",
        "Interactive Storytelling Solution",
        "Internet Radio Streaming System",
      ],
      "Telecom": [
        "Customer Self-Service Portal",
        "Virtual Property Viewing Platform (AR/VR)",
        "SIM Card Activation & Management System",
        "Telecom Billing & Invoicing Platform",
        "Field Technician Scheduling Tool",
        "Wholesale Carrier Management System",
        "IVR Management & Automation Platform",
        "Number Portability Service Interface",
        "Chatbot for Telecom Support",
        "Usage Analytics and Insights Platform",
      ],
      "Construction": [
        "Project Management and Scheduling Platform",
        "Contractor and Subcontractor Collaboration Tool",
        "Site Progress Tracking Dashboard",
        "Blueprint and Plan Sharing System",
        "Resource & Equipment Allocation Platform",
        "Digital Safety Compliance Tracker",
        "Estimation and Quotation Tool",
        "On-site Worker Attendance System",
        "Real-time Cost Monitoring Dashboard",
        "Construction CRM",
      ],
      "Agriculture": [
        "Smart Irrigation Management Platform",
        "Livestock Monitoring System",
        "Farm Management Software",
        "Agricultural Supply Chain Tracker",
        "Soil Health and Crop Analytics Dashboard",
        "Crop Disease Identification & Alert Tool",
        "Equipment Maintenance Scheduling Portal",
        "Farmer Cooperative Management System",
        "Harvest Planning and Forecasting Tool",
        "Agri-input Marketplace Platform",
      ],
      'Oil and Gas': [
        "Rig Operations Monitoring Dashboard",
        "Environmental Compliance Reporting Platform",
        "Pipeline Leak Detection System",
        "Worker Safety and Incident Reporting Tool",
        "Asset Tracking and Maintenance Platform",
        "Drilling Analytics Dashboard",
        "Permit and License Management Tool",
        "Fuel Distribution Scheduling System",
        "Energy Trading and Contracting Portal",
      ],
      'Logistics': [
        "Freight and Shipment Tracking System",
        "Fleet Management Dashboard",
        "Warehouse Operations Platform",
        "Route Optimization and Planning Tool",
        "3PL Collaboration Portal",
        "Parcel Delivery Status Platform",
        "Cross-border Customs Management System",
        "Load Bidding & Carrier Matching Tool",
        "Return and Reverse Logistics System",
        "Logistics CRM",
      ],
      'Banking': [
        "Digital Banking Portal",
        "Loan Origination System",
        "Customer Onboarding and KYC Platform",
        "Transaction Analytics and Reporting Tool",
        "Wealth and Portfolio Management System",
        "Internal Compliance and Audit Tracker",
        "Treasury Management Platform",
        "ATM and Branch Locator Interface",
        "Banking API Management Hub",
      ],
      'Automotive': [
        "Vehicle Diagnostics and Maintenance Tracker",
        "Car Rental and Booking Platform",
        "Dealership Management System",
        "Driver Behavior Monitoring Dashboard",
        "Fleet Leasing Platform",
        "Used Vehicle Marketplace",
        "EV Charging Locator and Booking Interface",
        "Insurance Claim Integration Platform",
        "Inventory Management for Auto Parts",
        "In-Car Infotainment Dashboard Manager",
      ],
      'Insurance': [
        "Policy Quotation and Purchase Portal",
        "Claims Management Platform",
        "Risk Assessment and Underwriting Dashboard",
        "Customer Self-Service Insurance Portal",
        "Broker and Agent Management System",
        "Document Upload and eSignature Tool",
        "Premium Payment Collection System",
        "Automated Renewal and Notification Engine",
        "Product Comparison and Recommendation Tool",
        "Fraud Detection & Investigation Module Today",
      ],
    };

    // Return industry-specific options if available, otherwise return default options
    const selectedIndustry =
      formData.industry === "Other"
        ? formData.otherIndustry
        : formData.industry;
    return industryOptions[selectedIndustry] || defaultOptions;
  };

  const nextStep = () => {
    // Check if the current step is valid before proceeding
    if (
      (currentStep === 1 && formData.projectType.length === 0) ||
      (currentStep === 2 && !formData.platform) ||
      (currentStep === 3 &&
        !formData.industry &&
        !(showOtherIndustry && formData.otherIndustry)) ||
      (currentStep === 4 &&
        !formData.appType &&
        !(showOtherAppType && formData.otherAppType)) ||
      (currentStep === 5 && !formData.developmentStage) ||
      (currentStep === 6 && !formData.complexityLevel) ||
      (currentStep === 7 && !formData.designPolish) ||
      (currentStep === 8 && !formData.designFiles) ||
      (currentStep === 8 &&
        formData.designFiles === "Upload" &&
        formData.uploadedFiles.length === 0)
      // Note: Step 9 (additional info) is optional, so no validation needed
    ) {
      // Show validation message if trying to proceed without selection
      setShowValidationMessage(true);
      // Hide the message after 3 seconds
      setTimeout(() => {
        setShowValidationMessage(false);
      }, 3000);
      return;
    }

    // If we're on the last step, handle form submission
    if (currentStep === 9) {
      handleSubmit();
      return;
    }

    // If valid, proceed to next step and hide any validation message
    setShowValidationMessage(false);
    setCurrentStep(currentStep + 1);
  };

  // Function to format form data according to the required structure
  const formatFormData = () => {
    const formattedData = [];

    // Step 1: Project Type
    if (formData.projectType.length > 0) {
      formattedData.push({
        question: "What would you like to do?",
        answers: formData.projectType
      });
    }

    // Step 2: Platform
    if (formData.platform) {
      formattedData.push({
        question: "Which platforms do you want to target?",
        answers: [formData.platform]
      });
    }

    // Step 3: Industry
    if (formData.industry) {
      const industryAnswer = formData.industry === "Other" ? formData.otherIndustry : formData.industry;
      formattedData.push({
        question: "Which industry are you targeting?",
        answers: [industryAnswer]
      });
    }

    // Step 4: App Type
    if (formData.appType) {
      const appTypeAnswer = formData.appType === "Other" ? formData.otherAppType : formData.appType;
      formattedData.push({
        question: `Specify the type of app in ${formData.industry === "Other" ? formData.otherIndustry : formData.industry}`,
        answers: [appTypeAnswer]
      });
    }

    // Step 5: Development Stage
    if (formData.developmentStage) {
      formattedData.push({
        question: "Where are you in the product development journey?",
        answers: [formData.developmentStage]
      });
    }

    // Step 6: Complexity Level
    if (formData.complexityLevel) {
      formattedData.push({
        question: "How complex will your application's logic be?",
        answers: [formData.complexityLevel]
      });
    }

    // Step 7: Design Polish
    if (formData.designPolish) {
      formattedData.push({
        question: "How polished does the design need to be?",
        answers: [formData.designPolish]
      });
    }

    // Step 8: Design Files
    if (formData.designFiles) {
      formattedData.push({
        question: "Design files available?",
        answers: [formData.designFiles === "Upload" ? "Yes, I have design files" : "No, need Design Services"]
      });
    }

    // Step 9: Additional Information (only if provided)
    if (formData.additionalInfo.trim()) {
      formattedData.push({
        question: "Anything else you want to tell us?",
        answers: [formData.additionalInfo.trim()]
      });
    }

    return formattedData;
  };

  // Handle form submission
  const handleSubmit = async () => {
    // Format the data according to the required structure
    const formattedData = formatFormData();

    // Log the formatted data
    console.log("Form submitted with formatted data:", formattedData);

    try {
      // Send the formatted data to the backend
      const response = await fetch("https://api.valueans.com/api/estimate-with-expert/estimateAi/", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formattedData), // Convert formatted data to JSON format
      });
      // Handle the response
      const data = await response.json();
      console.log("Response from the server:", data);

      // Check if the response is successful
      if (response.ok) {
        // Open the modal and show the estimated cost
        setCostRange(data.message); // Assuming data.message is the array with cost values
        setIsModalOpen(true); // Open the modal
      } else {
        alert("There was an error with your submission.");
      }
    } catch (error) {
      // Handle any error that occurred during the fetch request
      console.error("Error submitting the form:", error);
      alert("There was an error with your submission.");
    }

    // Reset the form and close it
    setFormData({
      projectType: [],
      platform: "",
      industry: "",
      otherIndustry: "",
      appType: "",
      otherAppType: "",
      developmentStage: "",
      complexityLevel: "",
      designPolish: "",
      designFiles: "",
      uploadedFiles: [],
      additionalInfo: "",
    });
    setCurrentStep(1);
  };
const closeModal = () => {
    setIsModalOpen(false); // Close the modal
  };

  const prevStep = () => {
    setShowValidationMessage(false);
    setCurrentStep(currentStep - 1);
  };

  // Progress bar calculation
  const progress = (currentStep / 9) * 100;

  if (!isVisible) return null;

  return (
    <div id="multi-step-form-section" className="w-[90%] mx-auto mb-10 md:mb-24 mt-10 bg-white rounded-lg p-8 shadow-md">
      <div className="flex flex-col items-center mb-8">
        <h2 className="text-2xl md:text-3xl font-bold mb-4">Cost Estimator</h2>

        {/* Progress bar */}
        <div className="w-full max-w-3xl bg-gray-200 rounded-full h-2.5 mb-2">
          <div
            className="bg-[#7716BC] h-2.5 rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
        <div className="text-sm text-gray-500">Step {currentStep} of 9</div>
      </div>

      <div className="max-w-3xl mx-auto">
        {/* Step 1: Project Type */}
        {currentStep === 1 && (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold mb-4">
              What would you like to do?
            </h3>
            <p className="text-gray-500 mb-4">Select all that apply</p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[
                { option: "Build a full project", image: "/Images/1step1.png" },
                { option: "Build an MVP", image: "/Images/1step2.png" },
                { option: "Modernize Existing Product", image: "/Images/1step3.png" }
              ].map(({ option, image }) => (
                <OptionCard
                  key={option}
                  option={option}
                  isSelected={formData.projectType.includes(option)}
                  onClick={() => handleProjectTypeChange(option)}
                  imageSrc={image}
                  layout="vertical"
                  className="p-6 text-center"
                />
              ))}
            </div>

            <div className="flex flex-col items-end mt-8">
              {showValidationMessage &&
                currentStep === 1 &&
                formData.projectType.length === 0 && (
                  <p className="text-red-500 text-sm mb-2">
                    Please select at least one option to continue
                  </p>
                )}
              <Button
                bgColor="bg-[#F245A1]"
                paddingX="px-8"
                paddingY="py-2"
                onClick={nextStep}
              >
                Next Step
              </Button>
            </div>
          </div>
        )}

        {/* Step 2: Platform */}
        {currentStep === 2 && (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold mb-4">
              Which platforms do you want to target?
            </h3>
            <p className="text-gray-500 mb-4">Select one option</p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {[
                { option: "Both Mobile and web", image: "/Images/2step1.png" },
                { option: "Only Mobile Devices", image: "/Images/2step2.png" },
                { option: "Only Web browsers", image: "/Images/2step3.png" },
                { option: "Desktop Softwares", image: "/Images/2step4.png" }
              ].map(({ option, image }) => (
                <OptionCard
                  key={option}
                  option={option}
                  isSelected={formData.platform === option}
                  onClick={() => setFormData(prev => ({ ...prev, platform: option }))}
                  imageSrc={image}
                  layout="horizontal"
                />
              ))}
            </div>

            <div className="flex justify-between items-end mt-8">
              <Button
                bgColor="bg-gray-700"
                textColor="text-white"
                paddingX="px-8"
                paddingY="py-2"
                onClick={prevStep}
              >
                Back
              </Button>
              <div className="flex flex-col items-end">
                {showValidationMessage &&
                  currentStep === 2 &&
                  !formData.platform && (
                    <p className="text-red-500 text-sm mb-2">
                      Please select a platform to continue
                    </p>
                  )}
                <Button
                  bgColor="bg-[#F245A1]"
                  paddingX="px-8"
                  paddingY="py-2"
                  onClick={nextStep}
                >
                  Next Step
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Step 3: Industry */}
        {currentStep === 3 && (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold mb-4">
              Which industry are you targeting?
            </h3>
            <p className="text-gray-500 mb-4">Select one option</p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {industries.map((industry) => (
                <div
                  key={industry.id}
                  onClick={() => handleIndustryChange(industry.name)}
                  className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                    formData.industry === industry.name
                      ? "border-[#F245A1] bg-pink-50"
                      : "border-gray-200 hover:border-gray-300"
                  }`}
                >
                  <div className="flex items-center">
                    {/* Icon */}
                    <div className="relative w-10 h-10 mr-4 flex-shrink-0">
                      <Image
                        src={`/Images/3step${industry.id}.png`}
                        alt={industry.name}
                        layout="fill"
                        objectFit="contain"
                      />
                    </div>

                    <span className={`${formData.industry === industry.name ? "text-[#F245A1] font-medium" : ""}`}>
                      {industry.name}
                    </span>
                  </div>
                </div>
              ))}

              {/* Other option with toggle */}
              <div
                onClick={() => handleIndustryChange("Other")}
                className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                  showOtherIndustry
                    ? "border-[#F245A1] bg-pink-50"
                    : "border-dashed border-gray-300 hover:border-gray-400 bg-gray-50"
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {/* Icon */}
                    <div className="relative w-10 h-10 mr-4 flex-shrink-0">
                      <Image
                        src="/Images/3step18.png"
                        alt="Other Industry"
                        layout="fill"
                        objectFit="contain"
                      />
                    </div>

                    <span className={`font-medium ${showOtherIndustry ? "text-[#F245A1]" : ""}`}>
                      Other
                    </span>
                  </div>
                  <div
                    className={`text-xl font-bold ${showOtherIndustry ? "text-[#F245A1]" : "text-gray-500"}`}
                  >
                    {showOtherIndustry ? "-" : "+"}
                  </div>
                </div>
              </div>
            </div>

            {/* Other industry input field */}
            {showOtherIndustry && (
              <div className="mt-4 max-w-md ">
                <label className="text-gray-600 mb-1 block text-sm">
                  Please specify your industry
                </label>
                <input
                  type="text"
                  placeholder="Enter your industry"
                  value={formData.otherIndustry}
                  onChange={handleOtherIndustryChange}
                  className="w-full border-0 border-b-2 border-gray-300 focus:border-b-[#F245A1] focus:outline-none py-2 px-1 transition-colors"
                  autoFocus
                />
              </div>
            )}

            <div className="flex justify-between items-end mt-8">
              <Button
                bgColor="bg-gray-700"
                textColor="text-white"
                paddingX="px-8"
                paddingY="py-2"
                onClick={prevStep}
              >
                Back
              </Button>
              <div className="flex flex-col items-end">
                {showValidationMessage &&
                  currentStep === 3 &&
                  !formData.industry &&
                  !(showOtherIndustry && formData.otherIndustry) && (
                    <p className="text-red-500 text-sm mb-2">
                      Please select an industry to continue
                    </p>
                  )}
                <Button
                  bgColor="bg-[#F245A1]"
                  paddingX="px-8"
                  paddingY="py-2"
                  onClick={nextStep}
                >
                  Next Step
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Step 4: App Type */}
        {currentStep === 4 && (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold mb-4">
              Specify the type of app in{" "}
              {formData.industry === "Other"
                ? formData.otherIndustry
                : formData.industry}
            </h3>
            <p className="text-gray-500 mb-4">Select one option</p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {getAppTypeOptions().map((option, index) => (
                <div
                  key={index}
                  onClick={() => handleAppTypeChange(option)}
                  className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                    formData.appType === option
                      ? "border-[#F245A1] bg-pink-50"
                      : "border-gray-200 hover:border-gray-300"
                  }`}
                >
                  <span className={`${formData.appType === option ? "text-[#F245A1] font-medium" : ""}`}>
                    {option}
                  </span>
                </div>
              ))}

              {/* Other option with toggle */}
              <div
                onClick={() => handleAppTypeChange("Other")}
                className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                  showOtherAppType
                    ? "border-[#F245A1] bg-pink-50"
                    : "border-dashed border-gray-300 hover:border-gray-400 bg-gray-50"
                }`}
              >
                <div className="flex items-center justify-between">
                  <span className={`font-medium ${showOtherAppType ? "text-[#F245A1]" : ""}`}>
                    Other
                  </span>
                  <div
                    className={`text-xl font-bold ${showOtherAppType ? "text-[#F245A1]" : "text-gray-500"}`}
                  >
                    {showOtherAppType ? "-" : "+"}
                  </div>
                </div>
              </div>
            </div>

            {/* Other app type input field */}
            {showOtherAppType && (
              <div className="mt-4 max-w-md ">
                <label className="text-gray-600 mb-1 block text-sm">
                  Please specify your app type
                </label>
                <input
                  type="text"
                  placeholder="Enter app type"
                  value={formData.otherAppType}
                  onChange={handleOtherAppTypeChange}
                  className="w-full border-0 border-b-2 border-gray-300 focus:border-b-[#F245A1] focus:outline-none py-2 px-1 transition-colors"
                  autoFocus
                />
              </div>
            )}

            <div className="flex justify-between items-end mt-8">
              <Button
                bgColor="bg-gray-700"
                textColor="text-white"
                paddingX="px-8"
                paddingY="py-2"
                onClick={prevStep}
              >
                Back
              </Button>
              <div className="flex flex-col items-end">
                {showValidationMessage &&
                  currentStep === 4 &&
                  !formData.appType &&
                  !(showOtherAppType && formData.otherAppType) && (
                    <p className="text-red-500 text-sm mb-2">
                      Please select an app type to continue
                    </p>
                  )}
                <Button
                  bgColor="bg-[#F245A1]"
                  paddingX="px-8"
                  paddingY="py-2"
                  onClick={nextStep}
                >
                  Next Step
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Step 5: Development Stage */}
        {currentStep === 5 && (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold mb-4">
              Where are you in the product development journey?
            </h3>
            <p className="text-gray-500 mb-4">Select one option</p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[
                { option: "Just an idea", image: "/Images/5step1.png" },
                { option: "Prototype Built", image: "/Images/5step2.png" },
                { option: "In development", image: "/Images/5step3.png" },
                { option: "Existing App live", image: "/Images/5step4.png" },
                { option: "Wireframes ready", image: "/Images/5step5.png" }
              ].map(({ option, image }) => (
                <OptionCard
                  key={option}
                  option={option}
                  isSelected={formData.developmentStage === option}
                  onClick={() => setFormData(prev => ({ ...prev, developmentStage: option }))}
                  imageSrc={image}
                  layout="vertical"
                  className="p-6 text-center"
                />
              ))}
            </div>

            <div className="flex justify-between items-end mt-8">
              <Button
                bgColor="bg-gray-700"
                textColor="text-white"
                paddingX="px-8"
                paddingY="py-2"
                onClick={prevStep}
              >
                Back
              </Button>
              <div className="flex flex-col items-end">
                {showValidationMessage &&
                  currentStep === 5 &&
                  !formData.developmentStage && (
                    <p className="text-red-500 text-sm mb-2">
                      Please select your development stage to continue
                    </p>
                  )}
                <Button
                  bgColor="bg-[#F245A1]"
                  paddingX="px-8"
                  paddingY="py-2"
                  onClick={nextStep}
                >
                  Next Step
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Step 6: Application Complexity */}
        {currentStep === 6 && (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold mb-4">
              How complex will your application's logic be?
            </h3>
            <p className="text-gray-500 mb-4">Select one option</p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[
                {
                  option: "Basic",
                  image: "/Images/6step1.png",
                  description: "Few screens, no major integrations"
                },
                {
                  option: "Moderate",
                  image: "/Images/6step2.png",
                  description: "Payment systems, API calls, user roles"
                },
                {
                  option: "Advanced",
                  image: "/Images/6step3.png",
                  description: "AI, automation, multirole dashboards, integrations"
                }
              ].map(({ option, image, description }) => (
                <OptionCard
                  key={option}
                  option={option}
                  isSelected={formData.complexityLevel === option}
                  onClick={() => setFormData(prev => ({ ...prev, complexityLevel: option }))}
                  imageSrc={image}
                  description={description}
                  layout="vertical"
                  className="p-6 text-center"
                />
              ))}
            </div>

            <div className="flex justify-between items-end mt-8">
              <Button
                bgColor="bg-gray-700"
                textColor="text-white"
                paddingX="px-8"
                paddingY="py-2"
                onClick={prevStep}
              >
                Back
              </Button>
              <div className="flex flex-col items-end">
                {showValidationMessage &&
                  currentStep === 6 &&
                  !formData.complexityLevel && (
                    <p className="text-red-500 text-sm mb-2">
                      Please select a complexity level to continue
                    </p>
                  )}
                <Button
                  bgColor="bg-[#F245A1]"
                  paddingX="px-8"
                  paddingY="py-2"
                  onClick={nextStep}
                >
                  Next Step
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Step 7: Design Polish */}
        {currentStep === 7 && (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold mb-4">
              How polished does the design need to be?
            </h3>
            <p className="text-gray-500 mb-4">Select one option</p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[
                {
                  option: "Basic",
                  image: "/Images/7step1.png",
                  description: "Functional"
                },
                {
                  option: "Advanced",
                  image: "/Images/7step2.png",
                  description: "Good UX/UI"
                },
                {
                  option: "Expert",
                  image: "/Images/7step3.png",
                  description: "Brand-quality design"
                }
              ].map(({ option, image, description }) => (
                <OptionCard
                  key={option}
                  option={option}
                  isSelected={formData.designPolish === option}
                  onClick={() => setFormData(prev => ({ ...prev, designPolish: option }))}
                  imageSrc={image}
                  description={description}
                  layout="vertical"
                  className="p-6 text-center"
                />
              ))}
            </div>

            <div className="flex justify-between items-end mt-8">
              <Button
                bgColor="bg-gray-700"
                textColor="text-white"
                paddingX="px-8"
                paddingY="py-2"
                onClick={prevStep}
              >
                Back
              </Button>
              <div className="flex flex-col items-end">
                {showValidationMessage &&
                  currentStep === 7 &&
                  !formData.designPolish && (
                    <p className="text-red-500 text-sm mb-2">
                      Please select a design polish level to continue
                    </p>
                  )}
                <Button
                  bgColor="bg-[#F245A1]"
                  paddingX="px-8"
                  paddingY="py-2"
                  onClick={nextStep}
                >
                  Next Step
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Step 8: Design Files */}
        {currentStep === 8 && (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold mb-4">
              Design files available?
            </h3>
            <p className="text-gray-500 mb-4">Select one option</p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {[
                {
                  option: "Upload",
                  image: "/Images/8step1.png",
                  description: "Figma, Sketch, Adobe XD"
                },
                {
                  option: "No",
                  image: "/Images/8step2.png",
                  description: "Need Design Services"
                }
              ].map(({ option, image, description }) => (
                <OptionCard
                  key={option}
                  option={option}
                  isSelected={formData.designFiles === option}
                  onClick={() => setFormData(prev => ({
                    ...prev,
                    designFiles: option,
                    ...(option === 'No' && { uploadedFiles: [] })
                  }))}
                  imageSrc={image}
                  description={description}
                  layout="vertical"
                  className="p-6 text-center"
                />
              ))}
            </div>

            {/* File Upload Area - Only show if Upload option is selected */}
            {formData.designFiles === "Upload" && (
              <div className="mt-6">
                <div
                  className={`border-2 border-dashed rounded-lg p-8 text-center ${
                    dragActive
                      ? "border-[#F245A1] bg-pink-50"
                      : "border-gray-300"
                  }`}
                  onDragOver={(e) => {
                    e.preventDefault();
                    setDragActive(true);
                  }}
                  onDragEnter={(e) => {
                    e.preventDefault();
                    setDragActive(true);
                  }}
                  onDragLeave={(e) => {
                    e.preventDefault();
                    setDragActive(false);
                  }}
                  onDrop={handleFileDrop}
                >
                  <div className="flex flex-col items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-12 w-12 text-gray-400 mb-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                      />
                    </svg>
                    <p className="mb-2 text-sm text-gray-700">
                      <span className="font-semibold">Click to upload</span> or
                      drag and drop
                    </p>
                    <p className="text-xs text-gray-500">
                      Figma, Sketch, Adobe XD, or any design files (MAX. 20MB)
                    </p>
                    <input
                      ref={fileInputRef}
                      type="file"
                      multiple
                      className="hidden"
                      onChange={handleFileUpload}
                      accept=".fig,.sketch,.xd,.pdf,.png,.jpg,.jpeg"
                    />
                    <button
                      type="button"
                      onClick={() => fileInputRef.current.click()}
                      className="mt-4 px-4 py-2 bg-[#F245A1] text-white rounded-md hover:bg-opacity-90 transition-colors"
                    >
                      Select Files
                    </button>
                  </div>
                </div>

                {/* File List */}
                {formData.uploadedFiles.length > 0 && (
                  <div className="mt-4">
                    <h4 className="font-medium mb-2">Uploaded Files:</h4>
                    <ul className="space-y-2">
                      {formData.uploadedFiles.map((file, index) => (
                        <li
                          key={index}
                          className="flex items-center justify-between bg-gray-50 p-2 rounded"
                        >
                          <div className="flex items-center">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="h-5 w-5 text-gray-500 mr-2"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                              />
                            </svg>
                            <span className="text-sm truncate max-w-xs">
                              {file.name}
                            </span>
                          </div>
                          <button
                            type="button"
                            onClick={() => handleFileRemove(index)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="h-5 w-5"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M6 18L18 6M6 6l12 12"
                              />
                            </svg>
                          </button>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}

            <div className="flex justify-between items-end mt-8">
              <Button
                bgColor="bg-gray-700"
                textColor="text-white"
                paddingX="px-8"
                paddingY="py-2"
                onClick={prevStep}
              >
                Back
              </Button>
              <div className="flex flex-col items-end">
                {showValidationMessage &&
                  currentStep === 8 &&
                  !formData.designFiles && (
                    <p className="text-red-500 text-sm mb-2">
                      Please select an option to continue
                    </p>
                  )}
                {showValidationMessage &&
                  currentStep === 8 &&
                  formData.designFiles === "Upload" &&
                  formData.uploadedFiles.length === 0 && (
                    <p className="text-red-500 text-sm mb-2">
                      Please upload at least one file to continue
                    </p>
                  )}
                <Button
                  bgColor="bg-[#F245A1]"
                  paddingX="px-8"
                  paddingY="py-2"
                  onClick={nextStep}
                >
                  Next Step
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Step 9: Additional Information */}
        {currentStep === 9 && (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold mb-4">
              Anything else you want to tell us?
            </h3>
            <p className="text-gray-500 mb-4">
              Feel free to provide any additional details about your project
            </p>

            <div className="mt-4">
              <textarea
                value={formData.additionalInfo}
                onChange={handleAdditionalInfoChange}
                placeholder="Please Write"
                className="w-full h-40 p-4 border-0 border-b-2 border-gray-300 focus:border-b-[#F245A1] focus:outline-none rounded-t-lg bg-gray-50 transition-colors resize-none"
              ></textarea>
            </div>

            <div className="flex justify-between items-end mt-8">
              <Button
                bgColor="bg-gray-700"
                textColor="text-white"
                paddingX="px-8"
                paddingY="py-2"
                onClick={prevStep}
              >
                Back
              </Button>
              <Button
                bgColor="bg-[#F245A1]"
                paddingX="px-8"
                paddingY="py-2"
                onClick={nextStep}
              >
                Finish
              </Button>
            </div>
          </div>
        )}
      </div>
       <CostEstimatorpoup isOpen={isModalOpen} onClose={closeModal} costRange={costRange} />
    </div>
  );
};

export default MultiStepForm;
