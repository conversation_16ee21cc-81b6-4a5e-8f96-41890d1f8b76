import React from "react";
import Accordion from "./Accordion";

const ML_Faq = () => {
  const accordionData = [
    {
      title: "What is machine learning, and how does it benefit my business?",
      content: "This is the content for item 1.",
    },
    {
      title:
        "What types of businesses can benefit from machine learning solutions?",
      content: "This is the content for item 2.",
    },
    {
      title: "How does machine learning help in fraud detection?",
      content: "This is the content for item 3.",
    },
    {
      title: "Can machine learning solutions automate customer service?",
      content: "This is the content for item 3.",
    },
    {
      title:
        "What should I consider when implementing machine learning solutions in my business?",
      content: "This is the content for item 3.",
    },
    {
      title:
        "How can Valueans’ machine learning solutions be customized for my business needs?",
      content: "This is the content for item 3.",
    },
    {
      title:
        "What are the common challenges associated with machine learning implementations?",
      content: "This is the content for item 3.",
    },
  ];

  return (
    <div className="max-w-[75%] mx-auto my-24">
      <h2 className="text-[38px] text-center font-medium text-[#232536] mb-[42px] ">
        Frequently Asked Questions
      </h2>
      <div className="p-6">
        {accordionData.map((item, index) => (
          <Accordion key={index} title={item.title} content={item.content} />
        ))}
      </div>
    </div>
  );
};

export default ML_Faq;
