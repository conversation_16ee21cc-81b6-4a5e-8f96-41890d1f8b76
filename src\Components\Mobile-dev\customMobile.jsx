import Image from "next/image";
import React from "react";

const CustomMobile = () => {
  return (
    <section className="">
      <div className="max-w-[85%] mx-auto py-8 flex justify-center items-center gap-10">
        <div className="flex-1">
          <h2 className="text-4xl font-semibold leading-[57px] ">
            How are we <span className="text-[#F245A1]">Different ?</span>
          </h2>

          <p className="text-lg leading-7 font-normal">
            Don’t worry, we won't waste your time or break the bank just to
            develop an app. <br /> Because we believe in providing
            non-conventional solutions while valuing your demands and needs. We
            understand the importance of your time and money, which is why we
            bring <PERSON><PERSON>ps to the rescue!
          </p>
          <p className="text-lg leading-7 font-normal my-4">
            ReOps–a perfect blend of AI and DevOps, accelerating the code reuse
            process for mobile application solutions making the development
            process faster and more efficient.
          </p>
          <p className="text-lg leading-7 font-normal">
            Our whole team of certified designers, developers, and strategists
            work closely to bring out the best in you while balancing
            both–automation and creativity.
          </p>
        </div>
        <div className="flex-1 w-[500px] h-[500px] relative">
          <Image
            src="/Images/h-01.png"
            alt="custom"
            // layout="fill"
            width={500}
            height={500}
            objectFit="cover" // or "contain" based on your requirement
          />
        </div>
      </div>
    </section>
  );
};

export default CustomMobile;
