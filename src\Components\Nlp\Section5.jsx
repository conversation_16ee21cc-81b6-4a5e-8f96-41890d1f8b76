const Card = ({ title, description }) => {
  return (
    <div className="max-w-sm relative ">
      <div className="bg-pink-100 p-4 rounded-t-md absolute top-[-20px] w-full z-10 shadow-md text-center">
        <h3 className="text-base md:text-lg font-semibold">{title}</h3>
      </div>
      <div className="w-full border border-purple-500 p-6 rounded-md shadow-sm pt-20 h-auto md:h-[50vh]">
        <p className="text-sm md:text-base">{description}</p>
      </div>
    </div>
  );
};

const cardData = [
  {
    title: "Deletion, Distortion, and Generalization (DDG)",
    description:
      "Valueans use deletion techniques to focus on specific client requirements while filtering out irrelevant details. This helps us streamline the development process. Distortion is applied in understanding client feedback, interpreting ambiguous requirements, or exploring alternative approaches to problem-solving. Making assumptions based on specific patterns, meaning applying similar thought processes for all similar occurrences. We use this to aid us in our development process.",
  },
  {
    title: "Meta-Programs",
    description:
      "Every person has a different mental filter. They interact with and perceive information differently. This allows us to collaborate and look at a problem from a host of angles to figure out the path forward. At Valueans' we have set high standards that we judge ourselves by. Our values and morals shape us into who we are. A strong belief system also brings us together. A belief in the power of innovation and hard work. Also learning from the mistakes and the successes of the past.",
  },
  {
    title: "Effective Communication",
    description:
      "Valueans prioritize enhanced communication and understanding for their clients which is why we create powerful NLP solutions by integrating the following NLP components. Our approach ensures that businesses can interact with their audiences more effectively, paving the way for improved customer engagement and satisfaction. Customer experience with Valueans is not easily forgettable. NLP software development services that suit your company and the way you operate. ",
  },
];

const Section5 = () => {
  return (
    <div className="mx-[75px] mb-10 md:mb-24">
      <h2 className="text-xl md:text-[28px] text-center font-semibold mb-[42px]">
        Key Components of the <span className="text-[#F245A1]">NLP Model</span>{" "}
        at Valueans
      </h2>
      <div className="max-w-fit mx-auto grid md:grid-cols-2 lg:grid-cols-3 gap-8 mt-6 md:mt-0">
        {cardData.map((card, index) => (
          <Card key={index} title={card.title} description={card.description} />
        ))}
      </div>
    </div>
  );
};

export default Section5;
