import React from "react";
import Card from "../Cloud Services/Card";

const Section9 = () => {
  return (
    <div className="w-[85%] mx-auto mb-10 md:mb-24">
      <h2 className="text-2xl md:text-[38px] md:leading-[57px] text-center font-semibold">
        Why Do<span className="text-[#7716BC]">Valueans</span> Stands Out
      </h2>
      <div className="flex flex-col justify-center items-center gap-3 md:gap-6 my-3 md:my-6">
        <div className="flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6">
          <Card
            title={"Deep Expertise"}
            description={
              "Our team of passionate experts understands both technical and business aspects of BI. We guide and support you at every step to ensure a smooth and successful implementation."
            }
            height={"h-auto md:h-[250px]"}
          />
          <Card
            title={"Comprehensive Services"}
            description={
              "We deliver end-to-end BI solutions from initial planning to ongoing support, giving your business a smooth experience and lasting success for your business."
            }
            height={"h-auto md:h-[250px]"}
          />
          <Card
            title={"Proven History"}
            description={
              "Across a wide range of industries, where our BI solutions consistently improved business performance."
            }
            height={"h-auto md:h-[250px]"}
          />
        </div>
        <div className="flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6">
          <Card 
            title={"Data Security"}
            description={
              "Keeping your data safe and secure is imperative. We apply strict security measures including encryption and industry standards."
            }
            height={"h-auto md:h-[250px]"}
          />
          <Card
            title={"Ongoing Support"}
            description={
              "Our collaboration continues after implementation. We stay with you and support you in keeping up with your growing business needs."
            }
            height={"h-auto md:h-[250px]"}
          />
        </div>
      </div>
      <div className="bg-[#350668] w-full my-5 md:my-10 p-2 md:p-4">
        <p className="w-[90%] mx-auto text-base md:text-xl text-justify rounded-md text-white">
          When you choose <span className="text-[#F245A1]">valueans</span> , you
          are not simply investing in BI solutions—you’re partnering with a team
          that’s all in on your growth and success. Let’s work together to make
          your data a strategic asset and take your business to the next level.
        </p>
      </div>
    </div>
  );
};

export default Section9;
