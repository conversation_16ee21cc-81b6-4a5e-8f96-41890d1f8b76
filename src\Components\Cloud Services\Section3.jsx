import React from "react";

const Section3 = () => {
  return (
    <div className="w-[85%] mx-auto my-10 md:my-24  gap-6 md:gap-10 flex flex-col md:flex-row justify-center md:justify-between items-center">
      <div className="w-full md:w-[50%]">
        <h2 className="text-lg md:text-3xl font-semibold">
          Achieve Better Efficiency and Improved Performance with Our{" "}
          <span className="text-[#7716BC]">Cloud Managed Services</span>{" "}
        </h2>
        <p className="text-base md:text-xl text-justify mt-2">
         Our cloud managed network is not a technological burden, but rather the foundation for your company's innovation. With our cloud managed data center services, you can focus on your primary business functions while we handle and manage your cloud environment.
        </p>
      </div>
      <div className="w-full md:w-[45%]">
        <section className="w-full  mx-auto p-4 border border-pink-800 rounded-xl shadow-md">
          <h3 className="text-lg md:text-xl font-semibold">
            With Managed Cloud Services at Valueans, we:
          </h3>
          <div className="py-5 flex flex-col gap-3 md:gap-5">
            <div className="flex items-center gap-4">
              <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
              <p className="text-sm md:text-lg">
                Supports AI and other contemporary workloads.
              </p>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
              <p className="text-sm md:text-lg">Become more adaptable.</p>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
              <p className="text-sm md:text-lg">
                Steer clear of vendor lock-in.
              </p>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
              <p className="text-sm md:text-lg">
                Improves governance, security, and compliance.
              </p>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
              <p className="text-sm md:text-lg">
                Covers the abilities that are lacking in cloud computing.
              </p>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
              <p className="text-sm md:text-lg">Drives platform engineering.</p>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default Section3;
