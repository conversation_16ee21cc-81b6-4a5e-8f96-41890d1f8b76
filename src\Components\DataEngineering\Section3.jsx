import React from "react";

const Section3 = () => {
  return (
    <div className="w-[85%] mx-auto my-10 md:my-24  gap-6 md:gap-10 flex flex-col md:flex-row justify-center md:justify-between items-center">
      <div className="w-full md:w-[40%]">
        <h2 className="text-lg md:text-3xl font-semibold">
          <span className="text-[#7716BC]">
            Valueans Data Engineering Services
          </span>{" "}
          to Strengthen Your Data and Analytics Initiatives
        </h2>
        <p className="text-base md:text-xl text-justify">
         Valueans offers big data engineering services that help you operationalize AI platforms and increase the effectiveness of cloud data pipelines. We assist you in creating robust data platforms that provide insights more quickly.
        </p>
      </div>
      <div className="w-full md:w-[45%]">
        <section className="w-full  mx-auto p-4 border border-pink-800 rounded-xl shadow-md">
          <h3 className="text-lg md:text-xl font-semibold">
            With Data Engineering Services & Solutions at Valueans, we help you:
          </h3>
          <div className="py-5 flex flex-col gap-3 md:gap-5 text-justify">
            <div className="flex items-center gap-4">
              <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
              <p className="text-sm md:text-lg">
                Use the insightful information you obtain from the data to
                inform smarter judgments.
              </p>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
              <p className="text-sm md:text-lg">
                Analyze collected data to enhance user experience and product
                quality.
              </p>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
              <p className="text-sm md:text-lg">
               Determine new business prospects by <a href='/Technologies/Predictive_Analysis' class='text-[#7716BC] hover:underline'> forecasting actions using past data</a>.
              </p>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
              <p className="text-sm md:text-lg">
                Reduce expenses and boost revenue by streamlining your data
                infrastructure.
              </p>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
              <p className="text-sm md:text-lg">
                Shorten project time by accelerating insight access procedures.
              </p>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default Section3;
