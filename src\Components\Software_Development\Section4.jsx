import React from "react";
import Link from "next/link";

// Array of industries content
const industries = [
  {
    title: "Healthcare",
    description: `We create custom software for <a href='/health-care-development' class='text-[#7716BC] hover:underline'>healthcare</a>, including, but not limited to, electronic health records (EHR), telemedicine systems, and billing software, which are HIPAA compliant. By improving patient care, our solutions ensure data security while facilitating the management of the hospital.`
  },
  {
    title: "Finance & Fintech",
    description: `Our systems also include <a href='/financial-app-development' class='text-[#7716BC] hover:underline'>fintech software development</a>, such as payment gateways, fraud detection systems, trading platforms, and digital banking systems. We focus on maximizing transactional efficiency while ensuring compliance with industry regulations.`
  },
  {
    title: "Retail & E-Commerce",
    description: `Retail is enabled through tailor-made software such as inventory management, POS, customer analytics, and AI recommendation engines. Solutions of this kind enhance user experience which in turn drives sales growth.`
  },
  {
    title: "Manufacturing & Supply Chain",
    description: `We aid with the automation of production processes through ERP solutions, predictive maintenance, and automated inventory management. We tailor our <a href='/Industries/Manufacturing' class='text-[#7716BC] hover:underline'>software solutions to help manufacturers</a> minimize downtime, process improvement, and improve their supply chain visibility.`
  },
  {
    title: "Logistics & Transportation",
    description: `We create <a href='/Industries/Logistics' class='text-[#7716BC] hover:underline'>solutions for logistic companies</a> to help with fleet management, routing optimization, and real time tracking applications. These solutions enhance the efficiency of delivery at lower operational costs.`
  },
  {
    title: "Education & E-learning",
    description: `Apart from creating <a href='/Industries/Education' class='text-[#7716BC] hover:underline'>custom software for educational institutions</a>, we also develop software for e-learning sites such as: LMS, virtual classrooms, AI remote tutoring and more. These tools significantly enhance WFH learning experiences.`
  },
  {
    title: "Real Estate & Construction",
    description: `We specialize in the creation of customized software for the <a href='/Industries/Real_Estate' class='text-[#7716BC] hover:underline'>Real Estate</a> and <a href='/Industries/Construction' class='text-[#7716BC] hover:underline'>Construction</a> sectors. These include property management systems, 3D modeling software, and automated contract management systems.`
  },
  {
    title: "Energy & Utilities",
    description: `We offer enterprise solutions for energy companies such as smart grid management, IoT monitoring systems, and predictive analytics and management for energy waste. This enables firms to better distribute their resources.`
  },
  {
    title: "Telecommunications",
    description: `We provide <a href='/Industries/Telecom' class='text-[#7716BC] hover:underline'>custom software development for telecom</a> companies which include billing software, customer relationship management (CRM), and network optimization software.`
  }
];

// Card component
const Card = ({ title, description }) => (
  <div className="w-full bg-white border border-purple-500 p-4 shadow-md rounded-md">
    <h3 className="text-base md:text-lg font-semibold mb-2">
      {title}
    </h3>
    <p
      className="text-sm md:text-base text-justify"
      dangerouslySetInnerHTML={{ __html: description }}
    />
  </div>
);

// Section4 component with grid layout
const Section4 = () => (
  <div className="container mb-10 md:mb-24">
    <h2 className="text-xl md:text-3xl font-semibold text-center mb-2">
      <span className="text-[#F245A1]"> Industries</span> We Serve
    </h2>
    <p className="md:w-3/5 md:mx-auto text-base md:text-xl text-center mb-6">
      At Valueans, we have a custom software application development service that cuts across a variety of industries and improves efficiency, security, and creativity. We tailor our solutions to help solve challenges specific to the industry and promote digital advancement.
    </p>

    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {industries.map((item, idx) => (
        <Card key={idx} title={item.title} description={item.description} />
      ))}
    </div>
  </div>
);

export default Section4;
