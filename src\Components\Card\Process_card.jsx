import Image from "next/image";
import React from "react";

const Process_card = ({ title, descriptiom }) => {
  return (
    <>
      <div className="block w-[85vw] md:w-[25vw] h-[25%] md:h-[10%] p-3 md:p-6 bg-white border border-purple-700 rounded-lg overflow-hidden shadow hover:bg-gray-100">
        <div className="flex items-center">
          <div>
            <Image src={"/Images/Tick.png"} alt="Tick" width={42} height={42} />
          </div>
          <h5 className="mb-1 md:mb-2 text-lg md:text-2xl font-bold tracking-tight text-[#232222]">
            {title}
          </h5>
        </div>

        <p className="font-normal text-base md:text-xl text-[#232222]">
          {descriptiom}
        </p>
      </div>
    </>
  );
};

export default Process_card;
