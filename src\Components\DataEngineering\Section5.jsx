import Image from "next/image";
import React from "react";
import InfoCard from "../Cloud Services/InfoCard";

const Section5 = () => {
  const infoCards = [
    {
      imgSrc: "/Images/service_frame.png",
      altText: "Boost the Quality of the Data",
      title: "Boost the Quality of the Data",
      description:
        "Before utilizing information in analytical systems, our data engineers assist companies in gathering data from several sources and validating it. It lessens the possibility of making poor judgments based on unrelated information. ",
    },
    {
      imgSrc: "/Images/service_frame.png",
      altText: "Innovative ReOps Integration",
      title: "Maximize the Use of Large Data",
      description:
        "Advanced system techniques are used by Valueans to manage massive volumes of data and aggregate data from several sources into a single repository for additional processing. ",
    },
    {
      imgSrc: "/Images/service_frame.png",
      altText: "Boost Output",
      title: "Boost Output",
      description:
        "We offer sophisticated services that improve the data-driven processes of your company, finish them, and have them ready for precise analysis as quickly as possible. ",
    },
    {
      imgSrc: "/Images/service_frame.png",
      altText: "Cut Expenses",
      title: "Cut Expenses",
      description:
        "We provide very affordable data engineering services. Data engineers, who are specialists in big data technology, identify the best data architecture solutions and pipelines for the requirements of certain enterprises.",
    },
    {
      imgSrc: "/Images/service_frame.png",
      altText: "Expertise Across Technologies",
      title: "Protect Information and Preserve Accuracy",
      description:
        "We set up thorough procedures for compliance, access control, and data validation. It helps us to set strong data engineering basics to deliver the best big data engineering services.",
    },
    {
      imgSrc: "/Images/service_frame.png",
      altText: "Commitment to Quality",
      title: "Quick Data-Driven Decisions",
      description:
        "To facilitate decision-making at all levels, we make use of strong data pipelines to provide real-time, actionable information. Data pipelines will help make better decisions for your business using the provided insights. ",
    },
    {
      imgSrc: "/Images/service_frame.png",
      altText: "Seamless Integration",
      title: "Infrastructure for Scalable Data",
      description:
        "With our data engineering services & solutions, you can maintain peak performance while effectively handling growing data loads.",
    },
  ];

  return (
    <section className="bg-blue-100 my-10 md:my-24 p-4 md:p-6">
      <div className="w-[90%] mx-auto">
        <h3 className="text-xl md:text-3xl md:leading-[40px] font-semibold text-center">
          What You Get with <span className="text-[#7716BC]">Valueans</span>{" "}
          Data Engineering Services & Solutions
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 my-3 md:my-5">
          {infoCards.map((card, index) => (
            <div
              key={index}
              className={`${
                index === infoCards.length - 1
                  ? "md:col-span-2 md:flex md:justify-center"
                  : ""
              }`}
            >
              <InfoCard
                imgSrc={card.imgSrc}
                altText={card.altText}
                title={card.title}
                description={card.description}
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Section5;
