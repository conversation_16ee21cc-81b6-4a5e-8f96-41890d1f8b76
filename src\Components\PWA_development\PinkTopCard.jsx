import React from "react";

const PinkTopCard = ({
  title,
  description,
  description2,
  PinkTopCardheight,
  listData,
  cardHeaderHeight,
}) => {
  return (
    <div className="w-full md:max-w-md relative">
      <div
        className={`bg-pink-100 p-3 md:p-4 ${cardHeaderHeight ? cardHeaderHeight : "h-[80px] md:h-[100px]"} rounded-t-md absolute top-[-20px] w-full z-10 shadow-md text-center flex items-center justify-center`}
      >
        <h3 className="text-base md:text-lg font-semibold">{title}</h3>
      </div>

      <div
        className={`w-full border border-purple-500 p-1 rounded-md shadow-sm ${cardHeaderHeight ? "pt-5" : "pt-16"} h-auto ${PinkTopCardheight ? PinkTopCardheight : ""}`}
      >
        {description && (
          <p
            className="text-sm md:text-base mx-3 md:m-5 mt-5 text-justify"
            dangerouslySetInnerHTML={{ __html: description }}
          />
        )}
        {description2 && (
          <p className="text-sm md:text-base mx-3 md:m-5 mb-5 text-justify">
            {description2}
          </p>
        )}
        {listData && listData.length > 0 && (
          <ul className="list-disc list-inside text-sm md:text-base mx-3 md:m-5 mb-5 text-justify">
            {listData.map((item, index) => (
              <li key={index} dangerouslySetInnerHTML={{ __html: item || "" }} />
            ))}
          </ul>
        )}
      </div>
    </div>
  );
};

export default PinkTopCard;
