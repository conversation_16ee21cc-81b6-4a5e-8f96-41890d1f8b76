import PinkTopCard from "./PinkTopCard";

const Section6 = ({
  PinkTopCardData,
  PinktopCardheight,
  heading,
  spanLeft,
  spanRight,
  paragraph,
  cardHeaderHeight
}) => {
  return (
    <div className="w-[90%] mx-auto mb-10 md:my-24 ">
      <div className="mb-[42px]">
        <h2 className="text-xl md:text-3xl text-center font-semibold ">
          <span className="text-[#7716BC]">{spanLeft}</span> {heading}{" "}
          <span className="text-[#7716BC]">{spanRight} </span>
        </h2>
        <p className="text-xl md:w-[70%] mx-auto text-center">{paragraph}</p>
      </div>
      <div className="max-w-fit mx-auto grid md:grid-cols-2 lg:grid-cols-3 gap-8 mt-6 md:mt-[0px]">
        {PinkTopCardData.map((card, index) => (
          <PinkTopCard
            key={index}
            title={card.title}
            description={card.description}
            PinkTopCardheight={PinktopCardheight}
            cardHeaderHeight={cardHeaderHeight}
          />
        ))}
      </div>
    </div>
  );
};

export default Section6;
