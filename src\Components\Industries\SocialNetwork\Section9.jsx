import React from "react";

const Section9 = ({
  headingLeft,
  headingRight,
  spanHeading,
  cardData,
}) => {
  return (
    <div className="w-[90%] mx-auto mb-10 md:my-24 ">
      <div className="my-2   text-center ">
        <h1 className="text-xl md:text-3xl md:leading-[40px] md:pl-4 md:w-[50%] mx-auto font-semibold">
          {headingLeft} <span className="text-[#F245A1]">{spanHeading}</span>{" "}
          {headingRight}
        </h1>
        
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {cardData.map((card, index) => (
          <div
            key={index}
            className="p-4 border border-[#F245A1] rounded-lg shadow-md"
          >
            <h3 className="text-base md:text-lg font-bold">{card.title}</h3>
            <p className="mt-2 text-sm text-[#232222]">{card.description}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Section9;
