import PinkDotCard2 from "../HealthCare/PinkDotCard2";
import Image from "next/image";
import React from "react";

const Section4 = ({ heading, paragraph, PinkDotCardData, image }) => {
  return (
    <div className="bg-[#F245A126] py-3">
      <div className="w-[90%] mx-auto mb-10 md:my-24 ">
        <div className="flex flex-col md:flex-row justify-between gap-8">
          <div className="w-full md:w-[50%]">
            <h2 className="text-xl md:text-3xl md:leading-[40px] font-semibold mb-1 md:mb-3">
              {heading}
            </h2>
            <p className="text-base md:text-lg text-justify mb-2"> {paragraph}</p>
            <PinkDotCard2 cardContent={PinkDotCardData} />
          </div>
          <div className="min-h-[250px] md:min-h-0 w-full md:w-[50%]  relative">
            {/* Make the image take full space and maintain aspect ratio */}
            <Image src={image} alt="AI" fill className="object-cover" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Section4;
