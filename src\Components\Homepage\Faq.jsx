import React from "react";
import Accordion from "../Faq/Accordion";
import Heading from "../Heading/Heading";

const Faq = ({ title = "Frequently Asked Questions", data }) => {
  return (
    <>
      <Heading >{title}</Heading>
     <div className="max-w-[75%] mx-auto mt-5 mb-10 bg-[#F4F5F6] rounded-lg shadow-lg">
      
      <div className="p-6">
        {data.map((item, index) => (
          <Accordion key={index} title={item.title} content={item.content} />
        ))}
      </div>
    </div>
    </>
  );
};

export default Faq;