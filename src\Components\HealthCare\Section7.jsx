import React from "react";
import ServiceCount from "../Services/ServiceCount_2";

const Card = ({ title, description }) => {
  return (
    <div className="block w-full md:max-w-xl h-auto md:h-[150px] py-2 px-3 bg-white border border-purple-500 rounded-md shadow">
      <h3 className="text-base font-semibold">{title}</h3>
      <p className="text-sm font-light text-justify">{description}</p>
    </div>
  );
};

const Section7 = () => {
  return (
    <section className="bg-pink-100 mb-10 md:mb-24 py-4 md:py-10">
      <div className="w-[85%] mx-auto">
        <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold mb-1 md:mb-3">
          Our Process
        </h2>

        <div className="flex flex-col justify-center items-center gap-3 md:gap-6 my-3 md:my-6">
          <div className="flex flex-col md:flex-row justify-center gap-3 md:gap-6">
            <div className="flex justify-center items-center gap-2">
              <ServiceCount>1</ServiceCount>
              <Card 
                title={"Consulting"}
                description={
                  "By assisting you in selecting the solution that best satisfies your business's essential needs, offering guidance on platforms or technologies that complement your company's ecosystem, and creating an effective implementation strategy, our advisors help you create the finest possible digital environments."
                }
              />
            </div>
            <div className="flex justify-center items-center gap-2">
              <ServiceCount>2</ServiceCount>
              <Card 
                title={"Implementation"}
                description={
                  "The staff at Implementation Valueans is prepared to provide any kind of healthcare solution in line with your company's goals. We use best practices for application deployment, considering industry and security standards while following the approach that works best for your situation."
                }
              />
            </div>
          </div>
          <div className="flex flex-col md:flex-row justify-center gap-3 md:gap-6">
            <div className="flex justify-center items-center gap-2">
              <ServiceCount>3</ServiceCount>
              <Card
                title={"Integration"}
                description={
                  "By safely connecting all of your clinical and administrative software solutions and with external apps, we guarantee the interoperability of your healthcare IT environment."
                }
              />
            </div>
            <div className="flex justify-center items-center gap-2">
              <ServiceCount>4</ServiceCount>
              <Card
                title={"Support & Maintenance"}
                description={
                  "Our professionals assist healthcare organizations in maintaining the cost-effectiveness, flexibility, security, and full functionality of their software. We offer a variety of support services, such as audits, round-the-clock performance monitoring, and prompt updates and modifications."
                }
              />
            </div>
          </div>
          <div className="flex justify-center">
            <div className="flex justify-center items-center gap-2">
              <ServiceCount>5</ServiceCount>
              <Card
                title={"Legacy Software Modernization"}
                description={
                  "We assist healthcare organizations in modernizing, expanding, or rewriting their outdated software. Your present software may be improved by our developers to become more dependable, secure, and standard-compliant."
                } 
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Section7;
