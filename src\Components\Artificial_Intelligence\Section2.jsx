import Image from "next/image";
import React from "react";

const Section2 = () => {
  return (
    <section className="bg-[#350668] my-24">
      <div className="w-[80%] mx-auto flex justify-center items-center  py-10">
        <div className="flex-1">
          <h2 className="text-[#F245A1]  text-3xl font-semibold leading-10">
            What is AI/ML and why <br /> does it matter to your <br /> business?
          </h2>
        </div>
        <div className="flex-1">
          <p className="text-xl font-normal text-white">
            There's no denying that data is becoming a more valuable asset for
            businesses; worldwide data generation and storage are expanding at
            an exponential rate. Naturally, gathering data is useless if it is
            not put to any use, but in the absence of automated methods, these
            massive data floods are just insurmountable.
          </p>
          <p className="text-xl font-normal text-white mt-10">
            Organizations can now get value from the mountains of data they
            gather by utilizing artificial intelligence, machine learning, and
            deep learning to automate operations, get business insights, and
            enhance system capabilities. AI and ML have the power to completely
            change a company by assisting in the achievement of quantifiable
            goals like:
          </p>
          <div className="flex justify-center  mt-10">
            <div className="flex flex-col gap-4 justify-center ">
              {["Cross-Industry ML Expertise", "Agile Development"].map(
                (text, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <Image
                      src={"/Images/Tick.png"}
                      alt="Tick"
                      width={24}
                      height={24}
                      className="w-6 h-6"
                    />
                    <p className="text-lg text-white">{text}</p>
                  </div>
                )
              )}
            </div>

            <div className="flex flex-col gap-4 justify-center ">
              {["Flexible Engagement Models", "Process Transparency"].map(
                (text, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <Image
                      src={"/Images/Tick.png"}
                      alt="Tick"
                      width={24}
                      height={24}
                      className="w-6 h-6"
                    />
                    <p className="text-lg text-white">{text}</p>
                  </div>
                )
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Section2;
