"use client";

import React, { useState } from "react";
import Image from "next/image";

export default function Section5({ images }) {
  const [currentIndex, setCurrentIndex] = useState(0);

  const handlePrevious = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? images.length - 1 : prevIndex - 1
    );
  };

  const handleNext = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === images.length - 1 ? 0 : prevIndex + 1
    );
  };

  return (
    <div>
      <h1 className="text-3xl font-semibold text-center my-5 text-[#F245A1]">What was delivered?</h1>
      <div className="w-full px-4 relative">
        <div className="flex justify-center items-center">
          <div className="w-[60%] rounded-xl border shadow-lg">
            <Image
              src={images[currentIndex]}
              alt={`Slide ${currentIndex + 1}`}
              width={900}
              height={900}
              className="w-full h-full object-contain rounded-xl"
            />
          </div>
          
          {images.length > 1 && (
            <>
              <button
                onClick={handlePrevious}
                className="absolute left-8 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white p-2 rounded-full shadow-lg"
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
                </svg>
              </button>
              
              <button
                onClick={handleNext}
                className="absolute right-8 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white p-2 rounded-full shadow-lg"
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                </svg>
              </button>
            </>
          )}
        </div>
      </div>
      <h3 className="text-2xl font-semibold my-5 text-center">Web Application</h3>
    </div>
  );
}
