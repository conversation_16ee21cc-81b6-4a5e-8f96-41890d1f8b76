import React from "react";

const CostEstimatorpoup = ({ isOpen, onClose, costRange }) => {
  if (!isOpen) return null;

  return (
    <div className="modal-overlay fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
      <div className="modal-content bg-white p-6 rounded-lg max-w-md w-full text-center">
        <h2 className="text-2xl font-semibold mb-4">
          Total Cost to Build An App
        </h2>
        <h3 className="text-xl font-bold mb-6">
          <strong className="text-[#F245A1]">{costRange[0]}</strong> to{" "}
          <strong className="text-[#F245A1]">{costRange[1]}</strong>
        </h3>
        <p className="text-lg mb-4">Want a more accurate estimate?</p>
        <p className="text-sm text-gray-600 mb-4">
          Enter your details above to receive a full plan build with line-item
          cost and hours analysis.
        </p>
        <p className="text-sm text-gray-600 mb-6">
          Note: this is an estimate and is subject to change based on a more
          detailed technical assessment of your needs. If you require additional
          features that are not represented in your build plan, we recommend
          consulting a Valueans Expert before initiating development.
        </p>
              <button
                  id="Modal-button"
          className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition duration-200"
          onClick={onClose}
        >
          Close
        </button>
      </div>
    </div>
  );
};

export default CostEstimatorpoup;
