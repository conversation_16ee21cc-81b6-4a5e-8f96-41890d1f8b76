import React from "react";
import Dropdown from "../Services/Dropdown";

const WhatOffer = () => {
  return (
    <div className="w-[80%] mx-auto my-24">
      <h2 className="text-4xl text-center font-semibold">What we offer</h2>
      <p className="text-xl text-center my-10">
        Unlike other software development companies, Valueans goes above and
        beyond to meet your requirements because we understand issues like high
        costs, inadequate customization, and outdated frameworks can hinder your
        business from growing which is why we offer customized solutions for
        iOS, Android, and Cross-platform applications that help you build a
        scalable and futuristic product without wasting millions.
      </p>
      <div className="flex justify-between items-center mb-10">
        <Dropdown
          title="IOS Application Development"
          content="Our skilled developers are trained to craft feature-rich Web Applications that deliver valuable information and interactive services resulting in higher user engagement. We provide the latest technology by combining AI and DevOps which saves your time and money ensuring increased scalability, maintainability, and flexibility for your applications."
        />
        <Dropdown
          title="Android App Development"
          content="At Valueans, we develop feature-packed futuristic mobile applications with user-friendly and sleek front ends. Our skillful Android developers make the user's experience 10x better by blending the latest technology, efficient development process, and custom mobile app specifications."
        />
      </div>
      <div className="flex justify-between items-center mb-10">
        <Dropdown
          title="Native and Cross Platform Solution"
          content="Our skilled developers are trained to craft feature-rich Web Applications that deliver valuable information and interactive services resulting in higher user engagement. We provide the latest technology by combining AI and DevOps which saves your time and money ensuring increased scalability, maintainability, and flexibility for your applications."
        />
        <Dropdown
          title="UI/UX Design"
          content="At Valueans, we develop feature-packed futuristic mobile applications with user-friendly and sleek front ends. Our skillful Android developers make the user's experience 10x better by blending the latest technology, efficient development process, and custom mobile app specifications."
        />
      </div>
      <Dropdown
        title="Saas App Development"
        content="At Valueans, we develop feature-packed futuristic mobile applications with user-friendly and sleek front ends. Our skillful Android developers make the user's experience 10x better by blending the latest technology, efficient development process, and custom mobile app specifications."
      />
    </div>
  );
};

export default WhatOffer;
