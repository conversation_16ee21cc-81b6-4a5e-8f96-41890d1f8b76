import React from "react";
import Accordion from "./Accordion";
import Heading from "../Heading/Heading";

const Faq = ({bgColor}) => {
  const accordionData = [
    {
      title: "Why is UI/UX design important for my business?",
      content: "UI/UX design is crucial because it directly impacts how users interact with your product. A well-designed interface can improve user satisfaction, increase engagement, and ultimately drive more conversions and business success.",
    },
    {
      title: "What is the difference between UI and UX design?",
      content: "UI (User Interface) design focuses on the visual aspects of a product, such as layout, colors, and typography. UX (User Experience) design involves the overall experience a user has while interacting with the product, including usability, navigation, and functionality",
    },
    {
      title: "How do you ensure your designs are user-centered?",
      content: "We conduct extensive user research and analysis to understand the needs, behaviors, and pain points of your target audience. This information guides our design process, ensuring that our solutions are tailored to meet user expectations.",
    },
    {
      title: "Do you offer mobile UI UX design services?",
      content: "Yes, we specialize in mobile UI/UX design services to make sure that your application provides an error free and engaging experience across all devices.",
    },
    {
      title: "What tools do you use for UI/UX design?",
      content: "We use a variety of industry-standard tools such as Sketch, Figma, Adobe XD, InVision, and Axure for designing and prototyping our UI/UX projects.",
    },
    {
      title: "Can you design custom icons and animations?",
      content: "Absolutely! We offer custom icon set design and animated UI design to enhance the visual appeal and interactivity of your product.",
    },
    {
      title: "How do you conduct usability testing?",
      content: "We conduct usability testing by creating prototypes and having real users interact with them. We observe their interactions, gather feedback, and identify any usability issues to refine the design.",
    },
    {
      title: "What is the cost of your UI UX design services?",
      content: "The cost of our UI/UX design services varies depending on the complexity and scope of the project. We provide customized quotes based on your specific needs and requirements.",
    },
    {
      title: "How long does it take to complete a UI/UX design project?",
      content: "The timeline for completing a UI/UX design project depends on the project’s complexity and requirements. On average, a project can take anywhere from a few weeks to several months.",
    },
    {
      title: "Does Valueans offer ongoing support and maintenance?",
      content: "Yes, we offer ongoing support and maintenance to ensure your product remains up-to-date and performs optimally. We’re here to assist with any updates or changes you might need post-launch.",
    },
  ];

  return (
    <div className={`max-w-[75%] mx-auto my-24 md:mt-24 ${bgColor ? bgColor : "bg-[#F4F5F6]"} rounded-lg  shadow-lg`}>
      <Heading>Frequently Asked Questions</Heading>
      <div className="p-6">
        {accordionData.map((item, index) => (
          <Accordion key={index} title={item.title} content={item.content} />
        ))}
      </div>
    </div>
  );
};

export default Faq; 


