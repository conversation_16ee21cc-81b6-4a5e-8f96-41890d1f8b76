import React from "react";
import Heading from "../Heading/Heading";
import Card from "../Technologies/Card";

const cardData = [
  {
    Imgsrc: "/Images/Group.png",
    Altsrc: "Automation Icon",
    title: "Utilize Simple Drag and Drop to Automate Procedures",
    description:
      "With Laserfiche’s useful visualization tools and drag-and-drop interface, you can automate the business operations you depend on daily. Without any technological knowledge, incorporate conditional logic into automated procedures.",
  },
  {
    Imgsrc: "/Images/Group.png",
    Altsrc: "Data Consistency Icon",
    title: "Improve Data Consistency",
    description:
      "Automate organizational procedures to guarantee data integrity. Validation criteria may be incorporated, and pertinent data only has to be entered once.",
  },
  {
    Imgsrc: "/Images/Group.png",
    Altsrc: "Quick Approvals Icon",
    title: "Quick Approvals",
    description:
      "Make online forms that may be filled out and send them to the right people automatically. Laserfiche offers visibility and tracking throughout the form’s lifespan.",
  },
  {
    Imgsrc: "/Images/Group.png",
    Altsrc: "Integration Icon",
    title: "Integrate with Line of Business Applications",
    description:
      "Business technologists may easily include data from hundreds of different apps into their workflows with Laserfiche’s native and templated interfaces.",
  },
  {
    Imgsrc: "/Images/Group.png",
    Altsrc: "Empowerment Icon",
    title: "Empower Business Technologists",
    description:
      "You may create the procedures you want without requiring IT work thanks to safe role segregation.",
  },
  {
    Imgsrc: "/Images/Group.png",
    Altsrc: "Compliance Icon",
    title: "Maintain Control Over Compliance",
    description:
      "Automate processes without worrying about non-compliance. Even though anybody can develop, IT nevertheless maintains control to guarantee enterprise-wide compliance.",
  },
  {
    Imgsrc: "/Images/Group.png",
    Altsrc: "Business Insights Icon",
    title: "Get More Comprehensive Business Insights",
    description:
      "More business insights result from more data. Both bespoke and prebuilt reporting tools enable you to make use of your automation to make data-driven choices and have a better understanding of your business.",
  },
  {
    Imgsrc: "/Images/Group.png",
    Altsrc: "Integration Icon",
    title: "Build Your Integrations",
    description:
      "Drag-and-drop process automations that operate on an application’s surface make it simple to create unique integrations without having to alter the underlying architecture.",
  },
];

const Section9 = () => {
  return (
    <div className="mx-auto md:mx-[75px] mb-10 md:mb-24">
      <Heading>
        Benefits of <span className="text-[#F245A1]">Low Code Automation</span>
      </Heading>
      <div className="flex flex-col items-center mt-6 md:mt-[42px]">
        {/* First two rows (3 cards per row) */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-8 w-full max-w-5xl">
          {cardData.slice(0, 6).map((card, index) => (
            <Card key={index} {...card} />
          ))}
        </div>

        {/* Last row (2 cards, centered) */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-8 justify-center mt-4">
          {cardData.slice(6).map((card, index) => (
            <Card key={index} {...card} />
          ))}
        </div>
      </div>
    </div>
  );
};

export default Section9;
