import Image from "next/image";
import React from "react";

const Info_card = ({ title, description }) => {
  return (
    <div className="block w-[85vw] md:w-[40vw] h-auto p-[2px] mb-2 md:mb-0 bg-gradient-to-r from-pink-500 via-purple-500 to-blue-500 rounded-lg shadow hover:bg-gray-100">
      <div className="p-2 md:p-6 bg-white rounded-lg">
        <div className="flex items-center gap-2">
          <div>
            <Image
              src={"/Images/service_frame.png"}
              alt="arrow"
              width={32}
              height={32}
            />
          </div>
          <h5 className="mb-1 md:mb-2 text-base md:text-lg font-bold tracking-tight text-[#232222]">
            {title}
          </h5>
        </div>

        <p className="font-normal text-sm md:text-lg  text-[#232222]">
          {description}
        </p>
      </div>
    </div>
  );
};
export default Info_card;
