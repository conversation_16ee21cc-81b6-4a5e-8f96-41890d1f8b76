import React from "react";

const Text = () => {
  return (
    <section className="my-24 relative">
      <div className="mb-14">
        <p className="text-center text-[#F245A1] text-[24px] leading-[28px] ">
          What We Can Do For You
        </p>
        <h2 className="text-center text-[#232536] text-[38px] leading-[57px] font-semibold">
          <span className="text-[#7716BC]">Services</span> provide for you
        </h2>
      </div>

      <div className="bg-[#C67FF9] z-10 mx-6 absolute top-[123px] left-[152px] rounded-md shadow-lg">
        <h2 className="text-white m-8 text-2xl">
          Software Development & Engineering
        </h2>
      </div>

      <div className="max-w-[80%] mx-auto relative flex">
        {/* Purple Div */}
        <div className="absolute bg-[#7716BC] w-1/3 p-8 h-[715px] top-[-50px]">
          {" "}
          {/* Add top-[-50px] */}
          <ul className="text-2xl leading-7 px-7 mt-32">
            <li className="my-3 text-white mt-8">Design & User Experience</li>
            <li className="my-3 text-white mt-8">Data & Analytics</li>
            <li className="my-3 text-white mt-8">Cloud & IT Infrastructure</li>
            <li className="my-3 text-white mt-8">
              Industry-Specific Solutions
            </li>
            <li className="my-3 text-white mt-8">Testing & Support</li>
          </ul>
        </div>

        {/* White Div */}
        <div className="bg-white w-2/3 ml-[33%] p-8 min-h-[400px]">
          <h2 className="max-w-fit text-center mb-8 mt-[91px] mx-auto p-2 text-[32px] leading-[48px] text-[#232536] font-semibold border-b-2 border-[#7716BC]">
            Software Development & Engineering erwrwewerrewerrerwerweererwwerwerrewerwerwerwerwerwerrwerrwewerwerrwrrwerwerwerwerwerwerwerrwerwerwerer
          </h2>
          <p className="text-[#232222] text-[20px] leading-[30px] px-[30px]">
            A software development company with 35 years of business excellence,
            we can develop reliable, scalable and secure software solutions for
            any OS, browser and device. We bring together deep industry
            expertise and the latest IT advancements to deliver custom solutions
            and products that perfectly fit the needs and behavior of their
            users.
          </p>
          <div class="relative left-8 py-12">
            <div class=" max-w-full mx-auto grid grid-cols-1 sm:grid-cols-2 gap-1">
              <div>
                <ul class="space-y-4  text-xl font-semibold text-[#232536]">
                  <li class="flex items-center space-x-2 border-b-2 border-pink-500 max-w-fit">
                    <span class="text-pink-500">›</span>
                    <span>Custom Software Development</span>
                  </li>
                  <li class="flex items-center space-x-2 border-b-2 border-pink-500 max-w-fit">
                    <span class="text-pink-500">›</span>
                    <span>Mobile App Development</span>
                  </li>
                  <li class="flex items-center space-x-2 border-b-2 border-pink-500 max-w-fit">
                    <span class="text-pink-500">›</span>
                    <span>Web Application Development</span>
                  </li>
                  <li class="flex items-center space-x-2 border-b-2 border-pink-500 max-w-fit">
                    <span class="text-pink-500">›</span>
                    <span>Dedicated Development Teams</span>
                  </li>
                </ul>
              </div>
              <div>
                <ul class="space-y-4  text-xl font-semibold text-[#232536]">
                  <li class="flex items-center space-x-2 border-b-2 border-pink-500 max-w-fit">
                    <span class="text-pink-500">›</span>
                    <span>Full Stack Development</span>
                  </li>
                  <li class="flex items-center space-x-2 border-b-2 border-pink-500 max-w-fit">
                    <span class="text-pink-500">›</span>
                    <span>Software Development</span>
                  </li>
                  <li class="flex items-center space-x-2 border-b-2 border-pink-500 max-w-fit">
                    <span class="text-pink-500">›</span>
                    <span>Application Services</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Text;
