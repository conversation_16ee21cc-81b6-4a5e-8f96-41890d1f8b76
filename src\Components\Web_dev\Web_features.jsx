import React from "react";
import ServiceCard from "../Services/ServiceDevCard";

const Web_features = () => {
  return (
    <div className="w-[90%] mx-auto my-12">
      <h2 className="text-center text-3xl font-semibold mb-8">
        Features We Offer
      </h2>
      <div className="flex flex-wrap justify-center items-start gap-6">
        <ServiceCard
          title={"Customization"}
          imageSrc={"/Images/custom.png"}
          altText={"custom"}
          description={
            "We provide the perfect blend of customization and creativity. While we understand the importance of personalization, we also acknowledge the need to stay up-to-date and modern. We showcase our talents at each step whether it is development, design, or management while keeping you in the loop. We use the latest frameworks, coding techniques, and automation to bring a perfect Web App for you."
          }
        />
        <ServiceCard
          title={"ReOps"}
          imageSrc={"/Images/tailor.png"}
          altText={"tailer"}
          description={
            "Facilitating the blend of AI and DevOps, Valueans offers Web App Development services that accelerate the building, testing, and releasing of your software in a more efficient and reliable way. ReOps integrates with established DevOps practices, enhancing them without disruption. It uses current infrastructure and workflows, making sure that DevOps practitioners transition naturally and gradually. Using the power of AI, we make sure your specifications align perfectly with what we have in store. We combine human creativity and AI capabilities to deliver what’s best for you."
          }
        />
        <ServiceCard
          title={"Frontend"}
          imageSrc={"/Images/Frontend.png"}
          altText={"Frontend"}
          description={
            "Using the latest frameworks, our frontend developers design an aesthetically pleasing and feature-rich frontend that caters to your needs. We never compromise on the quality of the website, the performance, and product optimization."
          }
        />
        <ServiceCard
          title={"Third-Party Integration"}
          imageSrc={"/Images/tailor.png"}
          altText={"tailer"}
          description={
            "At Valuens, we assist you in integrating external systems with your web application using custom-built APIs, allowing you to expand and improve its current features."
          }
        />
        <ServiceCard
          title={"Backend"}
          imageSrc={"/Images/tailor.png"}
          altText={"tailer"}
          description={
            "Our highly skilled backend developers develop the backend using languages like Python, Ruby, PHP, or Node.js, while databases may be managed using SQL or NoSQL languages. At Valueans, we make sure that our clients are 100% satisfied, which is why we keep them in the loop throughout the project development."
          }
        />
        <ServiceCard
          title={"Maintenance & Support"}
          imageSrc={"/Images/tailor.png"}
          altText={"tailer"}
          description={
            "As a web development firm, we not only deal with development solutions but also keep up with the continuous maintenance of your web app. We take full responsibility for the support you might need post-development while keeping you updated."
          }
        />
      </div>
    </div>
  );
};

export default Web_features;
