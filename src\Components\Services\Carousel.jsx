"use client";
import { useState } from "react";
import Card_holder from "../Card/Card_holder";

const cards = [
  {
    id: 1,
    title1: "Customer Service Automation",
    description1:
      "Machine learning can significantly enhance customer service by automating routine tasks and providing intelligent support. Companies integrate ML-powered chatbots to handle common customer inquiries, improving response times and customer satisfaction.",
    title2: "Chatbots and Virtual Assistants",
    description2:
      "Use natural language processing (NLP) to understand and respond to customer queries, providing instant support and freeing up human agents for more complex issues.",
    title3: "Sentiment Analysis",
    description3:
      "Analyze customer feedback and interactions to gauge sentiment and identify areas for improvement.",
    bgcolor_1: "bg-purple-200",
    bgcolor_2: "bg-pink-200",
  },
  {
    id: 2,
    title1: "Operational Efficiency",
    description1:
      "Manufacturing companies use ML to predict equipment failures, allowing for proactive maintenance and reducing unplanned downtime. Machine learning can streamline business operations and improve efficiency in various ways.",
    title2: "Process Optimization",
    description2:
      "Identify inefficiencies in business processes and recommend improvements based on data analysis.",
    title3: "Predictive Maintenance",
    description3:
      "Monitor equipment and machinery to predict failures before they occur, reducing downtime and maintenance costs.",
    bgcolor_1: "bg-pink-200",
    bgcolor_2: "bg-purple-200",
  },
  {
    id: 3,
    title1: "Human Resources and Talent Management",
    description1:
      "Machine learning helps predict employee turnover, streamline recruitment processes, and analyze workforce productivity.",
    title2: "Recruitment",
    description2:
      "Automate resume screening and candidate matching to find the best-fit candidates quickly and efficiently.",
    title3: "Employee Retention",
    description3:
      "Analyze employee data to identify factors contributing to turnover and develop strategies to improve retention.",
    bgcolor_1: "bg-purple-200",
    bgcolor_2: "bg-pink-200",
  },
];

const Carousel = () => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const handlePrev = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? cards.length - 1 : prevIndex - 1
    );
  };

  const handleNext = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === cards.length - 1 ? 0 : prevIndex + 1
    );
  };

  return (
    <div className="relative w-full max-w-5xl mx-auto">
      {/* Cards Container */}
      <div className="overflow-hidden relative">
        <div
          className="flex transition-transform duration-500 ease-in-out"
          style={{
            transform: `translateX(calc(-${currentIndex * 100}% - ${currentIndex * 16}px))`,
          }}
        >
          {cards.map((card, index) => (
            <div
              key={card.id}
              className={`flex-shrink-0 w-80 px-4 ${
                index === currentIndex
                  ? "scale-100 opacity-100"
                  : "scale-95 opacity-75"
              } transform transition-all duration-500`}
            >
              <Card_holder
                title1={card.title1}
                description1={card.description1}
                title2={card.title2}
                description2={card.description2}
                title3={card.title3}
                description3={card.description3}
                bgcolor_1={card.bgcolor_1}
                bgcolor_2={card.bgcolor_2}
              />
            </div>
          ))}
        </div>
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between mt-4">
        {/* Prev Button */}
        <button
          onClick={handlePrev}
          className="w-12 h-12 flex items-center justify-center bg-purple-500 text-white rounded-full shadow-lg hover:bg-purple-600 transition"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            strokeWidth={2}
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M15 19l-7-7 7-7"
            />
          </svg>
        </button>

        {/* Next Button */}
        <button
          onClick={handleNext}
          className="w-12 h-12 flex items-center justify-center bg-purple-500 text-white rounded-full shadow-lg hover:bg-purple-600 transition"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            strokeWidth={2}
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M9 5l7 7-7 7"
            />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default Carousel;
