import Image from "next/image";
import Link from "next/link";
import React from "react";

const PortfolioCard = ({ imageSrc, title, description, navLink }) => {
  return (
    <Link href={navLink} passHref>
      <div className="block max-w-sm cursor-pointer">
        {/* Image Wrapper with Border */}
        <div className="w-[344px] h-[200px] p-2 rounded-lg bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500">
          <div className="relative w-full h-full rounded-lg overflow-hidden">
            <Image
              src={imageSrc}
              alt={title}
              layout="fill"
              objectFit="cover"
              className="rounded-lg"
            />
          </div>
        </div>

        {/* Content */}
        <div className="p-1">
          <h5 className="mb-2 text-lg md:text-2xl font-bold tracking-tight text-center">
            {title}
          </h5>
          <p className="mb-2 text-sm md:text-base font-light text-justify">
            {description}
          </p>
        </div>
      </div>
    </Link>
  );
};

export default PortfolioCard;
