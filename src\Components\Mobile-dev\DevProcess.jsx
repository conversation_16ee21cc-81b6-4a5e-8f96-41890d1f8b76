import React from "react";
import ServiceLifecycleCard from "../Services/ServiceLifecycleCard";
import ServiceCount from "../Services/ServiceCount";
import Connector1 from "../Connectors/Connector1";
import Connector2 from "../Connectors/Connector2";
import Connector3 from "../Connectors/Connector3";
import Connector4 from "../Connectors/Connector4";
import Connector5 from "../Connectors/Connector5";
import Connector6 from "../Connectors/Connector6";
import Connector7 from "../Connectors/Connector7";
import Connector9 from "../Connectors/Connector9";

const Devprocess = () => {
  const discoverYourNeedsItems = [
    "Understand and communicate your expectations from Valueans",
    "Discuss the features, UI/UX, user flow, and other technical implications",
  ];

  const appDevelopment = [
    "Identify competitors",
    "Establish your goals and objectives",
  ];

  const wireframingDesigningItems = [
    "Prepare blueprint structure",
    "Wireframe each screen",
    "Application Designing",
  ];

  const development = [
    "Coding the app",
    "Layout main sections",
    "Ask for your feedback",
  ];

  const testingItems = [
    "Conduct the app testing",
    "Make sure it's 100% bug and error-free",
    "Ask for your final approval",
  ];

  return (
    <div className="max-w-[90vw] mx-auto my-24">
      <h2 className="text-4xl text-center font-semibold mb-4">
        Our Mobile App <br /> Development{" "}
        <span className="text-[#7716BC]">Process</span>
      </h2>
      <p className="text-center text-xl font-medium my-10">
        We plan and employ the most effective technique for the rapid building
        of exclusive technology solutions while keeping your needs in the loop.
      </p>
      <div className="relative">
        <div className="flex justify-center items-center gap-8 mb-20 ">
          <div className="flex-col justify-center items-center">
            <ServiceLifecycleCard
              title="Discover Your Needs"
              items={discoverYourNeedsItems}
            />
            <ServiceCount count={"1"} />
          </div>
          <div className="flex-col justify-center items-center">
            <ServiceLifecycleCard
              title="Wireframing & Designing"
              items={wireframingDesigningItems}
            />
            <ServiceCount count={"2"} className="z-10" />
          </div>
          <div className="flex-col justify-center items-center">
            <ServiceLifecycleCard title="Testing" items={testingItems} />
            <ServiceCount count={"3"} />
          </div>
        </div>
        <div className="absolute top-[53%] left-[33%]">
          <Connector9 />
        </div>
        <div className="absolute top-[55%] left-[45%]">
          <Connector4 />
        </div>
        <div className="absolute top-[50%] left-[58%]">
          <Connector5 />
        </div>
        <div className="absolute top-[54%] left-[71%]">
          <Connector6 />
        </div>
        <div className="absolute top-[54%] left-[79%]">
          <Connector7 />
        </div>
        <div className=" ml-[15%] flex justify-center items-center gap-8 mt-20 relative">
          <div className="flex-col justify-center items-center absolute left-[14%]">
            <ServiceCount count={"4"} />
            <ServiceLifecycleCard
              title="App Development Consultation"
              items={appDevelopment}
            />
          </div>
          <div className="flex-col justify-center items-center ml-[35%]">
            <ServiceCount count={"5"} />
            <ServiceLifecycleCard title="Development" items={development} />
          </div>
          <div className="flex-col justify-center items-center ">
            <ServiceCount count={"6"} />
            <ServiceLifecycleCard title="Dvelopment" items={development} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Devprocess;
