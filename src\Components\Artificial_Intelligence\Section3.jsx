import Image from "next/image";
import React from "react";

const Section3 = () => {
  return (
    <section className="bg-pink-100">
      <div className="max-w-[85%] mx-auto py-8 flex justify-center items-center gap-10">
        <div className="flex-1">
          <h2 className="text-4xl font-semibold leading-[57px] ">
            Why <span>Valueans?</span>
          </h2>

          <p className="text-lg leading-7 font-normal">
            From healthcare and insurance to retail and finance, we always make
            sure that the solutions we create cater to the uniqueness of each
            sector and are in line with industry best practices.
          </p>
          <p className="text-lg leading-7 font-normal my-4">
            From healthcare and insurance to retail and finance, we always make
            sure that the solutions we create cater to the uniqueness of each
            sector and are in line with industry best practices. With years of
            experience in development, we understand the importance of data
            quality and effective model training which is why we offer 100%
            effective machine learning solutions that are tailored to your
            requirements.
          </p>
          <p className="text-lg leading-7 font-normal">
            At Valueans, your time, money, and opinions are valued–so we make
            sure you’re always in the loop with us while we bring your vision to
            reality. We focus on delivering the product 3x faster, better, and
            cheaper which is why we offer:
          </p>
          <ul className="list-disc list-inside mt-4">
            <li>Cross-Industry ML Expertise</li>
            <li>Flexible Engagement Models</li>
            <li>Agile Development</li>
            <li>Tailored Approach</li>
            <li>Process Transparency</li>
          </ul>
        </div>
        <div className="flex-1 w-[536px] h-[450px] relative">
          <Image
            src="/Images/custom_software_2_1.png"
            alt="custom"
            layout="fill"
            objectFit="cover" // or "contain" based on your requirement
          />
        </div>
      </div>
    </section>

    // From healthcare and insurance to retail and finance, we always make sure that the solutions we create cater to the uniqueness of each sector and are in line with industry best practices. With years of experience in development, we understand the importance of data quality and effective model training which is why we offer 100% effective machine learning solutions that are tailored to your requirements.  At Valueans, your time, money, and opinions are valued–so we make sure you’re always in the loop with us while we bring your vision to reality. We focus on delivering the product 3x faster, better, and cheaper which is why we offer: Cross-Industry ML Expertise Flexible Engagement Models Agile Development Tailored Approach Process Transparency
  );
};

export default Section3;
