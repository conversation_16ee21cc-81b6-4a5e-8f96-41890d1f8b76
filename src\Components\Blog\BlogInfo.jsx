// components/BlogsCard.jsx

import Image from "next/image";
import Link from "next/link";
import React from "react";

const BlogsCard = ({ imageSrc, title, slug, date }) => {
  return (
    <Link href={slug ? `/blog/${slug}` : "/blog"} passHref>
      <div className="w-[163px] md:w-[273px] h-[305px] md:min-h-[380px] bg-white border border-gray-200 rounded-lg shadow p-2 md:p-4 cursor-pointer hover:shadow-lg transition-shadow duration-300">
        <div className="w-[139px] md:w-[242px] h-[112px] md:h-[157px] relative">
          <Image
            className="rounded-t-lg"
            src={imageSrc}
            alt="blog"
            layout="fill"
            objectFit="contain"
          />
        </div>
        <div>
          <div className="w-full mt-6">
            <p className="mb-3 font-medium text-base md:text-xl text-gray-700 w-auto">
              {title}
            </p>
          </div>
          <div className="flex flex-col md:flex-row justify-center md:justify-between align-center border-t-2 border-gray-100">
            <span className="text-[#232536] text-sm font-light inline-flex items-center">
              {date}
            </span>
            <span className="inline-flex items-center px-3 py-1 md:py-2 text-sm font-medium text-center rounded-lg">
              Read more
              <svg
                className="rtl:rotate-180 w-3.5 h-3.5 ms-2"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 14 10"
              >
                <path
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M1 5h12m0 0L9 1m4 4L9 9"
                />
              </svg>
            </span>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default BlogsCard;
