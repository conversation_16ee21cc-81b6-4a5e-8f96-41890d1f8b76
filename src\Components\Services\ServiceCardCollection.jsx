import React from "react";
import ServiceCard from "./ServiceDevCard";

const ServiceCardCollection = () => {
  return (
    <div className="flex justify-center items-center gap-10 my-24">
      <ServiceCard
        title={"Tailored solutions"}
        imageSrc={"/Images/tailor.png"}
        altText={"tailer"}
        description={
          "Imagine having a digital platform, where you can optimize, control and manage your business according to your needs. Sounds cool, right? Exactly! This is why 48% of companies have their own customized mobile apps that are specifically made in a way to help your business grow and interact with clients."
        }
      />
      <ServiceCard
        title={"Better user experience $ engagement"}
        imageSrc={"/Images/UX.png"}
        altText={"UX"}
        description={
          "A client focused, personalized, and easy-to-use mobile app can rapidly grow your business as it attracts a large audience and gives them a platform to interact with. The enhanced user experience and engagement provided by custom mobile app development companies play a crucial role in building brand loyalty and a strong user base."
        }
      />
      <ServiceCard
        title={"Integration with existing systems"}
        imageSrc={"/Images/integrate.png"}
        altText={"Integrate"}
        description={
          "Another advantage of custom mobile app development companies is their ability to integrate with your existing systems. This integration allows a smooth data flow and enhances overall efficiency in various business processes."
        }
      />
    </div>
  );
};

export default ServiceCardCollection;
