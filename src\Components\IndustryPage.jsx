"use client";
import React, { useEffect, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/pagination";
import { Pagination } from "swiper/modules";

import Image from "next/image";
import Heading from "./Heading/Heading";

const IndustryExpertiseCard = ({ imageSrc, altText, title, description }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Detect screen width to determine if device is mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  const toggleExpansion = () => {
    if (isMobile) {
      setIsExpanded((prev) => !prev);
    }
  };

  return (
    <div className="relative max-w-sm mx-auto my-10">
      {/* Image Container */}
      <div className="relative w-full h-[200px] md:h-[250px] rounded-lg overflow-hidden">
        <Image
          src={imageSrc}
          alt={altText}
          layout="fill"
          objectFit="cover"
          className="rounded-lg"
        />

        {/* Mobile: Always visible overlay */}
        {isMobile && (
          <div className="absolute inset-0 bg-black bg-opacity-40 flex items-end">
            <div
              onClick={toggleExpansion}
              className={`w-full p-4 bg-white bg-opacity-95 backdrop-blur-sm cursor-pointer transition-all duration-300 ${
                isExpanded ? 'min-h-[85%]' : 'min-h-[40%]'
              } flex flex-col`}
            >
              <h5 className="mb-2 text-lg font-bold tracking-tight text-gray-900 sticky top-0 bg-white bg-opacity-95">
                {title}
              </h5>
             
              <p
                className={`text-sm text-gray-700 transition-all duration-300 overflow-y-auto flex-grow ${
                  isExpanded ? '' : 'line-clamp-2'
                }`}
                dangerouslySetInnerHTML={{ __html: description }}
              />
            </div>
          </div>
        )}
      </div>

      {/* Desktop: Hover overlay */}
      {!isMobile && (
        <div className="absolute top-[120px] left-[16px] max-w-[calc(100%-32px)] p-4 bg-white rounded-lg shadow-lg transition-all duration-300 hover:z-20 hover:bg-white hover:shadow-xl hover:scale-105">
          <h5 className="mb-2 text-lg font-bold tracking-tight text-gray-900">
            {title}
          </h5>
          
          <p
          className="text-sm text-gray-600 transition-all duration-300 line-clamp-3 hover:line-clamp-none hover:overflow-auto hover:max-h-32"
          dangerouslySetInnerHTML={{ __html: description }}
        />
        </div>
      )}
    </div>
  );
};

const expertiseData = [
  {
    imageSrc: "/Images/expertise1.png",
    altText: "Healthcare",
    title: "Healthcare",
    description:
      "The healthcare industry requires precision, security, and efficiency to <a href='/Industries/Healthcare' class='text-[#7716BC] hover:underline'> manage patient data</a> and streamline operations. Valueans delivers tailored solutions to enhance care, optimize workflows, and ensure regulatory compliance for better outcomes and smoother operations.",
  },
  {
    imageSrc: "/Images/expertise2.png",
    altText: "Logistics",
    title: "Logistics",
    description:
      "In logistics, efficiency and <a href='/Industries/Logistics' class='text-[#7716BC] hover:underline'>real-time tracking</a> are crucial for smooth supply chain operations. Valueans delivers innovative solutions to enhance route planning, inventory tracking, and real-time analytics. Our logistics experts optimize your logistics network for maximum efficiency and scalability.",
  },
  {
    imageSrc: "/Images/expertise3.png",
    altText: "Travel",
    title: "Travel",
    description:
      "The travel industry thrives on seamless <a href='/Industries/Travel' class='text-[#7716BC] hover:underline'> booking experiences and personalized customer solutions</a>. Valueans offers technology platforms that ensure user-friendly experiences, optimize booking systems, and enhance overall efficiency while delivering exceptional travel services and managing operations effectively.",
  },
  {
    imageSrc: "/Images/expertise4.png",
    altText: "Construction",
    title: "Construction",
    description:
      "The <a href='/Industries/Construction' class='text-[#7716BC] hover:underline'> construction industry</a> benefits from innovative project management tools, ensuring seamless collaboration. Valueans provides solutions for project planning, resource allocation, safety compliance, and reducing costs, ensuring your construction projects are completed on time and within budget.",
  },
  {
    imageSrc: "/Images/expertise5.png",
    altText: "Manufacturing",
    title: "Manufacturing",
    description:
      "With over 6 years of experience in providing on-demand solutions, we deliver solutions that <a href='/Industries/Manufacturing' class='text-[#7716BC] hover:underline'> optimize production processes</a>, improve quality controls, and refine supply chain visibility. Helping your business thrive by reducing downtime and enhancing output efficiency.",
  },
  {
    imageSrc: "/Images/expertise6.png",
    altText: "Agriculture",
    title: "Agriculture",
    description:
      "Valueans offers <a href='/Industries/Agriculture' class='text-[#7716BC] hover:underline'> smart solutions for precision agriculture</a>, farm management, and data analytics, helping you streamline agricultural processes. Our solutions empower you to optimize yields and operations with advanced technology and customized services.",
  },
  {
    imageSrc: "/Images/expertise7.png",
    altText: "Oil and Gas",
    title: "Oil and Gas",
    description:
      "The <a href='/Industries/Oil_And_Gas' class='text-[#7716BC] hover:underline'> oil and gas industry</a> requires robust systems for exploration, production, and delivery while enhancing safety and reducing costs. Valueans provides advanced tools to monitor operations, improve safety standards, and support your efforts to manage complex operations and drive innovation.",
  },
  {
    imageSrc: "/Images/automotive.png",
    altText: "Automotive",
    title: "Automotive",
    description:
      "We understand that the <a href='/Industries/Automotive' class='text-[#7716BC] hover:underline'> automotive industry</a> is evolving with connected vehicles and smart technologies. Thus, we provide innovative solutions for vehicle telematics, IoT integration, and advanced analytics, helping you stay at the forefront of automotive technology and improve vehicle performance.",
  },
  {
    imageSrc: "/Images/banking.png",
    altText: "Banking",
    title: "Banking",
    description:
      "In banking, security and user experience are critical. If you're looking for cutting-edge solutions for secure transactions, <a href='/Industries/Banking' class='text-[#7716BC] hover:underline'> digital banking platforms</a>, and compliance management, choose Valueans because we make sure your banking systems are reliable, user-friendly, and equipped to handle evolving financial needs.",
  },
  {
    imageSrc: "/Images/ecommerce111.png",
    altText: "E-Commerce",
    title: "E-Commerce",
    description:
      "E-commerce businesses need compelling, user-friendly platforms to <a href='/Industries/E-Commerce' class='text-[#7716BC] hover:underline'> drive sales and customer satisfaction</a>. Valueans builds dynamic, responsive e-commerce solutions that enhance user experience, streamline transactions, and integrate with your broader business systems for optimal performance.",
  },
  {
    imageSrc: "/Images/education111.png",
    altText: "Education",
    title: "Education",
    description:
      "We believe that education technology needs to be engaging and accessible to everyone.  We help educational institutions create dynamic, interactive environments that foster student success and develop <a href='/Industries/Education' class='text-[#7716BC] hover:underline'> innovative e-learning platforms</a> and administrative tools that enhance teaching and learning experiences.",
  },
  {
    imageSrc: "/Images/finance111.png",
    altText: "Finance",
    title: "Finance",
    description:
      "In finance, <a href='/Industries/Finance' class='text-[#7716BC] hover:underline'> data accuracy and security</a> are paramount, which is why Valueans offers tailored solutions for secure transactions, compliance, and financial data management. We ensure your systems are robust, scalable, and capable of handling complex financial operations with ease.",
  },
  {
    imageSrc: "/Images/gaming111.png",
    altText: "Gaming",
    title: "Gaming",
    description:
      "Gaming requires high performance and engaging user experiences, which is why you should choose Valueans because we specialize in creating immersive, <a href='/Industries/Gaming' class='text-[#7716BC] hover:underline'>scalable gaming solutions</a> that capture player interest and deliver smooth gameplay. We bring your creative visions to life with cutting-edge technology and robust infrastructure.",
  },
  {
    imageSrc: "/Images/life-insurance.png",
    altText: "Insurance",
    title: "Insurance",
    description:
      "The <a href='/Industries/Insurance' class='text-[#7716BC] hover:underline'>insurance industry</a> relies on accurate data and efficient claims processing to address this we provide tailored solutions for policy management, risk assessment, and customer service. Our expertise helps you optimize operations and deliver better experiences for your clients.",
  },
  {
    imageSrc: "/Images/real-estate11.png",
    altText: "Real Estate",
    title: "Real Estate",
    description:
      "Real estate professionals need tools for <a href='/Industries/Real_Estate' class='text-[#7716BC] hover:underline'>efficient property management</a> and client engagement. Therefore, Valueans brings you smart real estate solutions that streamline property listings, client interactions, and data management, helping you stay competitive and provide exceptional service.",
  },
  {
    imageSrc: "/Images/social-networking11.png",
    altText: "Social networking",
    title: "Social Networking",
    description:
      "<a href='/Industries/Social_Networking' class='text-[#7716BC] hover:underline'>Social networking platforms</a> thrive on engaging, user-friendly experiences and seamless connectivity. Valueans delivers customized solutions to enhance user interaction, manage large volumes of data, and integrate social features that foster community building and user engagement, which sets your online presence apart from your competitors.",
  },
  {
    imageSrc: "/Images/telecom11.png",
    altText: "Telecom",
    title: "Telecom",
    description:
      "If you're looking for scalable solutions for <a href='/Industries/Telecom' class='text-[#7716BC] hover:underline'>network management</a> and customer service, then you're at the right place because Valueans offers advanced technology to optimize network performance, manage customer interactions, and support new service offerings, keeping you ahead in a competitive industry.",
  },
];

export default function IndustryPage() {
  return (
    <div className="container md:mb-10">
      <Heading>
        Our <span className="text-[#7716BC]">Industry</span> Expertise
      </Heading>
      <div className="relative px-4 flex flex-col md:flex-row justify-center items-center">
        <Swiper
          // Default to 1 slide per view (mobile)
          slidesPerView={1}
          spaceBetween={30}
          grabCursor={true}
          pagination={{
            clickable: true,
            dynamicBullets: true,
          }}
          modules={[Pagination]}
          className="w-full relative pb-16 md:pb-10"
          style={{
            '--swiper-pagination-color': '#7716BC',
            '--swiper-pagination-bullet-inactive-color': '#999999',
            '--swiper-pagination-bullet-inactive-opacity': '0.4',
            '--swiper-pagination-bullet-size': '8px',
            '--swiper-pagination-bullet-horizontal-gap': '4px'
          }}
          // Display 3 slides on larger screens
          breakpoints={{
            768: {
              slidesPerView: 3,
              spaceBetween: 20,
            },
            1024: {
              slidesPerView: 3,
              spaceBetween: 30,
            },
          }}
        >
          {expertiseData.map((item, index) => (
            <SwiperSlide key={index} className="!h-auto">
              <IndustryExpertiseCard {...item} />
            </SwiperSlide>
          ))}
        </Swiper>
      </div>

      {/* Custom styles for mobile pagination positioning */}
      <style jsx global>{`
        .swiper-pagination {
          position: relative !important;
          margin-top: 20px !important;
          bottom: auto !important;
        }

        @media (max-width: 768px) {
          .swiper-pagination {
            margin-top: 30px !important;
          }
        }

        .swiper-pagination-bullet {
          transition: all 0.3s ease;
        }

        .swiper-pagination-bullet-active {
          transform: scale(1.2);
        }
      `}</style>
    </div>
  );
}
