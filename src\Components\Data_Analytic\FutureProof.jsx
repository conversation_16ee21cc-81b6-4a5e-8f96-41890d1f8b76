import React from "react";
import Card from "./Card";

const FutureProof = () => {
  const cardData = [
    {
      title: "AI and Automation",
      content: [
        "Automate routine tasks to focus on strategic activities.",
        "Use AI-driven insights to stay ahead of the competition.",
      ],
    },
    {
      title: "Data Democratization",
      content: [
        "Empower employees with self-service analytics tools.",
        "Foster a culture of data-driven decision-making across teams.",
      ],
    },
    {
      title: "Advanced predictive models",
      content: [
        "Move beyond historical analysis to forecast future trends.",
        "Enhance decision-making with real-time data insights.",
      ],
    },
    {
      title: "Sustainability Analytics",
      content: [
        "Use data to track and reduce environmental impact.",
        "Optimize resource usage for a greener future.",
      ],
    },
  ];

  return (
    <div className="w-[85%] mx-auto my-10 md:my-24 py-4 md:py-8">
      <h2 className="text-xl md:text-3xl md:leading-[40px] font-semibold text-center">
        Future-Proof Your Business with{" "}
        <span className="text-[#F245A1]">Valueans</span>
      </h2>
      <p className="text-base md:text-xl text-center mb-12">
        Data analytics service providers are constantly evolving, and staying
        ahead requires agility and innovation. At Valueans, we’re committed to
        helping you navigate this dynamic landscape with confidence.  
      </p>
      <h2 className="text-lg md:text-2xl md:leading-[40px] font-semibold text-[#7716BC] text-center">
        Emergency Trends in Data Analytics Service Companies
      </h2>
      <div className="flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6 my-3 md:my-7">
        {cardData.map((card, index) => (
          <Card
            key={index}
            title={card.title}
            content={card.content}
            className="border-purple-200 h-auto md:h-[300px]"
          />
        ))}
      </div>
    </div>
  );
};

export default FutureProof;
