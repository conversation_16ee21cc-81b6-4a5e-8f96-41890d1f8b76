"use client";

import React, { useState } from "react";

const Dropdown = ({ title, content }) => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className="w-80 border border-gray-200 rounded-lg mb-4 mx-auto">
      <button
        onClick={toggleDropdown}
        className="w-full p-4 text-left font-semibold rounded-t-lg transition-colors bg-gradient-to-r from-pink-500 to-purple-500 text-white"
      >
        {title}
        <span
          className={`float-right transform transition-transform ${
            isOpen ? "rotate-180" : ""
          }`}
        >
          {isOpen ? "▲" : "▼"}
        </span>
      </button>
      {isOpen && (
        <div className="p-4 bg-white shadow-md rounded-b-lg">
          <p className="text-gray-700">{content}</p>
        </div>
      )}
    </div>
  );
};

export default Dropdown;
