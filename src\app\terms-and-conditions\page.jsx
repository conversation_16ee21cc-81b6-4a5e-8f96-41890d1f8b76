"use client";
import React, { useEffect, useState } from "react";

const TermsAndConditions = () => {
  const [content, setContent] = useState("");
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchTerms = async () => {
      try {
        const response = await fetch("https://api.valueans.com/api/terms-and-condition/");
        if (response.ok) {
          const data = await response.json();
          setContent(data.content);
        } else {
          console.error("Failed to fetch terms and conditions");
        }
      } catch (error) {
        console.error("Error fetching terms and conditions:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTerms();
  }, []);

  return (
    <main className="min-h-screen">
      {/* Hero Section with Background Image */}
      <section
        className="pt-20 px-7 md:px-20 pb-20 bg-cover bg-center bg-no-repeat h-[50vh] w-full flex items-center justify-center"
        style={{
          backgroundImage: `url('/Images/HomePage-bg.png')`,
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      >
        <div className="text-center max-w-3xl">
          <h1 className="text-white text-4xl md:text-5xl font-trirong mb-6">
            Terms & Conditions
          </h1>
        </div>
      </section>

      {/* Content Section */}
      <section className="py-20 px-7 md:px-20">
        <div className="max-w-4xl mx-auto">
          {isLoading ? (
            <div className="text-center">Loading...</div>
          ) : (
            <div 
              className="prose max-w-none"
              dangerouslySetInnerHTML={{ __html: content }}
            />
          )}
        </div>
      </section>
    </main>
  );
};

export default TermsAndConditions; 