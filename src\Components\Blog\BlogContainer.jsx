import React from "react";
import BlogsCard from "./BlogInfo";

const BlogContainer = ({ blogs }) => {
  return (
    <div className="w-[50%] container mx-auto px-2">
      {" "}
      {/* Reduced padding on container */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
        {" "}
        {/* Reduced grid gap */}
        {blogs.map((blog, index) => (
          <div key={index} className="col-span-1">
            <BlogsCard
              imageSrc={blog.imageSrc}
              title={blog.title}
              slug={blog.slug}
              date={blog.date}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default BlogContainer;
