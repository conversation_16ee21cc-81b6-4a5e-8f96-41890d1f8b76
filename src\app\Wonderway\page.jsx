import Image from "next/image";
import React from "react";

const Page = () => {
  return (
    <>
      {/* Banner Section */}
      <section>
        <div className="relative max-w-screen max-h-screen overflow-hidden">
          <Image
            src="/Images/wonderway_mock.png"
            alt="banner"
            layout="responsive"
            width={1440} // Replace with your image's actual width
            height={500} // Replace with your image's actual height
            objectFit="contain"
          />
        </div>
      </section>

      {/* Main Content Section */}
      <section className="w-[90vw] mx-auto my-10">
        <h2 className="text-6xl font-semibold text-center">WonderWay</h2>
        <div className="flex flex-col md:flex-row justify-center items-center gap-12 my-8">
          <p className="text-2xl font-light md:w-1/2">
            At vero eos et accusamus et iusto odio dignissimos ducimus qui
            blanditiis praesentium voluptatum deleniti atque corrupti quos
            dolores et quas molestias excepturi sint occaecati cupiditate non
            provident, similique sunt in culpa qui officia deserunt mollitia
            animi, id est laborum et dolorum fuga. Et harum quidem rerum facilis
            est et expedita distinctio. Nam libero tempore, cum soluta nobis est
            eligendi optio cumque nihil impedit quo minus id quod maxime placeat
            facere possimus, omnis voluptas assumenda est
          </p>
          <div className="relative flex justify-center items-center">
            <div className="absolute top-0 w-[197px] h-[394px]">
              <Image
                src="/Images/wonderway_mock_1.png"
                alt="Mobile"
                layout="fill"
                objectFit="cover"
              />
            </div>
            <div className="relative w-[197px] h-[394px]">
              <Image 
                src="/Images/wonderway_mock_2.png"
                alt="Mobile"
                layout="fill"
                objectFit="cover"
              />
            </div>
            <div className="relative w-[197px] h-[394px]">
              <Image
                src="/Images/wonderway_mock_3.png"
                alt="Mobile"
                layout="fill"
                objectFit="cover"
              />
            </div>
          </div>
        </div>
      </section>

      {/* "What was delivered?" Section */}
      <section className="w-[85vw] mx-auto my-20 text-center">
        <h2 className="text-4xl font-semibold text-pink-400">
          What was delivered?
        </h2>
        <div className="flex justify-center items-center my-6">
          <div className="relative my-4 w-[340px] h-[617px]">
            <Image
              src="/Images/wonderway_mock_4.png"
              alt="mobile"
              layout="fill"
              objectFit="contain"
              width={340}
              height={617}
            />
          </div>
          <div className="relative my-4 w-[311px] h-[597px]">
            <Image
              src="/Images/wonderway_mock_5.png"
              alt="mobile"
              layout="fill"
              objectFit="contain"
              width={311}
              height={597}
            />
          </div>
          <div className="relative my-4 w-[324px] h-[615px]">
            <Image
              src="/Images/wonderway_mock_6.png"
              alt="mobile"
              layout="fill"
              objectFit="contain"
              width={324}
              height={615}
            />
          </div>
        </div>

        <h2 className="text-3xl font-medium mt-4">Android Application</h2>
      </section>
    </>
  );
};

export default Page;
