import Faq from "@/Components/Faq/Faq";
import Process from "@/Components/Cloud Services/process";
import Cardcarousel from "@/Components/Cloud Services/carousel_2";
import Section1 from "@/Components/AI/Section1";
import Section2 from "@/Components/Cloud Services/Section2";
import Section3 from "@/Components/Cloud Services/Section3";
import Section4 from "@/Components/Cloud Services/Section4";
import Section5 from "@/Components/Cloud Services/Section5";
import Section6 from "@/Components/Cloud Services/Section6";
export const metadata = {
  title: "Cloud managed data center services, we guarantee data safety",
  description: "With Managed Cloud Services at Valueans, we support AI and other contemporary workloads, become more adaptable, and steer clear of vendor lock-in.",
};
const page = () => {
  const accordionData = [
    {
      title: "In what clouds is this managed service available?",
      content: "At each stage of the cloud adoption lifecycle, managed cloud services are available for public, private, and hybrid clouds. In order to get assistance in identifying which cloud resources best meet their needs and to guarantee that setup is done correctly, enterprises frequently contract for services prior to migration.",
    },
    {
      title: "What does a managed service provider do?",
      content: "A managed service provider is a third-party company remotely managing an organization's IT infrastructure and end-user systems (MSP). Small and medium-sized businesses (SMBs), NGOs, and government agencies use MSPs to perform a specific set of everyday management duties.",
    },
    {
      title: "What is the difference between cloud services and managed cloud services?",
      content: "Servers, hardware, storage, networking, software, intelligence, analytics, and monitoring are all included in cloud computing. Managed cloud services keep an eye on every server and application in a single data center.",
    },
    {
      title: "What is a fully managed cloud service?",
      content: "This third-party organization may be in charge of managing the service in its entirety or in part. In a fully managed cloud service, a third party would handle help desk tickets, updates, and upgrades. However, there may be significant issues with a partially maintained cloud service that is managed by a third party.",
    },
    {
      title: "What is a major disadvantage of cloud services?",
      content: "Downtime is often cited as one of the primary disadvantages of cloud computing. Service disruptions are a permanent danger for cloud computing systems since they depend on the internet, and they might occur for a variety of reasons. The financial effect of service failures and disruptions is astounding.",
    },
    {
      title: "What is vendor lock-in in cloud computing?",
      content: "In the field of economics, vendor lock-in, also known as proprietary lock-in or customer lock-in, refers to a situation in which a client becomes reliant on a vendor for goods and services due to the high costs associated with switching vendors.",
    },
    {
      title: "What does cloud computing vendor lock-in mean?",
      content: "When a client gets dependent on a vendor for products and services because switching suppliers is expensive, this is referred to in economics as vendor lock-in, proprietary lock-in, or customer lock-in.",
    },
  ];
  return (
    <>
      <Section1
        backgroundImage={"/Images/cloud-bg.jpeg"}
        heading={"Managed Cloud Services"}
        bannerText={"Adaptability Starts with Cloud Managed Services "}
      />
      <Section2 />
      <Section3 />
      <Section4 />
      <Section5 />
      <Section6 />
      <Process />
      <section className="max-w-[90%] mx-auto my-10 md:my-24">
        <h2 className="text-xl md:text-3xl md:leading-[40px] font-semibold text-center">
          Business Benefits of {" "}
          <span className="text-[#F245A1]">Cloud Managed Services</span>{" "}
        </h2>
        <p className="text-base md:text-xl text-center capitalize mb-2 md:mb-5">
          A company may profit from cloud managed services in several ways, such as:       
        </p>
        {/* <Cardcarousel /> */}
        <Cardcarousel />
      </section>
      <Faq content={accordionData}/>
    </>
  );
};

export default page;
