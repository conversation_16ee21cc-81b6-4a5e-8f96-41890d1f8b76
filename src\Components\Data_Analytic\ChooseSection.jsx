import Image from "next/image";
import React from "react";
import InfoCard from "../Cloud Services/InfoCard";



const ChooseSection = () => {
  return (
    <section className="bg-pink-100 my-10 md:my-24 p-4 md:p-6">
      <div className="w-[90%] mx-auto">
        <div className="flex flex-col md:flex-row justify-center items-center my-5 md:my-10 gap-5 md:gap-40">
          <div className="w-full md:w-[50%]">
            <h2 className="text-xl md:text-3xl md:leading-[40px] font-semibold">
              Why Choose <span className="text-[#F245A1]">Valueans</span> for
              Data and Analytics Services? 
            </h2>
            <p className="text-base md:text-xl text-justify mt-2">
              Selecting the right partner for your Selecting the right partner for your Data Analytic services needs is crucial. At Valueans, we stand out for our ability, customer-center approach, and commitment to delivering measurable results. 
            </p>
          </div>
          <div className="">
            <Image
              src={"/Images/DataAndAnalytics.png"}
              alt="AI"
              width={335}
              height={335}
            />
          </div>
        </div>
        <h3 className="text-xl md:text-3xl md:leading-[40px] font-semibold text-center">
          Here’s What Sets Us Apart:
        </h3>
        <div className="flex flex-col md:flex-row justify-center md:justify-between gap-3 md:gap-10 my-5 md:my-10">
          <div className="flex flex-col justify-center items-center gap-3 md:gap-5">
            <InfoCard
              imgSrc="/Images/service_frame.png"
              altText="Tick"
              title="Tailored Solutions"
              description="No two businesses are alike, and neither are our solutions. We customize every engagement to meet your specific challenges and objectives."
            />
            <InfoCard
              imgSrc="/Images/service_frame.png"
              altText="Tick"
              title="Industry Expertise"
              description="With experience across sectors such as finance, healthcare, manufacturing, and retail, we bring deep domain knowledge to every project."
            />
            <InfoCard
              imgSrc="/Images/service_frame.png"
              altText="Tick"
              title="Innovative Technology"
              description="Our team stays at the forefront of innovation, leveraging the latest tools and platforms to deliver impactful results. "
            />
          </div>
          <div className="flex flex-col justify-center items-center gap-3 md:gap-5">
            <InfoCard
              imgSrc="/Images/service_frame.png"
              altText="Tick"
              title="Proven Track Record"
              description="Our clients consistently see tangible improvements in efficiency, decision making, and profitability."
            />
            <InfoCard
              imgSrc="/Images/service_frame.png"
              altText="Tick"
              title="End-to-End Partnership"
              description="From first strategy to implementation and support, we’re with you every step of the way."
            />
            <InfoCard
              imgSrc="/Images/service_frame.png"
              altText="Tick"
              title="Focus Results"
              description="We don’t just deliver projects; we deliver outcomes. Our solutions drive bona fide business value."
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default ChooseSection;
