"use client";
import React, { useState } from "react";
import { FaFacebook, FaInstagram, FaLinkedin } from "react-icons/fa";
import Image from "next/image";
import { ChevronDown, ChevronRight } from "lucide-react";
import { FaBars } from "react-icons/fa";
import Services from "./Services";
import Link from "next/link";
import Button from "../Buttons/Button";
import Technologies from "./Technologies";
import Industries from "./Industries";
import useSocialLinks from "@/hooks/useSocialLinks";
import LoadingSpinner from "../ui/LoadingSpinner";
import { useRouter } from "next/navigation";

const Navbar = () => {
  const [isServicesOpen, setIsServicesOpen] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isTechnologiesOpen, setIsTechnologiesOpen] = useState(false);
  const [isIndustriesOpen, setIsIndustriesOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const [openAccordions, setOpenAccordions] = useState({
    software: false,
    itConsulting: false,
    testing: false,
    dataAnalytics: false,
    design: false,
    application: false,
  });
  const { socialLinks, loading, error } = useSocialLinks();

  const toggleAccordion = (key) => {
    setOpenAccordions((prev) => ({ ...prev, [key]: !prev[key] }));
  };

  const handleNavigation = (href) => {
    setIsLoading(true);
    setIsServicesOpen(false);
    setIsTechnologiesOpen(false);
    setIsIndustriesOpen(false);
    setIsMenuOpen(false);
    router.push(href);
    // Ensure the loading spinner stays visible long enough for the page transition
    setTimeout(() => setIsLoading(false), 800);
  };

  return (
    <nav className="bg-white shadow-md">
      {isLoading && <LoadingSpinner />}
      <div className="max-w-screen-md mx-auto py-1 md:py-4 px-2 md:px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between md:justify-center h-16">
          {/* Logo */}
          <div onClick={() => handleNavigation("/")} className="flex-shrink-0 cursor-pointer">
            <Image
              src="/Images/logo.png"
              alt="Logo"
              width={123}
              height={79}
              className="w-auto h-[42px] md:h-[79px] object-contain"
            />
          </div>

          {/* Desktop Menu */}
          <div className="hidden md:block">
            <div className="flex justify-center items-center">
              <div className="mx-10 flex items-center text-base space-x-1 relative">
                <div
                  onClick={() => handleNavigation("/")}
                  className="font-poppins font-semibold text-gray-900 hover:text-[#7716BC] px-3 py-2 rounded-md cursor-pointer"
                >
                  Home
                </div>
                {/* Services with Dropdown */}
                <div
                  className="relative z-50"
                  onMouseEnter={() => setIsServicesOpen(true)}
                  onMouseLeave={() => setIsServicesOpen(false)}
                >
                  <button className="flex items-center font-semibold text-gray-900 hover:text-[#7716BC] px-3 py-2 rounded-md">
                    Services
                    <ChevronDown
                      size={20}
                      className={`ml-2 transition-transform ${isServicesOpen ? "rotate-180" : "rotate-0"}`}
                    />
                  </button>
                  {isServicesOpen && (
                    <div className="absolute left-0 w-[900px] bg-white border border-gray-200 rounded-md shadow-lg">
                      <Services onNavigate={handleNavigation} />
                    </div>
                  )}
                </div>
                <div
                  className="relative z-50"
                  onMouseEnter={() => setIsTechnologiesOpen(true)}
                  onMouseLeave={() => setIsTechnologiesOpen(false)}
                >
                  <button className="flex items-center font-poppins font-semibold text-gray-900 hover:text-[#7716BC] px-3 py-2 rounded-md">
                    Technologies
                    <ChevronDown
                      size={20}
                      className={`ml-2 transition-transform ${isTechnologiesOpen ? "rotate-180" : "rotate-0"}`}
                    />
                  </button>
                  {isTechnologiesOpen && (
                    <div className="absolute left-0 w-[800px] bg-white border border-gray-200 rounded-md shadow-lg">
                      <Technologies onNavigate={handleNavigation} />
                    </div>
                  )}
                </div>
                <div
                  className="relative z-50"
                  onMouseEnter={() => setIsIndustriesOpen(true)}
                  onMouseLeave={() => setIsIndustriesOpen(false)}
                >
                  <button className="flex items-center font-poppins font-semibold text-gray-900 hover:text-[#7716BC] px-3 py-2 rounded-md">
                    Industries
                    <ChevronDown
                      size={20}
                      className={`ml-2 transition-transform ${isIndustriesOpen ? "rotate-180" : "rotate-0"}`}
                    />
                  </button>
                  {isIndustriesOpen && (
                    <div className="absolute -left-3/4 w-[800px] bg-white border border-gray-200 rounded-md shadow-lg">
                      <Industries onNavigate={handleNavigation} />
                    </div>
                  )}
                </div>
                <div
                  onClick={() => handleNavigation("/portfolio")}
                  className="font-poppins font-semibold text-gray-900 hover:text-[#7716BC] px-3 py-2 rounded-md cursor-pointer"
                >
                  Portfolio
                </div>
                <div
                  onClick={() => handleNavigation("/blog")}
                  className="font-poppins font-semibold text-gray-900 hover:text-[#7716BC] px-3 py-2 rounded-md cursor-pointer"
                >
                  Blog
                </div>
                <div
                  onClick={() => handleNavigation("/contact")}
                  className="font-poppins font-semibold text-gray-900 hover:text-[#7716BC] px-3 py-2 rounded-md cursor-pointer"
                >
                  Contact
                </div>
              </div>
              <div onClick={() => handleNavigation("/estimate-cost")}>
                <Button>Get an Estimate</Button>
              </div>
              {socialLinks && (
                <div className="ml-2 pl-2 border-l-2 border-l-gray-800 flex justify-center items-center gap-6">
                  <Link href={socialLinks.facebook} target="_blank">
                    <FaFacebook className="w-4 md:w-6 h-4 md:h-6 hover:scale-110 transition-transform" />
                  </Link>
                  <Link href={socialLinks.instagram} target="_blank">
                    <FaInstagram className="w-4 md:w-6 h-4 md:h-6 hover:scale-110 transition-transform" />
                  </Link>
                  <Link href={socialLinks.linkedIn} target="_blank">
                    <FaLinkedin className="w-4 md:w-6 h-4 md:h-6 hover:scale-110 transition-transform" />
                  </Link>
                </div>
              )}
            </div>
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="flex items-center text-black px-4 py-2 rounded-md"
            >
              <FaBars size={20} />
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="md:hidden bg-white shadow-md">
          <div className="p-4 space-y-4">
            <div
              onClick={() => handleNavigation("/")}
              className="block font-poppins font-semibold text-[18px] leading-[27px] text-gray-900 hover:text-[#7716BC] cursor-pointer"
            >
              Home
            </div>
            {/* Services Dropdown in Mobile */}
            <div>
              <button
                onClick={() => setIsServicesOpen(!isServicesOpen)}
                className="flex justify-between items-center font-poppins font-semibold text-[18px] leading-[27px] text-gray-900 hover:text-[#7716BC] w-full"
              >
                Services
                <ChevronDown
                  size={20}
                  className={`ml-2 transition-transform ${
                    isServicesOpen ? "rotate-180" : "rotate-0"
                  }`}
                />
              </button>
              {isServicesOpen && (
                <div className="mt-2 pl-4 space-y-2">
                  {/* Software Development */}
                  <div className="font-semibold text-gray-900 text-sm mb-2">Custom Software Solutions</div>
                  <div
                    onClick={() => handleNavigation("/custom-software-development")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer pl-2"
                  >
                    Software Development
                  </div>
                  <div
                    onClick={() => handleNavigation("/custom-website-development")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer pl-2"
                  >
                    Custom Web Development
                  </div>
                  <div
                    onClick={() => handleNavigation("/saas-app-development")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer pl-2"
                  >
                    Saas Development
                  </div>
                  <div
                    onClick={() => handleNavigation("/Full_Stack_Development_Services")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer pl-2"
                  >
                    Full Stack Development
                  </div>
                  <div
                    onClick={() => handleNavigation("/mobile-app-development")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer pl-2"
                  >
                    Mobile App Development
                  </div>
                  <div
                    onClick={() => handleNavigation("/financial-app-development")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer pl-2"
                  >
                    Fintech
                  </div>
                  
                  <div
                    onClick={() => handleNavigation("/health-care-development")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer pl-2"
                  >
                    Healthcare Development
                  </div>

                  {/* IT Consulting */}
                  <div className="font-semibold text-gray-900 text-sm mt-4 mb-2">IT Consulting</div>
                  <div
                    onClick={() => handleNavigation("/product-management")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer pl-2"
                  >
                    Product Management
                  </div>
                  <div
                    onClick={() => handleNavigation("/AI")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer pl-2"
                  >
                    AI
                  </div>
                  <div
                    onClick={() => handleNavigation("/ML")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer pl-2"
                  >
                    ML
                  </div>
                  <div
                    onClick={() => handleNavigation("/App_Integration")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer pl-2"
                  >
                    Application Integration
                  </div>
                  <div
                    onClick={() => handleNavigation("/Cloud_Services")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer pl-2"
                  >
                    Cloud Services
                  </div>
                  <div
                    onClick={() => handleNavigation("/Business_Intelligence")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer pl-2"
                  >
                    Business Intelligence
                  </div>

                  {/* Testing & QA */}
                  <div className="font-semibold text-gray-900 text-sm mt-4 mb-2">Testing & QA</div>
                  <div
                    onClick={() => handleNavigation("/Quality_Assurance")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer pl-2"
                  >
                    Quality Assurance & Testing
                  </div>

                  {/* Data Analytics */}
                  <div className="font-semibold text-gray-900 text-sm mt-4 mb-2">Data Analytics</div>
                  
                  
                  <div
                    onClick={() => handleNavigation("/DataEngineering")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer pl-2"
                  >
                    Data Engineering
                  </div>
                  <div
                    onClick={() => handleNavigation("/Data_and_Analytics")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer pl-2"
                  >
                    Data Analytics
                  </div>
                  

                  {/* Design Services */}
                  <div className="font-semibold text-gray-900 text-sm mt-4 mb-2">Design Services</div>
                  <div
                    onClick={() => handleNavigation("/ui-ux")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer pl-2"
                  >
                    UI/UX Design
                  </div>
                 

                  {/* Application Services */}
                  <div className="font-semibold text-gray-900 text-sm mt-4 mb-2">Application Services</div>
                  
                  
                  <div
                    onClick={() => handleNavigation("/Dedicated_Deployment_teams")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer pl-2"
                  >
                    Dedicated Deployment Teams
                  </div>
                  <div
                    onClick={() => handleNavigation("/Maintenance_and_Support)")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer pl-2"
                  >
                    Maintenance and Support
                  </div>
                </div>
              )}
            </div>

            {/* Technologies Dropdown in Mobile */}
            <div>
              <button
                onClick={() => setIsTechnologiesOpen(!isTechnologiesOpen)}
                className="flex justify-between items-center font-poppins font-semibold text-[18px] leading-[27px] text-gray-900 hover:text-[#7716BC] w-full"
              >
                Technologies
                <ChevronDown
                  size={20}
                  className={`ml-2 transition-transform ${
                    isTechnologiesOpen ? "rotate-180" : "rotate-0"
                  }`}
                />
              </button>
              {isTechnologiesOpen && (
                <div className="mt-2 pl-4 space-y-2">
                  <div
                    onClick={() => handleNavigation("/Technologies/AI_ML")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer"
                  >
                    AI/ML
                  </div>
                  <div
                    onClick={() => handleNavigation("/Technologies/AR_VR")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer"
                  >
                    AR/VR
                  </div>
                  <div
                    onClick={() => handleNavigation("/Technologies/IOT")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer"
                  >
                    IoT
                  </div>
                  <div
                    onClick={() => handleNavigation("/Technologies/Cross_Platform_And_Hybrid_Development")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer"
                  >
                    Cross Platform & Hybrid
                  </div>
                  <div
                    onClick={() => handleNavigation("/Technologies/Low_Code_Development")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer"
                  >
                    Low Code Development
                  </div>
                  <div
                    onClick={() => handleNavigation("/Technologies/MicroServices")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer"
                  >
                    Microservices
                  </div>
                  <div
                    onClick={() => handleNavigation("/Technologies/Predictive_Analysis")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer"
                  >
                    Predictive Analyics
                  </div>
                  <div
                    onClick={() => handleNavigation("/Technologies/NLP")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer"
                  >
                    NLP
                  </div>
                  <div
                    onClick={() => handleNavigation("/Technologies/Progressive_Web_Apps")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer"
                  >
                    Progressive Web Apps
                  </div>
                </div>
              )}
            </div>

            {/* Industries Dropdown in Mobile */}
            <div>
              <button
                onClick={() => setIsIndustriesOpen(!isIndustriesOpen)}
                className="flex justify-between items-center font-poppins font-semibold text-[18px] leading-[27px] text-gray-900 hover:text-[#7716BC] w-full"
              >
                Industries
                <ChevronDown
                  size={20}
                  className={`ml-2 transition-transform ${
                    isIndustriesOpen ? "rotate-180" : "rotate-0"
                  }`}
                />
              </button>
              {isIndustriesOpen && (
                <div className="mt-2 pl-4 space-y-2">
                  <div
                    onClick={() => handleNavigation("/Industries/Healthcare")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer"
                  >
                    Healthcare
                  </div>
                  <div
                    onClick={() => handleNavigation("/Industries/Finance")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer"
                  >
                    Finance
                  </div>
                  <div
                    onClick={() => handleNavigation("/Industries/E-Commerce")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer"
                  >
                    E-Commerce
                  </div>
                  <div
                    onClick={() => handleNavigation("/Industries/Education")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer"
                  >
                    Education
                  </div>
                  <div
                    onClick={() => handleNavigation("/Industries/Manufacturing")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer"
                  >
                    Manufacturing
                  </div>
                  <div
                    onClick={() => handleNavigation("/Industries/Real_Estate")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer"
                  >
                    Real Estate
                  </div>
                  <div
                    onClick={() => handleNavigation("/Industries/Gaming")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer"
                  >
                    Gaming
                  </div>
                  <div
                    onClick={() => handleNavigation("/Industries/Insurance")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer"
                  >
                    Insurance
                  </div>
                  <div
                    onClick={() => handleNavigation("/Industries/Logistics")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer"
                  >
                    Logistics
                  </div>
                  <div
                    onClick={() => handleNavigation("/Industries/Oil_And_Gas")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer"
                  >
                    Oil & Gas
                  </div>
                  <div
                    onClick={() => handleNavigation("/Industries/Telecom")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer"
                  >
                    Telecom
                  </div>
                  <div
                    onClick={() => handleNavigation("/Industries/Travel")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer"
                  >
                    Travel
                  </div>
                  <div
                    onClick={() => handleNavigation("/Industries/Banking")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer"
                  >
                    Banking
                  </div>
                  <div
                    onClick={() => handleNavigation("/Industries/Construction")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer"
                  >
                    Construction
                  </div>
                  <div
                    onClick={() => handleNavigation("/Industries/Agriculture")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer"
                  >
                    Agriculture
                  </div>
                  <div
                    onClick={() => handleNavigation("/Industries/Social_Networking")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer"
                  >
                    Social Networking
                  </div>
                  <div
                    onClick={() => handleNavigation("/Industries/Automotive")}
                    className="block text-gray-900 text-sm hover:text-[#7716BC] cursor-pointer"
                  >
                    Automotive
                  </div>
                </div>
              )}
            </div>

            <div
              onClick={() => handleNavigation("/portfolio")}
              className="block font-poppins font-semibold text-[18px] leading-[27px] text-gray-900 hover:text-[#7716BC] cursor-pointer"
            >
              Portfolio
            </div>
            <div
              onClick={() => handleNavigation("/blog")}
              className="block font-poppins font-semibold text-[18px] leading-[27px] text-gray-900 hover:text-[#7716BC] cursor-pointer"
            >
              Blog
            </div>
            <div
              onClick={() => handleNavigation("/contact")}
              className="block font-poppins font-semibold text-[18px] leading-[27px] text-gray-900 hover:text-[#7716BC] cursor-pointer"
            >
              Contact
            </div>
          </div>
          <div className="p-4 border-t border-gray-200">
            <div className="mb-4" onClick={() => handleNavigation("/estimate-cost")}>
              <Button>Get an Estimate</Button>
            </div>
            {socialLinks && (
              <div className="flex justify-center items-center gap-6">
                <Link href={socialLinks.facebook} target="_blank">
                  <FaFacebook className="w-5 h-5 hover:scale-110 transition-transform" />
                </Link>
                <Link href={socialLinks.instagram} target="_blank">
                  <FaInstagram className="w-5 h-5 hover:scale-110 transition-transform" />
                </Link>
                <Link href={socialLinks.linkedIn} target="_blank">
                  <FaLinkedin className="w-5 h-5 hover:scale-110 transition-transform" />
                </Link>
              </div>
            )}
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;
