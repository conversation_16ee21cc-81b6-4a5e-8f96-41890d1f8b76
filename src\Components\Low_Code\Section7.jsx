import Image from "next/image";
import Button from "../Buttons/Button";

const Section7 = () => {
  return (
    <div className="mb-10 md:mb-24 md:py-8 md:px-[75px]">
      <div className="flex flex-col md:flex-row justify-center md:justify-between items-center">
        <div className="">
          <Image src={"/Images/tech.png"} alt="AI" width={400} height={400} />
        </div>
        <div className="w-full md:max-w-2xl">
          <section className="w-full  mx-auto p-4 border border-pink-500 rounded-2xl shadow-md mb-4 md:mb-8">
            <div className="py-5 flex flex-col gap-3  ">
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className="text-sm md:text-base">
                  Valueans facilitates communication amongst fusion teams and
                  quickly composes apps.
                </p>
              </div>

              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className="text-sm md:text-base">
                  We guarantee data uniformity across all platforms, apps, and
                  external systems.
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className="text-sm md:text-base">
                  Boost corporate apps by addressing security and compliance
                  issues creatively.
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className="text-sm md:text-base">
                  We create enterprise-level applications with less reliance on
                  IT.
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className="text-sm md:text-base">
                  You may reach your clients with your business applications far
                  more quickly than ever before. By reacting swiftly to consumer
                  needs and market changes, you may gain a competitive edge.
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className="text-sm md:text-base">
                  You may reach your clients with your business applications far
                  more quickly than ever before. By reacting swiftly to consumer
                  needs and market changes, you may gain a competitive edge.
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className="text-sm md:text-base">
                  Handle security and compliance aspects out of the box,
                  strengthening enterprise applications.
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className="text-sm md:text-base">
                  You may explore and maximize your experiences to do more in
                  shorter amounts of time.
                </p>
              </div>

              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className="text-sm md:text-base">
                  Simplify the development process to cut down on mistakes and
                  bad coding techniques.
                </p>
              </div>
            </div>
          </section>
          <Button bgColor="bg-[#7716BC]" hoverColor="hover:bg-purple-600">
            Get an Estimate
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Section7;
