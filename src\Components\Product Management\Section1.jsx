import React from "react";
import Image from "next/image";

const Section1 = () => {
  return (
    <section className="relative h-[60vh] md:h-screen">
      <div className="relative h-fit md:min-h-screen flex justify-center md:justify-end items-center mb-0 md:mb-10">
        <div className="absolute top-0 w-[430px] h-[375px] md:w-[465px] md:h-[550px]">
          <div className="w-full h-full">
            <Image
              src="/Images/purple_frame.png"
              alt="purple-block"
              layout="fill" // Fills the parent container
              className="object-cover" // Adjust the image to cover the container dimensions
            />
          </div>
        </div>
      </div>

      <div className="flex flex-col-reverse md:flex-row justify-center items-center space-x-4 mt-0 md:mt-8 gap-3 md:gap-6 max-w-[90%] mx-auto z-10 absolute top-8 left-3 md:left-[188px]">
        <div className="w-[90vw] md:w-[40%]">
          <h2 className="text-[28px] md:text-6xl leding-[42px] md:leading-[75px] text-center md:text-left font-bold capitalize">
            <span className="text-[#F245A1]">
              {" "}
              Product Management Consulting{" "}
            </span>
            Services   
          </h2>
        </div>
        <div className="relative z-10">
          <div className="w-[239.5px] h-[200px] md:w-[465px] md:h-[378px] ">
            <Image
              src="/Images/ML_banner.png"
              alt="Machine learning"
              layout="fill"
              objectFit="cover"
              className="rounded-md "
            />
          </div>
        </div>
      </div>

      <div className="bg-[#350668] w-full md:w-[60%] py-3 md:py-7 md:px-12  absolute bottom-[8%]  md:bottom-[93px]">
        <div className="w-[85%] mx-auto ml-9 md:ml-auto">
          <p className={`text-2xl md:text-3xl text-white`}>
            Hooking the ends of the customer with the anchor of success.
          </p>
        </div>
      </div>
    </section>
  );
};

export default Section1;
