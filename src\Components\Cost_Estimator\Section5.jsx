'use client'

import React, { useState } from 'react';
import Button from '../Buttons/Button';
import Toast from '../ui/Toast';
import useToast from '../../hooks/useToast';

const Section5 = () => {
  // Initial form data structure
  const [formData, setFormData] = useState({
    name: '',
    company_name: '',
    email: '',
    phone_number: '',
    industry_name: '',
    country: '',
    job_title: '',
    target_start_date: '',
    target_budget: '',
    hear_about: '',
    app_description: ''
  });

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prevState => ({
      ...prevState,
      [name]: value
    }));
  };

  // State for form submission status
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Toast notification hook
  const { toast, showSuccess, showError, hideToast } = useToast();

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    console.log('Form data being sent:', formData);

    try {
      const response = await fetch('https://api.valueans.com/api/estimate-with-expert/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        showSuccess("Thank you for your submission! We will contact you shortly.");
        setFormData({
          name: '',
          company_name: '',
          email: '',
          phone_number: '',
          industry_name: '',
          country: '',
          job_title: '',
          target_start_date: '',
          target_budget: '',
          hear_about: '',
          app_description: ''
        });
      } else {
        showError("Something went wrong. Please try again.");
      }
    } catch (error) {
      console.error("Error submitting the form:", error);
      showError("An error occurred. Please try again later.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div id="expert-form-section" className="w-[80%] bg-[#ffffff] mx-auto rounded-md mb-10 md:my-24 mt-10">
      <div className="flex p-2">
        <div
          className="md:w-[40%] rounded-md"
          style={{
            backgroundImage: `url(/Images/form-bg.png)`,
            backgroundSize: "cover",
            backgroundPosition: "center",
          }}
        ></div>
        <div className="md:w-[60%] p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="flex flex-col md:flex-row gap-6">
              <div className="flex flex-col flex-1">
                <label className="text-gray-600 mb-1">Name</label>
                <input
                  type="text"
                  name="name"
                  placeholder="Name"
                  className="border-0 border-b-2 border-gray-300 focus:border-b-[#F245A1] focus:outline-none py-2 px-1 transition-colors"
                  value={formData.name}
                  onChange={handleInputChange}
                />
              </div>
              <div className="flex flex-col flex-1">
                <label className="text-gray-600 mb-1">Company name</label>
                <input
                  type="text"
                  name="company_name"
                  placeholder="Company name"
                  className="border-0 border-b-2 border-gray-300 focus:border-b-[#F245A1] focus:outline-none py-2 px-1 transition-colors"
                  value={formData.company_name}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            <div className="flex flex-col md:flex-row gap-6">
              <div className="flex flex-col flex-1">
                <label className="text-gray-600 mb-1">Email</label>
                <input
                  type="email"
                  name="email"
                  placeholder="Email"
                  className="border-0 border-b-2 border-gray-300 focus:border-b-[#F245A1] focus:outline-none py-2 px-1 transition-colors"
                  value={formData.email}
                  onChange={handleInputChange}
                />
              </div>
              <div className="flex flex-col flex-1">
                <label className="text-gray-600 mb-1">Phone Number</label>
                <input
                  type="text"
                  name="phone_number"
                  placeholder="Phone Number"
                  className="border-0 border-b-2 border-gray-300 focus:border-b-[#F245A1] focus:outline-none py-2 px-1 transition-colors"
                  value={formData.phone_number}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            <div className="flex flex-col md:flex-row gap-6">
              <div className="flex flex-col flex-1">
                <label className="text-gray-600 mb-1">Industry Name</label>
                <input
                  type="text"
                  name="industry_name"
                  placeholder="Industry Name"
                  className="border-0 border-b-2 border-gray-300 focus:border-b-[#F245A1] focus:outline-none py-2 px-1 transition-colors"
                  value={formData.industry_name}
                  onChange={handleInputChange}
                />
              </div>
              <div className="flex flex-col flex-1">
                <label className="text-gray-600 mb-1">Country Region</label>
                <input
                  type="text"
                  name="country"
                  placeholder="Country Region"
                  className="border-0 border-b-2 border-gray-300 focus:border-b-[#F245A1] focus:outline-none py-2 px-1 transition-colors"
                  value={formData.country}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            <div className="flex flex-col md:flex-row gap-6">
              <div className="flex flex-col flex-1">
                <label className="text-gray-600 mb-1">Job Title</label>
                <input
                  type="text"
                  name="job_title"
                  placeholder="Job Title"
                  className="border-0 border-b-2 border-gray-300 focus:border-b-[#F245A1] focus:outline-none py-2 px-1 transition-colors"
                  value={formData.job_title}
                  onChange={handleInputChange}
                />
              </div>
              <div className="flex flex-col flex-1">
                <label className="text-gray-600 mb-1">Target Start Date</label>
                <input
                  type="date"
                  name="target_start_date"
                  placeholder="Target Start Date"
                  className="border-0 border-b-2 border-gray-300 focus:border-b-[#F245A1] focus:outline-none py-2 px-1 transition-colors"
                  value={formData.target_start_date}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            <div className="flex flex-col md:flex-row gap-6">
              <div className="flex flex-col flex-1">
                <label className="text-gray-600 mb-1">Total Project budget</label>
                <input
                  type="text"
                  name="target_budget"
                  placeholder="Project Budget"
                  className="border-0 border-b-2 border-gray-300 focus:border-b-[#F245A1] focus:outline-none py-2 px-1 transition-colors"
                  value={formData.target_budget}
                  onChange={handleInputChange}
                />
              </div>
              <div className="flex flex-col flex-1">
                <label className="text-gray-600 mb-1">How did you hear about Valueans</label>
                <input
                  type="text"
                  name="hear_about"
                  placeholder="How did you hear about us"
                  className="border-0 border-b-2 border-gray-300 focus:border-b-[#F245A1] focus:outline-none py-2 px-1 transition-colors"
                  value={formData.hear_about}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            <div className="flex flex-col mt-2">
              <label className="text-gray-600 mb-1">Tell us a little about the app you want to build</label>
              <input
                type="text"
                name="app_description"
                placeholder="Tell us about your app"
                className="border-0 border-b-2 border-gray-300 focus:border-b-[#F245A1] focus:outline-none py-2 px-1 transition-colors w-full"
                value={formData.app_description}
                onChange={handleInputChange}
              />
            </div>
            <div className="mt-8">
              <Button bgColor="bg-[#F245A1]" paddingX="px-8" paddingY="py-2" disabled={isSubmitting}>
                {isSubmitting ? 'Submitting...' : 'Submit'}
              </Button>
            </div>
          </form>
        </div>
      </div>

      {/* Toast Notification */}
      <Toast
        message={toast.message}
        type={toast.type}
        isVisible={toast.isVisible}
        onClose={hideToast}
      />
    </div>
  );
};

export default Section5;
