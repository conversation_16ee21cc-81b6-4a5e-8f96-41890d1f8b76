import React from "react";
import Card from "./Card";

const Section4 = () => {
  const cardData = [
    {
      title: "Contemporary Data Pipelines",
      description:
        "We create, build, and deploy production-quality end-to-end automated data pipelines. The data engineering consulting team at Valueans is well-versed in on-premises and <a href='/Cloud_Services' class='text-[#7716BC] hover:underline'> cloud-based automated</a> data pipeline installation.",
    },
    {
      title: "Data Preparation and ETL/ELT",
      description:
        "ETL (Extract, Transform, Load) and ELT (Extract, Load, Transform) aid in the loading, processing, and transformation of data into the necessary data model for advanced analytics and business reporting. These pipelines have been created by our Data Engineering team for several business divisions, including Supply Chain, Finance, and Sales.",

      bgColor: "bg-pink-100",
    },
    {
      title: "Implementation of Data Lakes",
      description:
        "The most innovative and potent solution for efficient processing and economic data storage is a data lake. Your organization may be able to grow its business data architecture if it adopts data lakes. Product traceability, customer data platforms, <a href='/Technologies/IOT' class='text-[#7716BC] hover:underline'> IoT data reporting</a>, and other client business issues have all been resolved by Valueans using Data Lake solutions.",
    },
    {
      title: "Architecture for Cloud Data",
      description:
        "At Valueans, we build and design adaptable and universally accessible company data infrastructures that are crucial in today's world. With their expertise from many big organizations, our Data Architects can assist your company in advancing its data analytics foundation. Check out our services for big data engineering!",
        bgColor: "bg-pink-100",},
    {
      title: "ML Engineering",
      description:
        "Boost your ML model lifecycle management and expedite AI initiatives' time to commercial value with our data engineering services & solutions. Store strong machine learning models that can efficiently scale across brands and regions to get from proof-of-concept to production.",
    },
    {
      title: "Cloud Transformation",
      description:
        "<a href='/Cloud_Services' class='text-[#7716BC] hover:underline'>Cloud transformation</a>  may reduce infrastructure costs and increase company agility. Our professionals evaluate and design the best cloud configuration for your company and assist with a smooth data transfer without compromising production quality and SLA.",
        bgColor: "bg-pink-100",
    },
    {
      title: "DataOps",
      description:
        "We help reduce downtime and minimize data threats through efficient business data management and governance. Use our tested DataOps services to test, monitor, and explore CI-CD pipelines, high availability, and enterprise data operations.",
    },
  ];

  return (
    <div className="w-[85%] mx-auto mb-10 md:mb-24">
      <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold capitalize">
        Thrive through data with next-gen{" "}
        <span className="text-[#F245A1]">data engineering solutions</span>
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 my-5">
        {cardData.map((card, index) => (
          <div 
            key={index}
            className={`${
              index === 6 ? "md:col-span-3 flex justify-center" : ""
            }`}
          >
            <Card
              title={card.title}
              description={card.description}
              bgColor={card.bgColor}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default Section4;
