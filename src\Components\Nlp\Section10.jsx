import React from "react";
import Heading from "../Heading/Heading";
import Button from "../Buttons/Button";
import Image from "next/image";

const Section10 = () => {
  return (
    <section className=" bg-[#7716BC] py-8">
      <div className="flex flex-col-reverse md:flex-row justify-center md:justify-between items-center w-[85vw] mx-auto ">
        <div className="">
          <Heading className="text-white text-center md:text-left">
            Get in Touch
          </Heading>
          <h2 className="text-center md:text-left text-xl md:text-2xl  font-semibold mt-2 md:mt-0"></h2>
          <p className="w-full md:w-1/3 text-base md:text-lg text-white font-normal m-1 mb-10">
            If you're looking to integrate NLP software development into your
            business operations, reach out to Valueans today. Contact us now for
            a consultation or a personalized demo to see our NLP services in 
            action.
          </p>
          <div className="w-fit mx-auto md:mx-0">
            <Button>Get an Estimate</Button>
          </div>
        </div>
        <div className="w-[234px] h-[232px] relative">
          <Image 
            src="/Images/estimate.png"
            alt="estimate"
            width={234}
            height={232}
            objectFit="cover"
          />
        </div>
      </div>
    </section>
  );
};

export default Section10;
