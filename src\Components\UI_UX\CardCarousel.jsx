"use client";

import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/pagination";
import { Pagination } from "swiper/modules";
import Card from "./Gradient_card";

const cardData = [
  {
    title: "UI/UX Consulting",
    description:
      "At Valueans, we help you gain a comprehensive understanding of advanced technology and develop the most suitable visual concepts following the goals of your business and the viewpoints of the users.",
    items: [],
  },
  {
    title: "SaaS Product Design",
    description:
      "We specialize in designing user-friendly and <a href='/saas-app-development' class='text-[#7716BC] hover:underline'> modern SaaS products</a> that leave users 100% satisfied. Our approach to SaaS product design combines deep industry knowledge with trendy designs to create a product that stands out in the market.",
    items: [],
  },
  {
    title: "Prototyping and Wireframing",
    description:
      "Our team carefully tests the usability of the final product before deploying it using fully interactive prototypes, data visualization, and interface designs. We design low-fidelity and high-fidelity wireframes to outline the layout and functionality of each screen.",
    items: [],
  },
  {
    title: "UI/UX for Native Apps",
    description:
      "Our deep understanding of native mobile user interfaces and experiences, along with our proficiency in both <a href='/mobile-app-development' class='text-[#7716BC] hover:underline'>iOS and Android mobile app development</a> , allows us to provide exceptional UI/UX development services especially mobile UI/UX design services that are tailored to your specific requirements.",
    items: [],
  },
  {
    title: "Visual Design and Branding",
    description:
      "Our visual design services focus on creating aesthetically pleasing and brand-consistent interfaces. We make sure that every design element aligns with your brand identity and appeals to your target audience. This includes:",
    items: [
      "Interface Designs",
      "Animated UI Designs",
      "Custom Icon Set Designs",
      "NFT Designs",
      "Design systems",
    ],
  },
  {
    title: "Enterprise Software Design",
    description:
      "Clear, flexible, and consistent enterprise system UI/UX can help you meet your target user needs, whether they’re related to increasing consumer interaction or modernizing business procedures.",
    items: [],
  },
  {
    title: "Software Redesigning",
    description:
      "Your current app isn't up to par? Don’t worry! We provide User Interface design services that will help you update its appearance and functionality for optimal usage and performance. We’ll make sure all features of your app are exceptional and align with your vision.",
    items: [],
  },
  {
    title: "Design for Cross-Platform Solutions",
    description:
      "Looking for <a href='/Technologies/Cross_Platform_And_Hybrid_Development' class='text-[#7716BC] hover:underline'> cross-platform solutions</a>? We got you! Our professionals are experts in creating a user interface (UI) that is responsive and has functionality that works across several platforms, providing a smooth experience for the user.",
    items: [],
  },
  // Add more cards here if needed
];

const CardCarousel = () => {
  return (
    <div className="relative px-4 flex flex-col md:flex-row justify-center items-center">
    <Swiper
        spaceBetween={30}
        grabCursor={true}
        slidesPerView={1}
         
         
          pagination={{
            clickable: true,
            dynamicBullets: true,
        }}
        className="w-full relative pb-16 md:pb-10"
          style={{
            '--swiper-pagination-color': '#7716BC',
            '--swiper-pagination-bullet-inactive-color': '#999999',
            '--swiper-pagination-bullet-inactive-opacity': '0.4',
            '--swiper-pagination-bullet-size': '8px',
            '--swiper-pagination-bullet-horizontal-gap': '4px'
          }}
        breakpoints={{
            768: {
              slidesPerView: 3,
              spaceBetween: 20,
            },
            1024: {
              slidesPerView: 3,
              spaceBetween: 30,
            },
          }}
     
        modules={[Pagination]}
        
    >
      {cardData.map((item, index) => (
        <SwiperSlide key={index}>
          <Card
            title={item.title}
            description={item.description}
            items={item.items}
            height={"md:h-[370px]"}
          />
        </SwiperSlide>
      ))}
    </Swiper>
    </div>
  );
};

export default CardCarousel;
