import React from "react";

const Section6 = () => {
  return (
    <section className="bg-purple-100 mb-10 md:mb-24 p-4 md:p-8">
      <div className="w-[90%] mx-auto flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6">
        <div className="flex-1">
          <h2 className="text-xl md:text-3xl md:leading-[40px] font-semibold">
            Why Choose <span className="text-[#F245A1]">Valueans</span> for
            Cloud Services
          </h2>
          <p className="text-base md:text-xl text-justify mt-2">
            It might be quite difficult to manage your cloud setup. Allow us to assist you in making your cloud complexity simpler. In order to help you create a scalable, adaptable cloud environment that meets your demands, advances your growth goals, and frees you up to concentrate on providing value, we collaborate with you to establish precise, affordable cloud management techniques. We have:
          </p>
        </div>
        <div className="flex-1">
          <section className="w-full  mx-auto p-4 border border-purple-800 rounded-2xl shadow-md">
            <div className="py-5 flex flex-col gap-3 md:gap-5 text-justify p-4">
              <div className="flex items-center gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className="text-sm md:text-lg">
                  Comprehensive knowledge of your company and its environment, which enables you to foresee and seize market opportunities and changing competitive landscapes
                </p>
              </div>
              <div className="flex items-center gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className="text-sm md:text-lg">
                  Complete business solutions driven by our Cloud maintained Services, including SAP, application migration, cloud native apps,<a href='/App_Integration' class='text-[#7716BC] hover:underline'> app integration</a>, big data, <a href='/Data_and_Analytics' class='text-[#7716BC] hover:underline'>data analytics</a>, IoT, and classic n-tier or legacy apps, configured and maintained to your unique needs and financial objectives.
                </p>
              </div>
              <div className="flex items-center gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className="text-sm md:text-lg">
                  Large-scale automation and self-service features that are flexible and reasonably priced so you can concentrate on providing value
                </p>
              </div>
              <div className="flex items-center gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className="text-sm md:text-lg">
                 Outstanding compliance and security features are included in our cloud managed services for a variety of sectors
                </p>
              </div>
              <div className="flex items-center gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className="text-sm md:text-lg">
                  A wide range of basic services that handle typical issues and correspond with your current cloud journey stage
                </p>
              </div>
              <div className="flex items-center gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className="text-sm md:text-lg">
                  World-class techniques, procedures, and tools combined with flexible pricing, integration, delivery, and management models offer on-demand scalability.
                </p>
              </div>
            </div>
          </section>
        </div>
      </div>
    </section>
  );
};

export default Section6;
