import Script from 'next/script';

const StructuredData = () => {
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Kapoor Software Solutions",
    "url": "https://kapoorsoftwaresolutions.com",
    "logo": "https://kapoorsoftwaresolutions.com/Images/logo/logo1.svg",
    "description": "Kapoor Software Solutions provides custom software development, mobile app development, web development, AI/ML solutions, and digital transformation services.",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "US"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+1-XXX-XXX-XXXX",
      "contactType": "customer service",
      "availableLanguage": "English"
    },
    "sameAs": [
      "https://www.linkedin.com/company/kapoor-software-solutions",
      "https://twitter.com/kapoorsoftware"
    ],
    "foundingDate": "2020",
    "numberOfEmployees": "50-100",
    "industry": "Software Development",
    "services": [
      "Custom Software Development",
      "Mobile App Development", 
      "Web Development",
      "AI/ML Solutions",
      "Cloud Services",
      "Digital Transformation"
    ]
  };

  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Kapoor Software Solutions",
    "url": "https://kapoorsoftwaresolutions.com",
    "description": "Custom software development services including mobile apps, web development, AI/ML solutions, and digital transformation.",
    "publisher": {
      "@type": "Organization",
      "name": "Kapoor Software Solutions"
    },
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://kapoorsoftwaresolutions.com/search?q={search_term_string}",
      "query-input": "required name=search_term_string"
    }
  };

  const serviceSchema = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "Custom Software Development Services",
    "description": "Professional custom software development, mobile app development, web development, and AI/ML solutions for businesses.",
    "provider": {
      "@type": "Organization",
      "name": "Kapoor Software Solutions",
      "url": "https://kapoorsoftwaresolutions.com"
    },
    "serviceType": "Software Development",
    "areaServed": "Worldwide",
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Software Development Services",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Custom Software Development"
          }
        },
        {
          "@type": "Offer", 
          "itemOffered": {
            "@type": "Service",
            "name": "Mobile App Development"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service", 
            "name": "Web Development"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "AI/ML Solutions"
          }
        }
      ]
    }
  };

  return (
    <>
      <Script
        id="organization-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationSchema),
        }}
      />
      <Script
        id="website-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteSchema),
        }}
      />
      <Script
        id="service-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(serviceSchema),
        }}
      />
    </>
  );
};

export default StructuredData;
