import React from "react";
import ServiceCount from "../Services/ServiceCount_2";
import ServiceLifecycleCard from "../Services/ServiceLifecycleCard";

const Process = () => {
  const discoveryAndRequirementGathering = [
    "Understand business goals, target audience, and project requirements",
    "Conduct consultations to define key features and functionalities",
    "Deliverables: Requirement documentation, project scope, and timeline",
  ];

  const technologyAndSelection = [
    "Choose the most suitable frontend, backend, and database technologies",
    "Deliverables: Technology stack selection and system architecture",
  ];

  const uiUxDesign = [
    "Create wireframes, prototypes, and design mockups",
    "Focus on user-friendly, responsive, and intuitive interfaces",
    "Deliverables: Wireframes, prototypes, and final UI design",
  ];

  const frontendDevelopment = [
    "Develop a responsive, interactive frontend using frameworks like React and Vue.js",
    "Ensure fast loading and cross-platform optimization",
    "Deliverables: Fully functional user interface",
  ];

  const backendDevelopment = [
    "Build secure, scalable server-side logic and databases",
    "Develop APIs and integrate backend functionality",
    "Deliverables: Backend system, API, and database integration",
  ];

  const apiIntegration = [
    "Seamlessly integrate third-party APIs (e.g., payment gateways, social media)",
    "Deliverables: Smooth API connections and external service integration",
  ];

  const testingAndQualityAssurance = [
    "Conduct unit, integration, and performance testing",
    "Ensure bug-free, optimized performance",
    "Deliverables: Fully tested, high-performance application",
  ];

  const deploymentAndLaunch = [
    "Set up servers, configure databases, and deploy the application",
    "Ensure a smooth and stress-free launch",
    "Deliverables: Deployed application on hosting or cloud platform",
  ];

  const postLaunchSupportAndMaintenance = [
    "Provide ongoing support, regular updates, and bug fixes",
    "Deliverables: Continuous monitoring and maintenance",
  ];

  const scalingAndFutureEnhancements = [
    "Scale the application for increased traffic and new features",
    "Deliverables: Scalable architecture and performance optimization",
  ];

  return (
    <section className="bg-pink-100 py-5 md:py-10 my-10 md:my-24">
      <div className="w-[90%] mx-auto my-5 md:py-10">
        <h2 className="text-center text-2xl md:text-[38px] leading-[57px]">
          Full Stack Development <span className="text-[#7716BC]">Process</span>{" "}
        </h2>
        <div className="flex flex-col md:flex-row justify-center items-center gap-5 md:gap-10  mt-10">
          <div className="flex justify-center items-center gap-2">
            <ServiceCount>1</ServiceCount>
            <ServiceLifecycleCard
              title="Discovery & Requirement Gathering"
              items={discoveryAndRequirementGathering}
            />
          </div>
          <div className="flex justify-center items-center gap-2">
            <ServiceCount>2</ServiceCount>
            <ServiceLifecycleCard
              title="Technology and Selection"
              items={technologyAndSelection}
            />
          </div>
          <div className="flex justify-center items-center gap-2">
            <ServiceCount>3</ServiceCount>
            <ServiceLifecycleCard title="UI/UX Design" items={uiUxDesign} />
          </div>
        </div>
        <div className="flex flex-col md:flex-row justify-center items-center gap-10 my-10">
          <div className="flex justify-center items-center gap-2">
            <ServiceCount>4</ServiceCount>
            <ServiceLifecycleCard
              title="Frontend Development"
              items={frontendDevelopment}
            />
          </div>
          <div className="flex justify-center items-center gap-2">
            <ServiceCount>5</ServiceCount>
            <ServiceLifecycleCard
              title="Backend Development"
              items={backendDevelopment}
            />
          </div>
          <div className="flex justify-center items-center gap-2">
            <ServiceCount>6</ServiceCount>
            <ServiceLifecycleCard
              title="API Integration"
              items={apiIntegration}
            />
          </div>
        </div>

        <div className="flex flex-col md:flex-row justify-center items-center gap-10">
          <div className="flex justify-center items-center gap-2">
            <ServiceCount>7</ServiceCount>
            <ServiceLifecycleCard
              title="Testing and Quality Assurance"
              items={testingAndQualityAssurance}
            />
          </div>
          <div className="flex justify-center items-center gap-2">
            <ServiceCount>8</ServiceCount>
            <ServiceLifecycleCard
              title="Deployment and Launch"
              items={deploymentAndLaunch}
            />
          </div>
          <div className="flex justify-center items-center gap-2">
            <ServiceCount>9</ServiceCount>
            <ServiceLifecycleCard
              title="Post-Launch Support & Maintenance"
              items={postLaunchSupportAndMaintenance}
            />
          </div>
        </div>
        <div className="w-full md:w-[40%] mx-auto flex justify-center items-center gap-2 mt-10">
          <ServiceCount>10</ServiceCount>
          <ServiceLifecycleCard
            title="Scaling & Future Enhancements"
            items={scalingAndFutureEnhancements}
          />
        </div>
      </div>
    </section>
  );
};

export default Process;
