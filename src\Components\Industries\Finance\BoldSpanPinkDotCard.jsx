import React from 'react'

const BoldSpanPinkDotCard = ({cardContent, paragraph}) => {
  return (
    <section className="w-full mx-auto p-4 border border-pink-500 rounded-2xl shadow-md">
     {cardContent.map((card, index) => (
  <div key={index}>
    <h3 className="font-semibold text-base md:text-lg">{card.title}</h3>
    <div className="py-5 flex flex-col gap-3">
      {card.content.map((feature, idx) => (
        <div className="flex items-start gap-4" key={idx}>
          <div className="flex-shrink-0 w-4 md:w-5 h-4 mt-1 md:h-5 bg-[#F245A1]"></div>
          <p className="text-sm md:text-base">
            {feature.span && <span className="font-semibold">{feature.span}</span>}
            {feature.text}
          </p>
        </div>
      ))}
    </div>
  </div>
))}
      <p className="font-semibold text-base md:text-lg">{paragraph}</p>
    </section>
  )
}

export default BoldSpanPinkDotCard