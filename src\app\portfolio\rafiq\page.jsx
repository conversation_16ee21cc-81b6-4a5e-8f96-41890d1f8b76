import React from 'react'
import Section1 from "@/Components/Portfolio/WebDev/PaintReady/Section1";
import Section2 from "@/Components/Portfolio/WebDev/PaintReady/Section2";
import Section4 from "@/Components/Portfolio/WebDev/PaintReady/Section4";
import Section5 from "@/Components/Portfolio/WebDev/PaintReady/Section5";

export const metadata = {
  title: "RAFIQ – AI Chatbot Platform for Education | Valueans",
  description: "AI-powered chatbot platform that enhances learning through multilingual support and user-trained bots. RAFIQ brings smart, scalable innovation to the education industry.",
};
const page = () => {
 const images = ["/Images/Rafiq2.png"];
  return (
    <div>
      <Section1 backgroundImage={"/Images/Rafiq-bg.png"} />
      <Section2
        image={"/Images/Rafiq1.png"}
        heading={"Rafiq"}
        paragraph1={
          "Valueans has provided digital solutions to several industries. One of the latest projects we delivered is RAFIQ which is an Artificial Intelligence solution. The main purpose of developing RAFIQ was to bring innovation to the education industry and improve the learning environment. "
        }
        paragraph2={
          "With RAFIQ, users can develop their own chatbots and can also train them by providing useful information. One unique feature of this AI solution was that it must offer interactions in more than a hundred languages."
        }
      />
      <Section4
        paragraph1={
          "To make RAFIQ robust and intuitive, we used the latest technology stack. We wanted to develop a very engaging front end for users so they can enhance their learning experience with ease. So we used React.js for its frontend development. However, we also had to make it fast-processing and efficient and to do that, we used Python and FastAPI for its backend development."
        }
        paragraph2={
          "RAFIQ is a highly innovative solution and it has transformed the education industry with its smart learning experience."
        }
      />
      <Section5 images={images} />
    </div>
  )
}

export default page