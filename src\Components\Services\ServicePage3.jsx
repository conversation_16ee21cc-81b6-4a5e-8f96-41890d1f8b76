import React from "react";
import ServiceCard from "./ServiceCard";
import Dropdown from "./Dropdown";

const cardData = [
  {
    imageSrc: "/Images/budget-cost.png",
    altText: "budget",
    title: "Budget Friendly",
    description:
      "We offer justified pricing without compromising on quality. So you invest once and take benefit from it forever.",
  },
  {
    imageSrc: "/Images/measure-tool.png",
    altText: "measure",
    title: "Made to measure",
    description:
      "We specialize in providing custom software development services that are meticulously designed to meet your expectations",
  },
  {
    imageSrc: "/Images/expert-route.png",
    altText: "expert",
    title: "Expertise",
    description:
      "Our well-qualified developers, designers, and strategists have proven exceptional skills which they have showcased in all projects by exceeding client’s expectations.",
  },
  {
    imageSrc: "/Images/cube-transparent.png",
    altText: "transparent",
    title: "Transparency",
    description:
      "We encourage transparent and effective communication throughout the development process to ensure client satisfaction and smooth workflow",
  },
  {
    imageSrc: "/Images/advacedmech.png",
    altText: "Advanced",
    title: "Advanced mechanisms",
    description:
      "We possess the latest technology assets, whether it is Cloud computing, AI, or Machine learning algorithms, we’ve got the best to deliver the best.",
  },
  {
    imageSrc: "/Images/turn-around-up.png",
    altText: "Turn around",
    title: "Fast Turnarounds",
    description:
      "We understand the importance of your time and money which is why we prioritize delivering the product on time while maintaining the standards.",
  },
];

const ServicePage3 = () => {
  return (
    <div className="max-w-[80%] mx-auto my-24">
      <h2 className="text-4xl text-center font-semibold capitalize p-4">
        Why select our services for <br />{" "}
        <span className="text-[#F245A1]">developing custom applications?</span>
      </h2>
      <div className="max-w-[80%] mx-auto">
        <p className="text-lg">
          At Valueans, we focus on building software that resonates with you. We
          carefully analyze your requirements and understand your business to
          provide you with the product you deserve. With a blend of creativity,
          technical excellence, and a client-centric approach, we transform your
          ideas into reality, ensuring every detail is perfect and every
          expectation is exceeded.
        </p>
        <p className="text-center text-lg font-bold py-4 my-4">
          Here’s how we’re different from others:
        </p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {cardData.map((card, index) => (
            <ServiceCard
              key={index}
              imageSrc={card.imageSrc}
              altText={card.altText}
              title={card.title}
              description={card.description}
            />
          ))}
        </div>
        <Dropdown title="test" content="test test test" />
      </div>
    </div>
  );
};

export default ServicePage3;
