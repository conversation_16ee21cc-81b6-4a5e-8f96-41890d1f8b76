"use client";
import React from "react";
import Image from "next/image";
import { FaFacebook, FaInstagram, FaLinkedin } from "react-icons/fa";
import Link from "next/link";
import useSocialLinks from "@/hooks/useSocialLinks";

const Section1 = ({ backgroundImage, heading, bannerText, className }) => {
  const { socialLinks, loading, error } = useSocialLinks();
  return (
    <section
      className={`relative ${className ? className : "h-[60vh] md:min-h-screen"}  `}
      style={{
        backgroundImage: `url(${backgroundImage})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
      }}
    >
      {/* Overlay div */}
      <div className="absolute inset-0 bg-[#7716BC99] z-0"></div>

      {/* Content */}
      <div className="flex flex-col justify-center items-center gap-6 h-full relative z-10 ">
        <h1 className="font-titillium text-3xl md:mb-10 font-bold md:text-5xl text-white text-center">
          {heading}
        </h1>

        {/* Full width h3 */}
        <h3 className="w-full text-center font-semibold font-titillium text-lg md:text-2xl py-3 md:py-4 text-white">
          {bannerText}
        </h3>
        {socialLinks && (
          <div className="flex justify-center items-center gap-8">
            <Link href={socialLinks.facebook} target="_blank">
              <FaFacebook className="text-white w-7 h-7 md:w-9 md:h-9 hover:scale-110 transition-transform" />
            </Link>
            <Link href={socialLinks.instagram} target="_blank">
              <FaInstagram className="text-white w-7 h-7 md:w-9 md:h-9 hover:scale-110 transition-transform" />
            </Link>
            <Link href={socialLinks.linkedIn} target="_blank">
              <FaLinkedin className="text-white w-7 h-7 md:w-9 md:h-9 hover:scale-110 transition-transform" />
            </Link>
          </div>
        )}
      </div>
    </section>
  );
};

export default Section1;
