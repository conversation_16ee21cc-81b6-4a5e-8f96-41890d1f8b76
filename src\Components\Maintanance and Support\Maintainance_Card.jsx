import React from "react";
import Image from "next/image";

const MaintenanceCard = ({
  title,
  description,
  items,
  containerBg = "bg-pink-100",
}) => {
  return (
    <div
      className={`w-[85vw] shadow flex flex-col md:flex-row justify-center items-center gap-3 md:gap-4 p-3  md:p-4 border border-gray-100 ${containerBg}`}
    >
      <div className="w-full md:w-[50%] px-3">
        <h3 className="text-lg md:text-2xl md:leading-[40px] font-semibold text-center md:text-left">
          {title}
        </h3>
        <p className="text-base md:text-xl text-center md:text-left">{description}</p>
      </div> 
      <div className="flex flex-col justify-center w-full md:w-[50%] items-center gap-3">
        {items.map((item, index) => (
          <div
            key={index}
            className={`flex justify-center items-start p-2 gap-1 shadow rounded-lg border w-[85%] bg-purple-200 border-purple-500`}
          >
            
              <Image src={"/Images/pointer.png"} alt="pointer" className="flex-shrink-0" width={32} height={32} />
            
            <p className="text-base">{item}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default MaintenanceCard;
