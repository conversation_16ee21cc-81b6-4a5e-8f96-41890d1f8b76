import Image from "next/image";
import React from "react";

const Card = ({ Imgsrc, Altsrc, title, description }) => {
  return (
    <div className="max-w-sm block border-2 border-gray-200 bg-pink-100 p-4 rounded-xl shadow-md">
      <div className="flex justify-center items-center">
        <Image src={Imgsrc} alt={Altsrc} width={53} height={53} />
      </div>
      <h3 className="text-center text-base md:text-lg font-medium text-[#7716BC] my-3">
        {title}
      </h3>
      <p className="text-center text-sm md:text-base">{description}</p>
    </div>
  );
};

export default Card;
