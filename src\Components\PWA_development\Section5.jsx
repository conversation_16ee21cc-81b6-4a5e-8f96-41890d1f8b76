import Image from "next/image";
import React from "react";
import Button from "../Buttons/Button";
import PinkDotCard from "./PinkDotCard";

const Section5 = ({ cardContent, image, heading,paragraph }) => {
  
  return (
    <div className="mb-10 md:mb-24 md:py-8 md:px-[75px]">
      <h1 className="text-xl md:text-3xl text-center md:text-start font-bold">{heading}</h1>
      <div className="flex flex-col-reverse md:flex-row justify-center gap-4 md:justify-between items-center">
        <div className="w-[90%]">
          <Image src={image} alt="AI" width={500} height={500} />
        </div>
        <div className="w-[90%] flex flex-col justify-center items-center md:justify-start md:items-start mx-[40px] space-y-5">
          <PinkDotCard paragraph={paragraph} cardContent={cardContent}  />

          <Button to={"/estimate-cost"} bgColor="bg-[#7716BC] w-48" hoverColor="hover:bg-purple-600">
            Get an Estimate
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Section5;
