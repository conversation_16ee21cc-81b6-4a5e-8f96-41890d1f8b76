import Image from "next/image";
import Link from "next/link";
import React from "react";

const BlogInfoDisplay = ({ imageSrc, title, link, date, description }) => {
  return (
    <div className="w-[100%] bg-white border border-gray-200 rounded-lg shadow p-4 md:p-6">
      <Link href={link || "/blog"} passHref>
        <div className="relative">
          <Image
            className="rounded-t-lg"
            src={imageSrc}
            alt={title || "Blog post image"}
            layout="responsive" // Makes image fill the container's width and adjust height automatically
            width={100} // Use percentage-based width
            height={60}
          />
        </div>
        <div>
          <div className="w-full mt-6 mb-5">
            <p className="mb-1 md:mb-3 font-semibold text-xl text-gray-700 w-auto">
              {title}
            </p>
            <p className="text-sm md:text-lg font-normal break-words overflow-hidden">
              {description}
            </p>
          </div>
          <div className="flex justify-between align-center border-t-2 border-gray-100">
            <span className="text-[#232536] text-[14px] font-light inline-flex items-center">
              {date}
            </span>

            <span className="inline-flex items-center px-3 py-2 text-sm font-medium text-center rounded-lg hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300">
              Read more
              <svg
                className="rtl:rotate-180 w-3.5 h-3.5 ms-2"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 14 10"
              >
                <path
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M1 5h12m0 0L9 1m4 4L9 9"
                />
              </svg>
            </span>
          </div>
        </div>
      </Link>
    </div>
  );
};

export default BlogInfoDisplay;
