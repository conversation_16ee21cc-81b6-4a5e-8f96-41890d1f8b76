"use client";

import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/pagination";
import { Pagination } from "swiper/modules";
import Card_holder from "../Card/Card_holder";

const cardData = [
  {
    id: 1,
    title1: "Customer Service Automation",
    description1:
      "Machine learning can significantly enhance customer service by automating routine tasks and providing intelligent support. Companies integrate ML-powered chatbots to handle common customer inquiries, improving response times and customer satisfaction.",
    title2: "Chatbots and Virtual Assistants",
    description2:
      "Use natural language processing (NLP) to understand and respond to customer queries, providing instant support and freeing up human agents for more complex issues.",
    title3: "Sentiment Analysis",
    description3:
      "Analyze customer feedback and interactions to gauge sentiment and identify areas for improvement.",
    bgcolor_1: "bg-purple-200",
    bgcolor_2: "bg-pink-200",
  },
  {
    id: 2,
    title1: "Operational Efficiency",
    description1:
      "Manufacturing companies use ML to predict equipment failures, allowing for proactive maintenance and reducing unplanned downtime. Machine learning can streamline business operations and improve efficiency in various ways.",
    title2: "Process Optimization",
    description2:
      "Identify inefficiencies in business processes and recommend improvements based on data analysis.",
    title3: "Predictive Maintenance",
    description3:
      "Monitor equipment and machinery to predict failures before they occur, reducing downtime and maintenance costs.",
    bgcolor_1: "bg-pink-200",
    bgcolor_2: "bg-purple-200",
  },
  {
    id: 3,
    title1: "Human Resources and Talent Management",
    description1:
      "Machine learning helps predict employee turnover, streamline recruitment processes, and analyze workforce productivity.",
    title2: "Recruitment",
    description2:
      "Automate resume screening and candidate matching to find the best-fit candidates quickly and efficiently.",
    title3: "Employee Retention",
    description3:
      "Analyze employee data to identify factors contributing to turnover and develop strategies to improve retention.",
    bgcolor_1: "bg-purple-200",
    bgcolor_2: "bg-pink-200",
  },
  {
    id: 4,
    title1: "Human Resources and Talent Management",
    description1:
      "Machine learning helps predict employee turnover, streamline recruitment processes, and analyze workforce productivity.",
    title2: "Recruitment",
    description2:
      "Automate resume screening and candidate matching to find the best-fit candidates quickly and efficiently.",
    title3: "Employee Retention",
    description3:
      "Analyze employee data to identify factors contributing to turnover and develop strategies to improve retention.",
    bgcolor_1: "bg-purple-200",
    bgcolor_2: "bg-pink-200",
  },
];

const CardCarousel = () => {
  return (
    <Swiper
      spaceBetween={20}
      breakpoints={{
        640: { slidesPerView: 1 },
        768: { slidesPerView: 2 },
        1024: { slidesPerView: 3 },
      }}
      pagination={{ clickable: true }}
      modules={[Pagination]}
    >
      {cardData.map((item, index) => (
        <SwiperSlide key={index}>
          <Card_holder
            title1={item.title1}
            description1={item.description1}
            title2={item.title2}
            description2={item.description2}
            title3={item.title3}
            description3={item.description3}
            bgcolor_1={item.bgcolor_1}
            bgcolor_2={item.bgcolor_2}
          />
        </SwiperSlide>
      ))}
    </Swiper>
  );
};

export default CardCarousel;
