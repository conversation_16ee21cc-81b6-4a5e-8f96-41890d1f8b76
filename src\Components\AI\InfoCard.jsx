import React from "react";
import Image from "next/image";
import Link from "next/link";

const InfoCard = ({ title, description }) => {
  return (
    <div className="text-justify">
      <div className="flex gap-2 ">
        <Image
          src="/Images/Tick.png"
          alt="Tick"
          width={32} // Next.js will handle this as the max size
          height={24}
        />
        <h3 className="text-lg md:text-2xl font-semibold">{title}</h3>
      </div>
      {description && (
        <p
          className="text-sm md:text-base"
          dangerouslySetInnerHTML={{
            __html: description,
          }}
        />
      )}
    </div>
    // <div className=" w-full md:max-w-sm  p-2 md:p-4">
    //   <div className="flex">
    //     <div className=" gap-2 ">
    //       <Image
    //         src="/Images/Tick.png"
    //         alt="Tick"
    //         width={30} // Next.js will handle this as the max size
    //         height={24}
    //         className="w-full h-full"
    //       />
    //     </div>
    //     <h3 className="text-lg md:text-2xl font-semibold">{title}</h3>
    //   </div>
    //   <p className="text-sm md:text-lg ml-7">{description}</p>
    // </div>
  );
};

export default InfoCard;
