import React from "react";
const Card = ({ title, description }) => {
  return (
    <div className="block w-full h-auto md:max-w-sm p-4 md:h-[200px] rounded-md shadow-sm border border-purple-500 bg-white">
      <h3 className="text-base md:text-lg font-semibold">{title}</h3>
      <p className="text-sm md:text-base text-justify">{description}</p>
    </div>
  );
};

const Section6 = () => {
  return (
    <div className="w-[85%] mx-auto mb-10 md:mb-24">
      <h2 className="text-xl md:text-3xl md:leading-9 text-center font-semibold">
        <span className="text-[#F245A1]">Valueans</span> Covers a Wide Range of
        Industries
      </h2>
      <div className="flex flex-col justify-center items-center gap-4 md:gap-6 mt-6 md:mt-[42px]">
        <div className="flex flex-col md:flex-row justify-center items-center gap-4 md:gap-6">
          <Card
            title={"Healthcare"}
            description={
              "Mobile health apps may improve medical services, boost patient involvement, and streamline operational procedures."
            }
          />
          <Card
            title={"Insurance"}
            description={
              "Use mobile technology created with your industry in mind to provide improved insurance solutions and services."
            }
          />
          <Card
            title={"Finance"}
            description={
              "Mobile fintech software may provide you with a competitive advantage in any field, including trading, loan management, and investment banking."
            }
          />
        </div>
        <div className="flex flex-col md:flex-row justify-center items-center gap-4 md:gap-6">
          <Card
            title={"Manufacturing"}
            description={
              "We provide useful mobile solutions to manage supply chains, production lines, and customer support for the industrial sector."
            }
          />
          <Card
            title={"Education"}
            description={
              "Use custom app development to support online learning procedures and guarantee a mobile experience for instructors and students."
            }
          />
          <Card
            title={"Transportation"}
            description={
              "Mobile applications help businesses in the transportation sector run their fleets and logistics more efficiently."
            }
          />
        </div>
        <div className="flex flex-col md:flex-row justify-center items-center gap-4 md:gap-6">
          <Card
            title={"Real Estate"}
            description={
              "We use mobile technologies to help all real estate market participants, from agents and sellers to homebuyers, succeed."
            }
          />
          <Card
            title={"Retail & eCommerce"}
            description={
              "To attract online customers and guarantee business growth, offer in-person experiences and use mobile apps to streamline retail procedures."
            }
          />
        </div>
      </div>
    </div>
  );
};

export default Section6;
