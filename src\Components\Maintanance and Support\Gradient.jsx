import React from "react";
import Gradient_card from "../UI_UX/Gradient_card";

const cardData = [
  {
    title: "1. Application Evolution",
    description:
      "We help make your applications smarter, stronger, and aligned with your business goals through our applications maintenance and support services. Here’s how we make it happen.",
    items: [
      "We make your app more powerful by adding next-level tools connections.",
      "We upgrade your app with the latest features that follow the latest trends and are currently in demand.",
      "We focus on making clear designs and enjoyable experiences for users.",
    ],
  },
  {
    title: "2. Application Security Management",
    description:
      "Security is critical for building trust, and safeguarding your platform is a top priority. Through Our IT maintenance and support services, Here’s how we keep you secure: ",
    items: [
      "We are always on the monitoring to always detect and stop threats as they happen.",
      "We schedule different assessments for your app to try and detect threats if any.",
      "We do an essential process to judge the failure of your system against DDOS attack.",
    ],
  },
  {
    title: "3. Continuous Improvement",
    description:
      "We focus on ongoing enhancements to ensure your system evolves and adapts to your business needs. Our maintenance and support services include:",
    items: [
      "Automating workflows for efficiency.",
      "Powering analytics to drive future development strategies.",
    ],
  },
];

const GradientHolder = () => {
  return (
    <div className="flex flex-wrap justify-center gap-4 mt-8">
      {cardData.map((card, index) => (
        <Gradient_card
          key={index}
          title={card.title}
          description={card.description}
          items={card.items}
        />
      ))}
    </div>
  );
};

export default GradientHolder;
