import React from "react";
import ServiceCount from "../Services/ServiceCount_2";
import ServiceLifecycleCard from "../Services/ServiceLifecycleCard_2";

const Process = () => {
  const discoveryAssessment = [
    "Understand your business goals and data challenges.",
    "Assess existing data systems and identify gaps.",
  ];

  const strategyDevelopment = [
    "Develop a customized data strategy aligned with your goals.",
    "Define KPIs and success metrics.",
  ];

  const implementation = [
    "Deploy data solutions using the latest tools and methodologies.",
    "Integrate systems to ensure seamless workflows.",
  ];

  const analysisAndInsightGeneration = [
    "Perform in-depth analysis to uncover trends and patterns.",
    "Generate actionable insights through visualizations and reports.",
  ];

  const optimizationAndSupport = [
    "Continuously monitor and refine solutions for maximum impact.",
    "Provide ongoing support to adapt to changing business needs.",
  ];

  return (
    <section className="bg-pink-100 my-10 md:my-24 p-4 md:p-7">
      <div className="w-[85%] mx-auto">
        <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
          Our process: From{" "}
          <span className="text-[#F245A1]">Data to Value</span>
        </h2>
        <p className="text-center text-base md:text-lg">At Valueans, we follow a structured process to transform your data into actionable insights. Here’s a glimpse of how we work.</p>
        <div className="flex flex-col md:flex-row justify-center items-center gap-2 md:gap-5">
          <div className="flex justify-center items-center gap-2 my-5">
            <ServiceCount>1</ServiceCount>
            <ServiceLifecycleCard
              title="Discovery Assessment"
              items={discoveryAssessment}
            />
          </div>

          <div className="flex justify-center items-center gap-2 my-5">
            <ServiceCount>2</ServiceCount>
            <ServiceLifecycleCard
              title="Strategy Development"
              items={strategyDevelopment}
            />
          </div>

          <div className="flex justify-center items-center gap-2 my-5">
            <ServiceCount>3</ServiceCount>
            <ServiceLifecycleCard
              title="Implementation"
              items={implementation}
            />
          </div>
        </div>
        <div className="flex flex-col md:flex-row justify-center items-center gap-2 md:gap-5">
          <div className="flex justify-center items-center gap-2 my-5">
            <ServiceCount>4</ServiceCount>
            <ServiceLifecycleCard
              title="Analysis and Insight Generation"
              items={analysisAndInsightGeneration}
            />
          </div>

          <div className="flex justify-center items-center gap-2 my-5">
            <ServiceCount>5</ServiceCount>
            <ServiceLifecycleCard
              title="Optimization and Support"
              items={optimizationAndSupport}
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default Process;
