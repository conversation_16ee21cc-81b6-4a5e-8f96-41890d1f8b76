"use client";

import React from "react";
import dynamic from "next/dynamic";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import Card_holder from "../Card/Card_holder"; // Assuming Card_holder is in the same directory

const Slider = dynamic(() => import("react-slick"), {
  ssr: false,
  loading: () => <p>Loading...</p>,
});

const CustomPrevArrow = ({ onClick }) => (
  <button
    onClick={onClick}
    className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-purple-500 text-white p-1 md:p-3 rounded-full hover:bg-purple-600"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      className="h-5 w-5"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
      strokeWidth={2}
    >
      <path strokeLinecap="round" strokeLinejoin="round" d="M15 19l-7-7 7-7" />
    </svg>
  </button>
);

const CustomNextArrow = ({ onClick }) => (
  <button
    onClick={onClick}
    className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-purple-500 text-white p-1 md:p-3 rounded-full hover:bg-purple-600"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      className="h-5 w-5"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
      strokeWidth={2}
    >
      <path strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7" />
    </svg>
  </button>
);

const CardCarousel = () => {
  const sliderSettings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 3,
    slidesToScroll: 1,
    swipeToSlide: true,
    nextArrow: <CustomNextArrow />,
    prevArrow: <CustomPrevArrow />,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
          swipeToSlide: true,
        },
      },
      {
        breakpoint: 600,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
    ],
  };

  const cardData = [
    {
      title1: "Card 1 Title",
      description1: "This is the description for the first card.",
      title2: "Sub Card 1",
      description2: "Details about sub card 1.",
      title3: "Sub Card 2",
      description3: "Details about sub card 2.",
      bgcolor_1: "bg-purple-50",
      bgcolor_2: "bg-purple-100",
    },
    {
      title1: "Card 2 Title",
      description1: "This is the description for the second card.",
      title2: "Sub Card 3",
      description2: "Details about sub card 3.",
      title3: "Sub Card 4",
      description3: "Details about sub card 4.",
      bgcolor_1: "bg-blue-50",
      bgcolor_2: "bg-blue-100",
    },
    {
      title1: "Card 3 Title",
      description1: "This is the description for the third card.",
      title2: "Sub Card 5",
      description2: "Details about sub card 5.",
      title3: "Sub Card 6",
      description3: "Details about sub card 6.",
      bgcolor_1: "bg-green-50",
      bgcolor_2: "bg-green-100",
    },

    {
      title1: "Card 1 Title",
      description1: "This is the description for the first card.",
      title2: "Sub Card 1",
      description2: "Details about sub card 1.",
      title3: "Sub Card 2",
      description3: "Details about sub card 2.",
      bgcolor_1: "bg-purple-50",
      bgcolor_2: "bg-purple-100",
    },
    {
      title1: "Card 2 Title",
      description1: "This is the description for the second card.",
      title2: "Sub Card 3",
      description2: "Details about sub card 3.",
      title3: "Sub Card 4",
      description3: "Details about sub card 4.",
      bgcolor_1: "bg-blue-50",
      bgcolor_2: "bg-blue-100",
    },
    {
      title1: "Card 3 Title",
      description1: "This is the description for the third card.",
      title2: "Sub Card 5",
      description2: "Details about sub card 5.",
      title3: "Sub Card 6",
      description3: "Details about sub card 6.",
      bgcolor_1: "bg-green-50",
      bgcolor_2: "bg-green-100",
    },
  ];

  return (
    <div className="my-12 px-4">
      <h2 className="text-2xl md:text-4xl text-center font-semibold mb-10">
        Our <span className="text-purple-600">Card Carousel</span>
      </h2>
      <div className="relative w-full max-w-6xl mx-auto overflow-hidden">
        <Slider {...sliderSettings}>
          {cardData.map((card, index) => (
            <div
              key={index}
              className="flex justify-center items-center gap-5 px-2 md:px-4"
            >
              <Card_holder
                title1={card.title1}
                description1={card.description1}
                title2={card.title2}
                description2={card.description2}
                title3={card.title3}
                description3={card.description3}
                bgcolor_1={card.bgcolor_1}
                bgcolor_2={card.bgcolor_2}
              />
            </div>
          ))}
        </Slider>
      </div>
    </div>
  );
};

export default CardCarousel;
