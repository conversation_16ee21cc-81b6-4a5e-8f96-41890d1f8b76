import React from "react";
import PinkBgcard from "../Nlp/PinkBgcard";

const Section6 = ({ cardData, heading, spanHeading, paragrapgh }) => {
  return (
    <div className="mx-[20px] mb-10 md:mb-24 md:px-[75px]">
      <h2 className="text-xl md:text-3xl text-center font-semibold mb-1">
        <span className="text-[#7716BC]">{spanHeading}</span> {heading}
      </h2>
      <p className="md:w-[85%] md:mx-auto mb-4 text-base md:text-xl text-center">
        {paragrapgh}
      </p>
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-5">
        {cardData.map((card, index) => (
          <PinkBgcard
            key={index}
            title={card.title}
            description={card.description}
          />
        ))}
      </div>
    </div>
  );
};

export default Section6;
