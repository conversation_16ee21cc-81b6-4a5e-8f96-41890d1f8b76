import React from "react";

const Section3 = () => {
  return (
    <section className="bg-pink-100 mb-10 md:mb-24 p-4 md:p-8">
      <div className="w-[90%] mx-auto flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6">
        <div className="flex-1">
          <h2 className="text-xl md:text-3xl md:leading-[40px] font-semibold">
            Custom Mobile App Development Services at
            <span className="text-[#F245A1]">Valueans</span>
          </h2>
          <p className="text-base md:text-xl capitalize">
            We provide a comprehensive range of application design, integration, and management services. The business oversees the complete mobile app development process, from conception and ideation to delivery and continuing support, regardless of whether the app is a consumer-focused app or a game-changing enterprise-class solution.
          </p>
        </div>
        <div className="flex-1">
          <section className="w-full  mx-auto p-4 border border-purple-800 rounded-2xl shadow-md">
            <h3 className="font-semibold text-base md:text-2xl ">
              With enterprise mobile app development services at Valueans, we:
            </h3>
            <div className="py-5 flex flex-col gap-3 px-2 text-justify md:gap-5">
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 mt-1 bg-[#F245A1]"></div>
                <p className="text-sm md:text-lg">
                  Have leveraged more than 500 Personalized Mobile App Solutions
                </p>
              </div>

              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 mt-1 bg-[#F245A1]"></div>
                <p className="text-sm md:text-lg">
                  Offer Cross-Platform and Native Development from ERP to
                  Workflow Management
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 mt-1 bg-[#F245A1]"></div>
                <p className="text-sm md:text-lg">
                  Provide Enterprise Mobile App Development Services &
                  Integrations
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 mt-1 bg-[#F245A1]"></div>
                <p className="text-sm md:text-lg">
                  Deliver apps for smartphones that function on any device!
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 mt-1 bg-[#F245A1]"></div>
                <p className="text-sm md:text-lg">
                  Ownership of the entire product, including backlog control.
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 mt-1 bg-[#F245A1]"></div>
                <p className="text-sm md:text-lg">
                  Have 250+ Leading IT Professionals
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 mt-1 bg-[#F245A1]"></div>
                <p className="text-sm md:text-lg">
                  Have almost two decades of experience leading in mobile development since iOS and Android launched 
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 mt-1 bg-[#F245A1]"></div>
                <p className="text-sm md:text-lg">
                  Full cycle development and support strategic long-term partnerships 
                </p>
              </div>
            </div>
          </section>
        </div>
      </div>
    </section>
  );
};

export default Section3;
