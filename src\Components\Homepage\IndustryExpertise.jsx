"use client";
import React from "react";
import dynamic from "next/dynamic";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import IndustryExpertiseCard from "./IndustryExpertiseCards";
import { FaArrowLeft, FaArrowRight } from "react-icons/fa";
import Heading from "../Heading/Heading";

const Slider = dynamic(() => import("react-slick"), {
  ssr: false,
  loading: () => <p>Loading...</p>,
});

const CustomPrevArrow = ({ onClick }) => (
  <button
    onClick={onClick}
    className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-purple-500 text-white p-1 md:p-3 rounded-full hover:bg-purple-600"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      className="h-5 w-5"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
      strokeWidth={2}
    >
      <path strokeLinecap="round" strokeLinejoin="round" d="M15 19l-7-7 7-7" />
    </svg>
  </button>
);

const CustomNextArrow = ({ onClick }) => (
  <button
    onClick={onClick}
    className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-purple-500 text-white p-1 md:p-3 rounded-full hover:bg-purple-600"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      className="h-5 w-5"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
      strokeWidth={2}
    >
      <path strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7" />
    </svg>
  </button>
);

const IndustryExpertise = () => {
  const expertiseData = [
    {
      imageSrc: "/Images/expertise1.png",
      altText: "Healthcare",
      title: "Healthcare",
      description:
        "The healthcare industry demands precision, security, & efficiency to manage sensitive patient data & streamline operations. Valueans excels in delivering customized solutions that enhance patient care, optimize workflows, and ensure compliance with regulatory standards. Our expertise in healthcare technology helps you with better outcomes and smoother operations.",
    },
    {
      imageSrc: "/Images/expertise2.png",
      altText: "Logistics",
      title: "Logistics",
      description:
        "In logistics, efficiency and real-time tracking are crucial for smooth supply chain operations. Valueans delivers innovative solutions to enhance route planning, inventory tracking, and real-time analytics. Our logistics experts optimize your logistics network for maximum efficiency and scalability.",
    },
    {
      imageSrc: "/Images/expertise3.png",
      altText: "Travel",
      title: "Travel",
      description:
        "The travel industry thrives on seamless booking experiences and personalized customer solutions. Valueans offers technology platforms that ensure user-friendly experiences, optimize booking systems, and enhance overall efficiency while delivering exceptional travel services and managing operations effectively.",
    },
    {
      imageSrc: "/Images/expertise4.png",
      altText: "Construction",
      title: "Construction",
      description:
        "The construction industry benefits from innovative project management tools, ensuring seamless collaboration. Valueans provides solutions for project planning, resource allocation, safety compliance, and reducing costs, ensuring your construction projects are completed on time and within budget.",
    },
    {
      imageSrc: "/Images/expertise5.png",
      altText: "Manufacturing",
      title: "Manufacturing",
      description:
        "With over 6 years of experience in providing on-demand solutions, we deliver solutions that optimize production processes, improve quality controls, and refine supply chain visibility. Helping your business thrive by reducing downtime and enhancing output efficiency.",
    },
    {
      imageSrc: "/Images/expertise6.png",
      altText: "Agriculture",
      title: "Agriculture",
      description:
        "Valueans offers smart solutions for precision agriculture, farm management, and data analytics, helping you streamline agricultural processes. Our solutions empower you to optimize yields and operations with advanced technology and customized services.",
    },
    {
      imageSrc: "/Images/expertise7.png",
      altText: "Oil and Gas",
      title: "Oil and Gas",
      description:
        "The oil and gas industry requires robust systems for exploration, production, and delivery while enhancing safety and reducing costs. Valueans provides advanced tools to monitor operations, improve safety standards, and support your efforts to manage complex operations and drive innovation.",
    },
  ];

  const sliderSettings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 3,
    slidesToScroll: 1,
    swipeToSlide: true,
    nextArrow: <CustomNextArrow />,
    prevArrow: <CustomPrevArrow />,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 600,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
    ],
  };

  return (
    <div className="my-12 px-4">
      <Heading>
        Our <span className="text-purple-600">Industry</span> Expertise
      </Heading>

      <div className="relative w-full max-w-6xl mx-auto overflow-hidden">
        <Slider {...sliderSettings}>
          {expertiseData.map((item, index) => (
            <div key={index} className="flex justify-center items-center px-2">
              <IndustryExpertiseCard
                imageSrc={item.imageSrc}
                altText={item.altText}
                title={item.title}
                description={item.description}
              />
            </div>
          ))}
        </Slider>
      </div>
    </div>
  );
};

export default IndustryExpertise;
