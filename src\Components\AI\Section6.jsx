import React from "react";
import Card from "../App Integration/Card_2";

const Section6 = () => {
  const data = [
    {
      title: "IT Services",
      content: [
        "LLM",
        "AI Agents",
        "AI Lab Style Transfer",
        "Synthetic Data Showcase",
        "ML Models",
      ],
    },
    {
      title: "Healthcare and Biotech Industry AI Solutions",
      content: [
        "Virtual Care",
        "Medical Diagnosis",
        "Forecasting",
        "Data Analytics",
      ],
    },
    {
      title: "Legal & Business AI Solutions",
      content: [
        "Research",
        "Regulatory Compliance",
        "Bot for Legal Clauses",
        "Document Review",
        "Data Extraction",
        "Case Selection",
        "Contract Management",
      ],
    },
    {
      title: "Retail Industry",
      content: [
        "Market Research",
        "Automated Customer Service",
        "Price Optimization",
        "Forecasting",
        "Inventory Management",
      ],
    },
    {
      title: "Energy and Resources Industry",
      content: [
        "Anomaly Detection",
        "Predictive Energy Production",
        "Geological Data Analysis",
        "Forecasting",
        "Predictive Maintenance",
      ],
    },
    {
      title: "Real Estate AI Solutions",
      content: [
        "Risk Prevention",
        "Investment Analysis",
        "Vision for Safety",
        "Better Energy Consumption",
        "Improved Building Management",
        "Predictive Maintenance",
      ],
    },
    {
      title: "Manufacturing Industry",
      content: [
        "Process Automation",
        "Better Adaptive Controls",
        "Robotics",
        "Improved Quality Control",
        "Predictive Maintenance",
        "Optimized Supply Chain",
      ],
    },
  ];

  return (
    <section className="w-full my-10 md:my-24">
      <div className="w-[90%] md:w-[85%] mx-auto px-4">
        <h2 className="text-2xl md:text-3xl text-center font-semibold mb-8">
          We’ve Delivered Successful AI Business <br /> Solutions to{" "}
          <span className="text-[#7716BC]">Countless Industries</span>
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {data.map((item, index) => (
            <div
              key={index}
              className={`${index === data.length - 1 ? "md:col-span-3" : ""}`}
            >
              {index === data.length - 1 ? (
                <div className="flex justify-center">
                  <Card height={"md:h-[300px]"} title={item.title} content={item.content} />
                </div>
              ) : (
                <div className="justify-items-center">
                  <Card height={"md:h-[300px]"} title={item.title} content={item.content} />
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Section6;
