"use client";

import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/pagination";
import { Pagination } from "swiper/modules";
import Card from "./Card";

const dummyData = [
  {
    title: "Cost Savings",
    description:
      "Cut expenses by avoiding the need to buy and replace physical servers. Reduce operating costs by doing away with server and cloud infrastructure management and upkeep. With a fully managed and integrated cloud infrastructure, you can streamline operations and spend more time operating your company and less time establishing, maintaining, and managing the network infrastructure.",
  },
  {
    title: "Automatic Upgrades",
    description:
      "With a modernized, integrated cloud services architecture that is automatically updated and improved, you can move and innovate more quickly.",
  },
  {
    title: "Network Security",
    description:
      "Use cloud managed services to offer enhanced data protection, disaster recovery assistance, and round-the-clock monitoring. Stay ahead of the competition and boost operational efficiency by utilizing cloud services' sophisticated security.",
  },
  {
    title: "Adaptability and Creativity",
    description:
      "Platforms and services for multi cloud-based business and technology are robust, and intelligent by design, and promote automation and creative business operations.",
  },
  {
    title: "Assistance for Disaster Recovery",
    description:
      "To swiftly recover and restore data and redeploy without causing system-wide interruptions, backup and sync your data. To reduce downtime and quickly and nimbly restart regular company operations in the case of a disaster, make sure disaster recovery support is in place.",
  },
  {
    title: "Rapid Response Times",
    description:
      "Use round-the-clock assistance to promptly address issues found to improve operational effectiveness and provide a robust, dependable network infrastructure for cloud managed services.",
  },
];

const CardCarousel = () => {
  return (
    <Swiper 
      spaceBetween={20} 
      breakpoints={{
        640: { slidesPerView: 1 },
        768: { slidesPerView: 2 },
        1024: { slidesPerView: 3 }
      }}
      pagination={{ clickable: true }}
      modules={[Pagination]}
    >
      {dummyData.map((item, index) => (
        <SwiperSlide key={index}>
          <Card title={item.title} description={item.description} height={"h-auto md:h-[300px]"}/>
        </SwiperSlide>
      ))}
    </Swiper>
  );
};

export default CardCarousel;
