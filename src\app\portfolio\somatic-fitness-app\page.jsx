import React from "react";
import Section1 from "@/Components/Portfolio/WebDev/PaintReady/Section1";
import Section2 from "@/Components/Portfolio/WebDev/PaintReady/Section2";
import Section4 from "@/Components/Portfolio/WebDev/PaintReady/Section4";
import Section5 from "@/Components/Portfolio/WebDev/PaintReady/Section5";
export const metadata = {
  title: "Somatic Fitness – Nutrition & Workout Tracking App | Valueans",
  description: "Custom mobile app for diet and workout planning. Features barcode scan, analytics, and a user-friendly interface for health-conscious users.",
};
const page = () => {
  const images = ["/Images/Somantic-app.png"];
  return (
    <div>
      <Section1 backgroundImage={"/Images/Somatic-bg.png"} />
      <Section2
        image={"/Images/Somatic2.png"}
        heading={"Somatic Fitness App"}
        paragraph1={
          "At Valueans, we love to deliver user-friendly mobile solutions that can make a difference. We recently worked on Somatics Fitness & Nutrition (SFN) software. It’s a smartphone application to help people with their diet and exercise routines. "
        }
        paragraph2={
          "We strived to make this app clear and intuitive in terms of its User Interface so that users can have a convenient navigation experience. The app contains a built-in barcode reader, nutrition aid, calorie tracker, and much more. We also provided a dashboard to track progress with its analytical charts and statistics. "
        }
      />
      <Section4
        paragraph1={
          "Somatics Fitness & Nutrition (SFN) is a user-friendly fitness monitoring mobile solution designed using a contemporary technological stack. To offer a strong architecture and an aesthetically appealing design, we used Python for developing the back end and React for developing the front end."
        }
        paragraph2={
          "We leveraged state-of-the-art technology with our deep expertise to deliver an exercise and nutrition mobile solution that enhanced user experiences and drove corporate success."
        }
      />
      <Section5 images={images} />
    </div>
  );
};

export default page;
