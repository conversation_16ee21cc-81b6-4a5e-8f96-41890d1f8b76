import React from "react";
import Image from "next/image";
import BlueTopCard from "../E-commerce/BlueTopCard";

const Section13 = ({
  cardData,
  headingLeft,
  headingRight,
  spanHeading,
  image,
  cardHeight,
  cardHeaderHeight,
}) => {
  return (
    <div className="w-[90%] mx-auto mb-10 md:my-24 ">
      <div className="my-2  flex md:justify-end">
        <h1 className="text-xl md:text-3xl md:leading-[40px] md:pl-4 md:w-[50%] font-semibold text-center">
          {headingLeft} <span className="text-[#F245A1]">{spanHeading}</span>{" "}
          {headingRight}
        </h1>
      </div>
      <div className="flex flex-col md:flex-row justify-between gap-8 my-8">
        <div className="md:w-[50%] min-h-[250px] md:min-h-0 w-full relative">
          {" "}
          <Image
            src={image}
            alt="AI"
            layout="fill" // Use layout="fill" to take up the entire space of the div
            objectFit="cover" // Ensures the image covers the entire space
            objectPosition="center" // Centers the image within the div
          />
        </div>
        <div className="grid grid-cols-1 md:w-[50%]  gap-6 mx-auto">
          {cardData.map((card, index) => (
            <div key={index} className=" flex justify-center items-center mb-3">
              <BlueTopCard
                title={card.title}
                description={card.description}
                PinkTopCardheight={cardHeight}
                cardHeaderHeight={cardHeaderHeight}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Section13;
