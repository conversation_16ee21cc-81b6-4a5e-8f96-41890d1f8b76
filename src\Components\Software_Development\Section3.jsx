import React from "react";
import Link from "next/link";

// Array of service cards content
const services = [
  {
    title: "B2B Custom Software Development",
    description: `We design and develop B2B custom software at the application level to improve operational efficiency, workflow, and performance of the company. From managing digital transformation at a startup to enterprise level solutions, we ensure that there is seamless integration. Our B2B Custom Software services focus on improving collaboration, resource allocation, and customer experience. We cover the entire process from requirement analysis, through <a href='/ui-ux' class='text-[#7716BC] hover:underline'>UI/UX</a> designing, development, testing, and deployment.`
  },
  {
    title: "Customized Software Solutions",
    description: `Our company seeks to design and build tailored software solutions that best address your unique business challenges. Be it custom software development, ERP systems, or AI solutions, we make sure our products evolve as you scale. Every company is unique and that is why we ensure our products offer specific solutions to specific problems. We try to build systems that can allow for future growth and changes in the business environment.`
  },
  {
    title: "Enterprise Software Development",
    description: `We provide specialized services in enterprise software development, offering business-grade applications that can support entire organizations. Our business solutions automate tasks, foster teamwork and facilitate smarter decision making through the use of <a href='/Data_and_Analytics' class='text-[#7716BC] hover:underline'>data analytics and insights</a>. We apply high-end solutions, such as cloud computing, blocking, and AI, to ensure your organization remains competitive in the market. We develop and implement enterprise grade CRM systems, ERP systems, and other applications such as inventory and human resources management systems to simplify your processes.`
  },
  {
    title: "Custom Software Application Development",
    description: `If you require a specific application, we can help you with that. We develop custom applications that fully utilize your company's existing infrastructure, reducing the chances of problems across the various departments. We develop web, mobile, and desktop applications that can be used to improve organizational productivity while fostering customer interactions. Our applications are designed for ease of use while at the same time providing advanced features that are tailored for your business.`
  },
  {
    title: "System Modernization",
    description: `Increased inefficiency of a business can be a byproduct of obsolete software. We assist in upgrading legacy systems and enhancing security alongside boosting performance through technological advancements. Updating these services includes reengineering older applications as well as migrating them to the cloud while adjusting security standards. This promotes improved overall efficiency alongside reduced maintenance costs.`
  },
  {
    title: "Cloud Software Development",
    description: `We provide a tailored software solution which incorporates cloud technology in order to enhance security, access, and scalability. Our <a href='/Cloud_Services' class='text-[#7716BC] hover:underline'>managed cloud services</a> allow for simple integration with other business applications and enable real-time collaboration. Companies can utilize our software as a service or an infrastructure platform to encourage Paas or IaS, further improving digital transformation.`
  },
  {
    title: "Integration of AI and ML",
    description: `Alongside business automation and insightful analysis, we also provide tailored software development. This includes the building of AI powered tools such as predictive analytics technologies, chatbots, and intelligent solutions to enhance efficiency and decision making in a business. All these tools make use of <a href='/ML' class='text-[#7716BC] hover:underline'>machine learning solutions</a>, whether it be to learn patterns, or a chatbot improving its understanding with more interactions, or an intelligent system learning from data.`
  },
  {
    title: "Compliance and Cybersecurity",
    description: `There is no doubt that security is an important element in every digital solution. We also use advanced security measures including the most recent encryption techniques to ensure compliance standards while safeguarding your software from cyber threats. Your business operations are protected by our identity management regulatory compliance alongside data protection solutions.`
  },
  {
    title: "Bespoke Software Solution",
    description: `Our company seeks to design and build a bespoke software solution that best addresses your unique business challenges. Be it custom software development, ERP systems, or <a href='/AI' class='text-[#7716BC] hover:underline'>AI business solutions</a>, we make sure our products evolve as you scale. Every company is unique and that is why we ensure our products offer specific solutions to specific problems. We try to build systems that can allow for future growth and changes in the business environment.`
  }
];

// Card component
const Card = ({ title, description }) => (
  <div className="bg-white border border-gray-200 shadow-lg rounded-lg p-4">
    <h3 className="text-[#7716BC] text-base md:text-lg font-semibold mb-3 md:mb-4">
      {title}
    </h3>
    <p
      className="text-sm md:text-base text-justify"
      dangerouslySetInnerHTML={{ __html: description }}
    />
  </div>
);

// Section3 component with grid layout
const Section3 = () => (
  <div className="container mb-10 md:mb-24">
    <h2 className="text-xl md:text-3xl md:leading-10 text-center font-semibold">
      Our <span className="text-[#F245A1]">Custom Software Development</span> Services
    </h2>

    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mt-6">
      {services.map((service, idx) => (
        <Card key={idx} title={service.title} description={service.description} />
      ))}
    </div>
  </div>
);

export default Section3;
