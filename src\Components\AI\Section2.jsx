import React from "react";

const Section2 = ({lefttext, righttext, fontClasses}) => {
  return (
    <section className="max-w-[90%] mx-auto text-justify flex flex-col md:flex-row justify-center items-center  my-10 md:my-20">
      <div className="p-1 md:p-10 flex-1">
        <p className={`${fontClasses? fontClasses:"text-[#7716BC] text-lg md:text-3xl font-semibold md:leading-[30px]"}`}>
          {lefttext}
        </p>
      </div>
      <div className="p-1 md:p-10 border-t-2 md:border-t-0 md:border-l-4 border-[#F245A1] flex-1">
        <p
        className="font-normal text-lg md:text-xl"
        dangerouslySetInnerHTML={{ __html: righttext }}
      />
      </div>
    </section>
  );
};

export default Section2;
