import Image from "next/image";
import React from "react";

const ServicePage2 = () => {
  return (
    <section className="bg-pink-100">
      <div className="max-w-[85%] mx-auto py-8 flex justify-center items-center gap-10">
        <div className="flex-1">
          <h2 className="text-4xl font-semibold leading-[57px] ">
            Why your Business needs <br /> Custom Software
          </h2>
          <h4 className="text-2xl leading-[36px] text-[#7716BC] font-semibold py-4">
            50% creativity, 50% On-Demand Solutions
          </h4>
          {/* <p>
        Our experienced developers and designers strategize solutions
        according to your company/business demands. We replace conventional
        methods with innovative and updated solutions to deliver a futuristic
        and scalable product that caters to your needs.  We bring to the table
        what others don't–services that are 3 times faster, cheaper, and
        BETTER!  Introducing ReOps—a combination of 2 main heroes of our
        organization i.e. AI and DevOps. By adapting and extending what’s
        already out there, ReOps allows a smooth and natural adoption for
        DevOps practitioners enabling your software with efficiency and
        productivity like never before.
      </p>
      <p>
        We bring to the table what others don't–services that are 3 times
        faster, cheaper, and BETTER! 
      </p>
      <p>
        Introducing ReOps—a combination of 2 main heroes of our organization
        i.e. AI and DevOps. By adapting and extending what’s already out
        there, ReOps allows a smooth and natural adoption for DevOps
        practitioners enabling your software with efficiency and productivity
        like never before.
      </p> */}

          <p className="text-lg leading-7 font-normal">
            Our experienced developers and designers strategize solutions
            according to your company/business demands. We replace conventional
            methods with innovative and updated solutions to deliver a
            futuristic and scalable product that caters to your needs.
          </p>
          <p className="text-lg leading-7 font-normal my-4">
            We bring to the table what others don't–services that are 3 times
            faster, cheaper, and BETTER!
          </p>
          <p className="text-lg leading-7 font-normal">
            Introducing ReOps—a combination of 2 main heroes of our organization
            i.e. AI and DevOps. By adapting and extending what’s already out
            there, ReOps allows a smooth and natural adoption for DevOps
            practitioners enabling your software with efficiency and
            productivity like never before. We bring to the table what others
            don't–services that are 3 times faster, cheaper, and BETTER!
            Introducing ReOps—a combination of 2 main heroes of our organization
            i.e. AI and DevOps. By adapting and extending what’s already out
            there, ReOps allows a smooth and natural adoption for DevOps
            practitioners enabling your software with efficiency and
            productivity like never before.
          </p>
        </div>
        <div className="flex-1 w-[536px] h-[450px] relative">
          <Image
            src="/Images/custom_software_2_1.png"
            alt="custom"
            layout="fill"
            objectFit="cover" // or "contain" based on your requirement
          />
        </div>
      </div>
    </section>
  );
};

export default ServicePage2;
