import PinkTopCard from '@/Components/PWA_development/PinkTopCard'
import React from 'react'
import BlueTopCard from './BlueTopCard'

const Section9 = ({ cardData, heading, spanHeading, bluespanHeading, cardHeight, gridCols }) => {
  return (
    <div className="w-[90%] mx-auto mb-10 md:mb-24 my-12">
      <div className="mb-10">
        <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold capitalize">
          <span className="text-[#F245A1]">{spanHeading}</span> {heading}{" "}
          <span className="text-[#7716BC]">{bluespanHeading}</span>
        </h2>
      </div>
      <div className={`grid grid-cols-1  ${gridCols ? gridCols : "md:grid-cols-2 md:w-[70%]"}  gap-6 mx-auto`}>
        {cardData.map((card, index) => (
          <div key={index} className=" flex justify-center mb-5 items-center">
            <BlueTopCard title={card.title} description={card.description} PinkTopCardheight={cardHeight} />
          </div>
        ))}
      </div>
    </div>
  )
}

export default Section9