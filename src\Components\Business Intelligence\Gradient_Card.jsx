import React from "react";

const Gradient_card = ({ title, description, height }) => {
  return (
    <>
      <div className={`block w-[90vw] md:max-w-sm ${height? height: ""} overflow-hidden  mx-auto bg-gradient-to-br from-pink-500 to-purple-700 text-white p-6 rounded-lg`}>
        <h2 className="text-lg md:text-xl md:leading-8 font-bold">
          {title}
        </h2>
        <p className="text-sm md:text-base text-justify my-1 md:my-3">{description}</p>
      </div>
    </>
  );
};

export default Gradient_card;
