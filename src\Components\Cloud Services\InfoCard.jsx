import Image from "next/image";

const InfoCard = ({ imgSrc, altText, title, description }) => {
  return (
    <>
      <div className="w-full md:max-w-xl  border border-pink-500 p-2 md:p-4 rounded-lg shadow-sm">
        <div className="flex items-center gap-1">
          <Image
            src={imgSrc}
            alt={altText}
            width={32}
            height={32}
            className="flex-shrink-0"
          />
          <h3 className="font-medium text-sm md:text-lg">
            <span className="font-bold">{title}: </span>
          </h3>
        </div>
        <p className="text-justify md:text-base text-sm md:ml-8">{description}</p>
      </div>
      
    </>
  );
};

export default InfoCard;
