import React from "react";

const Section7 = ({ heading, spanheading, cardData }) => {
  return (
    <div className="bg-[#1F62EA26] py-5">
      <div className="w-[90%] mx-auto mb-10 md:mb-24">
        <div className="my-4">
          <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
            {heading} <span className="text-[#F245A1]">{spanheading}</span>
          </h2>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-8 mt-6">
          {cardData.map((card, index) => (
            <div
              key={index}
              className="border border-[#7716BC] rounded-lg bg-white p-4 min-w-lg mx-auto"
            >
              <h2 className="text-base md:text-lg my-2 font-semibold">
                {card.title}
              </h2>
              <p className="text-base mb-2 text-justify">
                {card.description.split("->").map((text, idx) => (
                  <React.Fragment key={idx}>
                    {idx === 0 ? (
                      <span
        
        dangerouslySetInnerHTML={{ __html: text }}
      />
                       // for the first part of the description, no list
                    ) : (
                      <>
                        <ul className="list-disc pl-6">
                          <li>{text.trim()}</li>
                        </ul>
                      </>
                    )}
                  </React.Fragment>
                ))}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Section7;
