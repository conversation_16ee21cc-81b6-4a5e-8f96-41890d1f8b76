"use client";

import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/pagination";
import { Pagination } from "swiper/modules";
import Card from "../Cloud Services/Card";

const cardData = [
  {
    title: "Web Application Integratiom",
    description:
      "Whether you’re using internal systems or third-party apps, our web application integration services help connect disparate web-based platforms. By linking applications, we ensure data consistency and improved operational efficiency across your enterprise.",
  },
  {
    title: "App-to-App Integration",
    description:
      "Whether you’re using internal systems or third-party apps, our web application integration services help connect disparate web-based platforms. By linking applications, we ensure data consistency and improved operational efficiency across your enterprise.",
  },
  {
    title: "Mobile App Integration",
    description:
      "Valueans offers cutting-edge mobile app integration solutions to enhance your mobile-first strategies. Our services allow your mobile apps to communicate with other enterprise systems, providing you with a unified experience and access to data anytime, anywhere.",
  },
  {
    title: "Cloud-to-Cloud Application Integration",
    description:
      "With businesses relying on multiple cloud environments, cloud-to-cloud application integration becomes critical. Our experts specialize in cloud app integration to ensure that your cloud-based services work together effortlessly, providing real-time data synchronization and scalability.",
  },
  // Add more cards here if needed
];

const CardCarousel_2 = () => {
  return (
    <Swiper
      spaceBetween={20}
      breakpoints={{
        640: { slidesPerView: 1 },
        768: { slidesPerView: 2 },
        1024: { slidesPerView: 3 },
      }}
      pagination={{ clickable: true }}
      modules={[Pagination]}
    >
      {cardData.map((item, index) => (
        <SwiperSlide key={index}>
          <Card title={item.title} description={item.description} />
        </SwiperSlide>
      ))}
    </Swiper>
  );
};

export default CardCarousel_2;
