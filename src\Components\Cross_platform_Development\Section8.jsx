import React from "react";
// import HorizontalColoredCard from "../PWA_development/HorizontalColoredCard";
const HorizontalColoredCard = ({
  bgColor,
  heading,
  paragraph1,
  paragraph2,
  paragraph3,
  listContent,
  spanHeading,
  bold2,
  bold3,
  bold1,
}) => {
  return (
    <div className={`${bgColor ? bgColor : ""} p-4 space-y-4 rounded-md`}>
      <h3 className="text-lg md:text-xl font-semibold">
        <span className="text-[#7716BC]">{spanHeading}</span> {heading}
      </h3>
      <div>
        <span className="font-semibold text-sm md:text-base text-justify">
          {bold1}
        </span>
        <p className="text-sm md:text-base text-justify">{paragraph1}</p>
      </div>
      <div>
        <span className="font-semibold text-sm md:text-base text-justify">
          {bold2}
        </span>
        <p className="text-sm md:text-base text-justify">{paragraph2}</p>
      </div>
      <div>
        <span className="font-semibold text-sm md:text-base text-justify">
          {bold3}
        </span>
        <p className="text-sm md:text-base text-justify">{paragraph3}</p>
      </div>
      <ul className="list-disc ml-4">
        {listContent?.map((item, index) => (
          <li key={index} className="text-sm md:text-base">
            {item}
          </li>
        ))}
      </ul>
    </div>
  );
};

const Section8 = () => {
  return (
    <div className="md:w-[95%] mx-auto my-10 md:my-24">
      <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
        <span className="text-[#F245A1]">Case Studies</span> and Success Stories
      </h2>
      <div className="items-center gap-3 md:gap-6 my-[32px] mx-[20px] md:mx-[75px]">
        <div className="bg-[#F245A126] flex flex-col gap-3 md:flex-row md:justify-between p-4 md:p-8">
          <HorizontalColoredCard
            spanHeading={"Case Study 1:"}
            heading={"Mobile App for E-Commerce Businesses"}
            bold1={"Challenge:"}
            paragraph1={
              "Develop a mobile app for the retail business that works seamlessly on both iOS and Android while minimizing development costs."
            }
            bold2={"Solution:"}
            paragraph2={
              "A cross-platform application based on React Native, featuring easy navigation, real-time inventory updates, and secure payment processing."
            }
            bold3={"Result:"}
            paragraph3={
              "User engagement increased by 35%, and development costs were reduced by 50%, leading to higher sales and improved customer retention."
            }
            bgColor={"bg-[#794CEC26] md:w-[40%]"}
          />

          <HorizontalColoredCard
            spanHeading={"Case Study 2:"}
            heading={"App for Healthcare Consultation"}
            bold1={"Challenge:"}
            paragraph1={
              "A healthcare startup needed a telemedicine application that could be used on different devices."
            }
            bold2={"Solution:"}
            paragraph2={
              "A Flutter-based application with encrypted video calls, AI-supported symptom checking, and cloud integration for medical record storage."
            }
            bold3={"Result:"}
            paragraph3={
              "The app garnered 100,000+ downloads in three months, significantly improving communication between patients and doctors, and increasing accessibility to healthcare services."
            }
            bgColor={"bg-[#F245A126] md:w-[40%]"}
          />
        </div>
        <div className="flex flex-col gap-3 md:flex-row md:justify-center  items-center mt-5 bg-[#794CEC26] p-4 md:p-8">
          <HorizontalColoredCard
            spanHeading={"Case Study 3:"}
            heading={"Mobile App for On-Demand Services"}
            bold1={"Challenge:"}
            paragraph1={
              "A service marketplace required a feature-rich mobile application for cross-industry freelancer service booking, including tracking and push notifications."
            }
            bold2={"Solution:"}
            paragraph2={
              "A hybrid app development service was used to build an application with geolocation tracking, instant booking, and secure payments via Xamarin."
            }
            bold3={"Result:"}
            paragraph3={
              "The application saw a 40% increase in bookings within six months and successfully expanded into new regions."
            }
            bgColor={"bg-[#794CEC26] md:w-[40%]"}
          />
        </div>
      </div>
      <div className="bg-[#350668] p-4 md:p-8 text-white text-center rounded-md mx-[20px] md:mx-[75px]">
        <p className="text-base md:text-lg">
          With our expertise in hybrid app development company solutions, we
          ensure that businesses get the best applications tailored to their
          industry needs.
        </p>
      </div>
    </div>
  );
};

export default Section8;
