import React from "react";
import ServiceCount from "../Services/ServiceCount";
import ServiceLifecycleCard from "../Services/ServiceLifecycleCard";
import Connector7 from "../Connectors/Connector7";

const Process = () => {
  const discoveryAndPlanning = [
    "Thoroughly examine your requirements",
    "Step-by-step planning",
  ];

  const architecturePrototyping = [
    "Make sure architecture prototyping incorporates vital use cases and user stories on a given architecture framework",
  ];

  const uiUxDesign = [
    "Design wireframes",
    "Ask for your feedback",
    "Implement the best design strategy",
  ];

  const frontAndBackEndDevelopment = [
    "Apply the best practices and tech tools for building web software",
    "Put together the database, frontend, and backend functionality",
    "Ask for your feedback",
  ];

  const testing = [
    "QA Specialists implement full-cycle testing",
    "Ensure the application’s performance and functionality",
  ];

  const deployment = [
    "Prepare the web app to be deployed",
    "Ask for your feedback",
    "Make sure everything is running smoothly",
    "Deploy the app",
  ];

  const supportAndMaintenance = [
    "Be available for post-implementation for maintenance and support services like adding new features, bug fixes, or delivering regular updates",
  ];
  return (
    <div className="w-[90vw] mx-auto mb-24">
      <h2 className="text-center text-4xl mb-10">
        Web App Developmemt <span className="text-[#F245A1]">Process</span>{" "}
      </h2>
      <div className="relative">
        <div className="flex justify-center items-center gap-8 mb-20 ">
          <div className="flex-col justify-center items-center">
            <ServiceLifecycleCard
              title="Discovery and Planning"
              items={discoveryAndPlanning}
            />
            <ServiceCount count={"1"} />
          </div>
          <div className="flex-col justify-center items-center">
            <ServiceLifecycleCard title="UI/UX Design" items={uiUxDesign} />
            <ServiceCount count={"3"} />
          </div>
          <div className="flex-col justify-center items-center">
            <ServiceLifecycleCard title="Testing" items={testing} />
            <ServiceCount count={"5"} />
          </div>
          <div className="flex-col justify-center items-center">
            <ServiceLifecycleCard
              title="Support and Maintenance"
              items={supportAndMaintenance}
            />
            <ServiceCount count={"7"} />
          </div>
        </div>
        <div className="absolute top-[48%] left-[20%]">
          <div
            className="w-[230px] h-[120px] border-dashed border-b-2 border-l-2 border-gray-700 absolute"
            style={{
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
            }}
          />
        </div>
        <div className="absolute top-[50%] left-[31%]">
          <div
            className="w-[70px] h-[115px] border-dashed border-t-2 border-l-2 border-gray-700 absolute"
            style={{
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
            }}
          />
        </div>
        <div className="absolute top-[49%] left-[46%]">
          <div
            className="w-[320px] h-[100px] border-dashed border-b-2 border-l-2 border-gray-700 absolute"
            style={{
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
            }}
          />
        </div>
        <div className="absolute top-[47%] left-[59%]">
          <div
            className=" h-[90px] border-dashed  border-r-2 border-gray-700 absolute"
            style={{
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
            }}
          />
        </div>
        <div className="absolute top-[57%] left-[74%]">
          <div
            className="w-[400px]  border-dashed border-b-2  border-gray-700 absolute"
            style={{
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
            }}
          />
        </div>
        <div className="absolute top-[47%] left-[87%]">
          <div
            className=" h-[90px] border-dashed  border-r-2 border-gray-700 absolute"
            style={{
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
            }}
          />
        </div>
        <div className=" ml-[15%] flex justify-center items-center gap-8 mt-20 relative">
          <div className="flex-col justify-center items-center absolute top-[5%] -left-[1%]">
            <ServiceCount count={"2"} />
            <ServiceLifecycleCard
              title="Arcitecture Prototyping"
              items={architecturePrototyping}
            />
          </div>
          <div className="flex-col justify-center items-center ml-[35%]">
            <ServiceCount count={"4"} />
            <ServiceLifecycleCard
              title="Front & Backend Development"
              items={frontAndBackEndDevelopment}
            />
          </div>
          <div className="flex-col justify-center items-center ml-5">
            <ServiceCount count={"6"} />
            <ServiceLifecycleCard title="Deployment" items={deployment} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Process;
