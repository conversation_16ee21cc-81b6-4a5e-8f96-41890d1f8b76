import React from "react";
import Card from "./Card_2";

const Section6 = () => {
  return (
    <div className="w-[85%] mx-auto mb-10 md:mb-24">
      <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
        Why Choose Valueans’{" "}
        <span className="text-[#F245A1]">Data Engineering Solutions</span>  
      </h2>
      <div className="flex flex-col justify-center items-center gap-3 md:gap-6 my-3 md:my-6">
        <div className="flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6">
          <Card
            title={"Industry-Specific Solutions"}
            description={
              "We are aware of the data difficulties in your industry. Our data engineering specialists assist you in developing data solutions that satisfy industry standards and offer value. "
            }
          />
          <Card
            title={"Quick Insights"}
            description={
              "Smarter choices may be made throughout your business with the support of our digital and technical advice solutions, which help you swiftly create insights. "
            }
          />
          <Card
            title={"Smart Automation"}
            description={
              "Automate data flows to reduce mistakes and save money. By doing this, your staff can focus on important tasks and become more productive. "
            }
          />
        </div>
        <div className="flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6">
          <Card
            title={"Future-Ready Architecture"}
            description={
              "Create a robust data environment that can grow with your company. Our robust, high-end solutions address present issues and save costly changes later."
            }
          />
          <Card
            title={"Strong Governance"}
            description={
              "Make sure your data procedures are sound and in line with the law. By doing this, you may reduce risks, adhere to rules, and gain confidence in your data-driven choices."
            }
          />
        </div>
      </div>
    </div>
  );
};

export default Section6;
