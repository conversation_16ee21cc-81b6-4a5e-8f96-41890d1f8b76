import Image from "next/image";
import React from "react";

const TestimonialCard = ({name,review,imageSrc,title}) => {
  return (
    <div className="w-full p-6 bg-white border border-gray-200 rounded-lg shadow hover:bg-gray-100">
      <div className="flex items-center space-x-4 mb-4">
        <div className="w-[90px] h-[90px] rounded-full overflow-hidden flex-shrink-0">
          <Image
            src={imageSrc}
            alt={name}
            width={90}
            height={90}
            className="rounded-full object-cover"
          />
        </div>
        <div>
          <h5 className="text-lg font-bold tracking-tight">
            {name}
          </h5>
          <h6 className="text-sm text-gray-600">{title}</h6>
        </div>
      </div>

      <p className="font-normal text-gray-700">
        {review}
      </p>
    </div>
  );
};

export default TestimonialCard;
