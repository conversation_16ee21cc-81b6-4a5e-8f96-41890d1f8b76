import React from "react";
import ServiceCount from "../Services/ServiceCount_2";
import Card from "./Card_2";

const Section6 = () => {
  return (
    <section className="bg-pink-100 mb-10 md:mb-24 p-4 md:p-10">
      <div className="w-[90%] mx-auto">
        <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
          Our <span className="text-[#F245A1]">BI Implementation</span> Process
        </h2>
        <p className="text-base md:text-xl text-center">
          Meticulously planned, discussed, and executed. 
        </p>
        <div className="flex flex-col justify-center items-center gap-3 md:gap-6 my-3 md:my-6">
          <div className="flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6">
            <div className="flex justify-center items-center gap-1">
              <ServiceCount>1</ServiceCount>
              <Card
                title={"Discovery and planning"}
                description={
                  "We start by understanding your business needs, data sources, and existing systems. We work with your team to develop a tailored BI roadmap that outlines the aim, timelines, and resources required for a successful deployment."
                }
              />
            </div>
            <div className="flex justify-center items-center gap-1">
              <ServiceCount>2</ServiceCount>
              <Card
                title={"Data Preparation"}
                description={
                  "Our team organizes and prepares your data, making sure it’s clean, consistent, and ready for analysis."
                }
              />
            </div>
            <div className="flex justify-center items-center gap-1">
              <ServiceCount>3</ServiceCount>
              <Card
                title={"Tool Selection and Development"}
                description={  
                  "BI tools that resonate with your needs are chosen which help us create analytical models, detailed reports, and custom dashboards."
                }
              />
            </div>
          </div>
          <div className="flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6">
            <div className="flex justify-center items-center gap-1">
              <ServiceCount>4</ServiceCount>
              <Card
                title={"Deployment and Integration"}
                description={
                  " Deployment and integration with systems that are already in place without any risk involved."
                }
              />
            </div>
            <div className="flex justify-center items-center gap-1">
              <ServiceCount>5</ServiceCount>
              <Card
                title={"Training and Support"}
                description={
                  "We also hold seminars and training modules so that your in-house team has all the necessary knowledge to get the most out of your investment. "
                }
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Section6;
