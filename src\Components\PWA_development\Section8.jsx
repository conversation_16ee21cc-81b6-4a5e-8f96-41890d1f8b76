import Image from "next/image";
import InfoCard from "../Cloud Services/InfoCard";

const cardData = [
  {
    imgSrc: "/Images/service_frame.png",
    altText: "Performance Icon",
    title: "Faster Performance",
    description:
      "PWAs are designed to load quickly, even on slow networks, enhancing the user experience and reducing bounce rates.",
  },
  {
    imgSrc: "/Images/service_frame.png",
    altText: "Offline Icon",
    title: "Offline Accessibility",
    description:
      "Using service workers, PWAs can function even when there is no internet connection, allowing users to access essential features anytime.",
  },
  {
    imgSrc: "/Images/service_frame.png",
    altText: "Engagement Icon",
    title: "Increased User Engagement",
    description:
      "Push notifications and smooth navigation help keep users engaged, increasing customer retention rates.",
  },
  {
    imgSrc: "/Images/service_frame.png",
    altText: "Cost Icon",
    title: "Cost-Effective Development",
    description:
      "Since PWAs work across all devices, businesses can save costs by developing a single app instead of separate mobile and web applications. ",
  },
  {
    imgSrc: "/Images/service_frame.png",
    altText: "SEO Icon",
    title: "Improved SEO & Discoverability",
    description:
      "Unlike native apps, PWAs are indexed by search engines, improving organic traffic and discoverability.",
  },
  {
    imgSrc: "/Images/service_frame.png",
    altText: "Store Icon",
    title: "No App Store Dependency",
    description:
      "PWAs end the need for app store approvals and updates, giving businesses full control over updates and deployment.",
  },
];
 
const Section8 = () => {
  return (
    <div className="bg-blue-100 py-3">
      <div className="flex flex-col justify-center w-[90%] mx-auto mb-10 md:my-24 gap-5 md:justify-between items-center">
        <h2 className="text-xl md:text-3xl text-center font-semibold my-1">
          Benefits of  
          <span className="text-[#F245A1]">Progressive Web Apps</span> for
          Businesses
        </h2>

        <div className="flex flex-col md:flex-row justify-between gap-8">
          <div className="min-h-[250px] md:min-h-0 w-full md:w-[40%]  relative">
            <Image
              src={"/Images/PWA10.jpeg"}
              alt="Progressive Web App Development Services"
              fill
              className="object-cover"
            />
          </div>
          <div className="md:w-[60%] my-4 grid grid-cols-1 md:grid-cols-2 gap-4">
            {cardData.map((card, index) => (
              <InfoCard
                key={index}
                imgSrc={card.imgSrc}
                altText={card.altText}
                title={card.title}
                description={card.description}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Section8;
