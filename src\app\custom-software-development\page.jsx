import Faq from "@/Components/Faq/Faq";
import Section1 from "@/Components/AI/Section1";
import Section2 from "@/Components/Software_Development/Section2";
import Section3 from "@/Components/Software_Development/Section3";
import Section4 from "@/Components/Software_Development/Section4";
import Section5 from "@/Components/Software_Development/Section5";
import Section6 from "@/Components/Software_Development/Section6";
import Section7 from "@/Components/Software_Development/Section7";
import Section8 from "@/Components/Software_Development/Section8";
import React from "react";
export const metadata = {
  title: "Create businesses with enterprise software development services",
  description: "We provide specialized services in enterprise software development. Our b2b custom software services cater to various industries and improve efficiency and security.",
};

const page = () => {
   const accordionData = [
    {
      title: "What exactly is software development?",
      content: "Software Development is the process of building large-scale applications designed to serve the specific requirements of organizations which enhance efficiency, optimization, and decision-making processes.",
    },
    {
      title: "How much time does it take to build custom enterprise software?",
      content: "Time needed for development depends on the scope of the project, features, and complexity of the enterprise software which usually falls between a few months and surpasses a year.",
    },
    {
      title: "What Enterprise Software Industries are there?",
      content: "Industries like healthcare, finance, retail, manufacturing, logistics, education, and real estate benefit from enterprise software solutions tailored to their business domains.",
    },
    {
      title: "Do you offer ongoing maintenance and support",
      content: "Sure, we provide ongoing maintenance and updates, as well as security enhancements to ensure smooth performance of the software.",
    },
    {
      title: "What is the level of security for custom software? ",
      content: "Custom software is protected by most recent security measures, including encryption, access control, compliance and other security protocols that safeguard your data and operations. ",
    },
    {
      title: "Is it possible to link Software to existing systems?",
      content: "Yes, we make sure that there is integration with existing systems such as CRM, ERP, and other tools without any issues.",
    },
    {
      title: "Which technologies are you applying to development? ",
      content: "We work on modern technologies such as AI, machine learning, cloud computing, blockchain, and IoT which are used to create scalable solutions.",
    },
    {
      title: "Is it possible to personalize such products to suit business purposes that vary across the board? ",
      content: "Of course. We can modify the software systems to meet your business requirements.",
    },
  ];
  return (
    <div>
      <Section1
        backgroundImage={"/Images/SD-bg.jpeg"}
        heading={"Enterprise Software Development Services"}
        bannerText={"We Develop Businesses with Personalized Development Solutions"}
      />
      <Section2 />
      <Section3/>
      <Section4/>
      <Section5 />
      <Section6 />
      <Section7 />
      <Section8/>
      <Faq content={accordionData}/>
    </div>
  );
};

export default page;
