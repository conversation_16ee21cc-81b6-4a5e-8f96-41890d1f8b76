import React from "react";
import Image from "next/image";

const ChooseCard = ({ title, content }) => {
  return (
    <>
      <div className="w-full md:max-w-xl border border-pink-500 p-2 md:p-4 rounded shadow">
        <div className="flex items-center flex-shrink-0 gap-4">
          <Image
            src="/Images/service_frame.png"
            alt="Tick"
            width={32}
            height={32}
          />
          <h2 className="text-sm md:text-lg font-bold">{title}</h2>
        </div>
        <p className="font-medium text-sm md:text-base">{content}</p>
      </div>
    </>
  );
};

export default ChooseCard;
