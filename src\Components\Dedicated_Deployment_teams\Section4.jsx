import ServiceCard_2 from "../Card/ServiceCard_2";
const Section4 = () => {
  const DedicatedTeamData = [
    {
      title: "Complex Initiatives Involving Software Development",
      content:
        "A committed team with a variety of talents is helpful for projects requiring the integration of several technologies and domains. These teams' specialized knowledge and concentrated efforts enable them to perform well in challenging situations.",
    },
    {
      title: "Long-Term Projects",
      content:
        "For initiatives that are expected to take several months or even years to complete, consistency and dedication are essential. Continuous involvement from a committed team guarantees steady development and a thorough comprehension of the goals and challenges of the project.",
    },
    {
      title: "Product Development and Startups",
      content:
        "When creating new goods, startups and existing companies often run across changing market conditions and demands. dedicated groups provide the required adaptability, which eases pivoting.",
    },
    {
      title: "Operations Scaling Up",
      content:
        "Businesses on the verge of growing their development capacity may find it difficult and expensive to scale up effectively. A committed, well-organized team provides a calculated answer without incurring the costs and hassles of recruiting more internal employees.",
    },
    {
      title: "Projects with Changing Specifications",
      content:
        "Dedicated teams' flexibility is crucial for projects whose specifications are subject to change in response to user input or industry developments. These teams make sure that solutions stay current and satisfy user, and market demands through constant iteration and modification.",
    },
    {
      title: "Specialized Tasks That Call for Specialized Knowledge",
      content:
        "Certain projects go beyond standard requirements and need specialized knowledge that is not accessible internally. You may choose specialists with the precise talents needed for particular projects by using the dedicated team model, which gives you access to a worldwide talent pool.",
    },
    {
      title: "Initiatives for Research and Development",
      content:
        "Software development initiatives that investigate new goods, markets, or technologies require a combination of flexibility and in-depth knowledge. These vital resources are supplied by committed teams, encouraging creativity and permitting changes in the project's course.",
    },
    {
      title: "Digital Transformations at the Enterprise Level",
      content:
        "There are several obstacles for big businesses trying to modernize their procedures and systems. Dedicated teams are prepared to oversee large, complex changes with a thorough plan that takes into account many business facets.",
    },
  ];
  return (
    <div className="w-[85vw] mx-auto mb-10 md:mb-24">
      <h2 className="text-2xl md:text-3xl md:leading-[57px] text-center font-semibold">
        Projects Where <span className="text-[#F245A1]">Valueans</span>{" "}
        Dedicated Development Team Helps
      </h2>
      <p className="md:w-[80%] md:mx-auto text-justify text-base md:text-xl">
        Projects with long schedules, changing requirements, and extremely specialized demands are best suited for dedicated software development teams. Examine situations in which they excel:  
      </p>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-6 p-6">
        {DedicatedTeamData.slice(0, 6).map((card, index) => (
          <ServiceCard_2
            key={index}
            title={card.title}
            content={card.content}
          />
        ))}

        {/* Conditionally render the last two cards centered */}
        {DedicatedTeamData.length > 3 && (
          <div className="col-span-1 md:col-span-3 flex flex-col md:flex-row justify-center gap-6">
            <ServiceCard_2
              key={6}
              title={DedicatedTeamData[6].title}
              content={DedicatedTeamData[6].content}
            />
            <ServiceCard_2
              key={7}
              title={DedicatedTeamData[7].title}
              content={DedicatedTeamData[7].content}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default Section4;
