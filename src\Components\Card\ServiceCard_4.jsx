import React from "react";

const ServiceCard_4 = ({
  titleHighlight,
  title,
  description,
  bgColor = "bg-white",
}) => {
  return (
    <div
      className={`block w-[90%] md:w-[65vw] py-3 md:py-6 px-5 md:px-10  border border-gray-200 rounded-lg shadow ${bgColor}`}
    >
      <h5 className={`mb-2 text-xl text-[32px]  font-semibold tracking-tight`}>
        <span className="text-[#F245A1]">{titleHighlight}</span> <br /> {title}
      </h5>
      <p className=" font-normal text-base md:text-xl">{description}</p>
    </div>
  );
};

export default ServiceCard_4;
