// "use client";

// import { Card } from "@/components/ui/card";
// import {
//   Carousel,
//   CarouselContent,
//   CarouselItem,
//   CarouselNext,
//   CarouselPrevious,
// } from "@/components/ui/carousel";
// import { ArrowRight } from "lucide-react";
// import { useState } from "react";
// import useEmblaCarousel from "embla-carousel-react";

// const expertiseData = [
//   {
//     title: "Healthcare",
//     description:
//       "The healthcare industry demands precision, security, & efficiency to manage sensitive patient data & streamline operations. Valueans excels in delivering customized solutions that enhance patient care, optimize workflows, and ensure compliance with regulatory standards.",
//     image: "/placeholder.svg?height=400&width=600",
//   },
//   {
//     title: "Logistic",
//     description:
//       "In logistics, efficiency and real-time tracking are crucial for smooth supply chain operations. Valueans provides innovative solutions to enhance route planning, inventory management, and real-time visibility. Our tech-driven approach optimizes your logistics network for maximum efficiency and reliability.",
//     image: "/placeholder.svg?height=400&width=600",
//   },
//   {
//     title: "Travel",
//     description:
//       "The travel industry thrives on seamless booking experiences and personalized services. Valueans designs intuitive platforms that enhance user engagement and streamline booking processes. Our solutions ensure you stay ahead in delivering exceptional travel experiences and managing operations effectively.",
//     image: "/placeholder.svg?height=400&width=600",
//   },
//   {
//     title: "Technology",
//     description:
//       "We deliver cutting-edge technology solutions that drive innovation and digital transformation across industries. Our expertise helps businesses leverage the latest tech trends for sustainable growth.",
//     image: "/placeholder.svg?height=400&width=600",
//   },
// ];

// export default function IndustryExpertise() {
//   const [emblaRef, emblaApi] = useEmblaCarousel({
//     slidesToScroll: 1,
//     align: "start",
//   });
//   const [currentSlide, setCurrentSlide] = useState(0);

//   const onSelect = () => {
//     if (!emblaApi) return;
//     setCurrentSlide(emblaApi.selectedScrollSnap());
//   };

//   return (
//     <div className="py-16 px-4 max-w-7xl mx-auto">
//       <h2 className="text-4xl font-bold text-center mb-12">
//         Our <span className="text-purple-600">Industry</span> Expertise
//       </h2>

//       <Carousel
//         opts={{
//           align: "start",
//           loop: true,
//         }}
//         className="w-full"
//         onSelect={onSelect}
//         ref={emblaRef}
//       >
//         <CarouselContent className="-ml-4">
//           {expertiseData.map((expertise, index) => (
//             <CarouselItem
//               key={index}
//               className="pl-4 md:basis-1/2 lg:basis-1/3"
//             >
//               <Card className="relative overflow-hidden h-full">
//                 <div className="absolute inset-0">
//                   <img
//                     src={expertise.image}
//                     alt=""
//                     className="w-full h-full object-cover opacity-20"
//                   />
//                 </div>
//                 <div className="relative p-6 flex flex-col h-full">
//                   <h3 className="text-xl font-bold mb-2">{expertise.title}</h3>
//                   <p className="text-gray-600 text-sm flex-grow">
//                     {expertise.description}
//                   </p>
//                   <button className="w-10 h-10 rounded-full bg-purple-600 text-white flex items-center justify-center hover:bg-purple-700 transition-colors mt-4 self-end">
//                     <ArrowRight className="w-5 h-5" />
//                   </button>
//                 </div>
//               </Card>
//             </CarouselItem>
//           ))}
//         </CarouselContent>
//         <CarouselPrevious />
//         <CarouselNext />

//         <div className="flex justify-center gap-2 mt-8">
//           {expertiseData.map((_, index) => (
//             <button
//               key={index}
//               className={`w-3 h-3 rounded-full transition-colors ${
//                 currentSlide === index ? "bg-purple-600" : "bg-gray-300"
//               }`}
//               onClick={() => emblaApi?.scrollTo(index)}
//               aria-label={`Go to slide ${index + 1}`}
//             />
//           ))}
//         </div>
//       </Carousel>
//     </div>
//   );
// }
