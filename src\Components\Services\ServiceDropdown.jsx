import React from "react";
import Dropdown from "./Dropdown";



const ServiceDropdown = () => {
  return (
    <div className="my-24 ">
  
      <div className="flex flex-col items-center ml-80 mr-72">
        {/* Container for the two side-by-side dropdowns */}
        <div className="flex flex-wrap justify-between my-10 w-full ">
          {/* First Dropdown */}
          <div className="w-full md:w-[45%] md:mr-8">
            {" "}
            {/* Added md:mr-8 for margin-right on larger screens */}
            <Dropdown
              title="Web Application Development"
              content="Our skilled developers are trained to craft feature-rich Web Applications that deliver valuable information and interactive services resulting in higher user engagement. We provide the latest technology by combining AI and DevOps which saves your time and money ensuring increased scalability, maintainability, and flexibility for your applications."
            />
          </div>
          {/* Second Dropdown */}
          <div className="w-full md:w-[45%] ml-10">
            <Dropdown
              title="Mobile Application Development"
              content="At Valueans, we develop feature-packed futuristic mobile applications with user-friendly and sleek front ends. Our skillful Android developers make the user's experience 10x better by blending the latest technology, efficient development process, and custom mobile app specifications."
            />
          </div>
        </div>
        {/* Centered third dropdown */}
        <div className="w-full md:w-[50%]">
          <Dropdown
            title="Enterprise Software Development"
            content="At Valueans, we develop feature-packed futuristic mobile applications with user-friendly and sleek front ends. Our skillful Android developers make the user's experience 10x better by blending the latest technology, efficient development process, and custom mobile app specifications."
          />
        </div>
      </div>
    </div>
  );
};

export default ServiceDropdown;
