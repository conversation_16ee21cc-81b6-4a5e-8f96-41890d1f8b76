"use client";

const Tabs = ({ activeTab, setActiveTab }) => {

  const tabs = [
    "Web",
    "Mobile",
    "UI/UX Design",
    "Machine Learning",
    "Database",
  ];

  return (
    <div className="flex flex-wrap justify-center py-4 mt-[42px] gap-8 md:gap-10">
      {tabs.map((tab) => (
        <button
          key={tab}
          onClick={() => setActiveTab(tab)}
          className={`text-lg md:text-2xl text-center text-[#232536] hover:text-[#7716BC] font-medium transition-colors duration-300 ${
            activeTab === tab
              ? "text-[#7716BC] border-b-2 border-[#7716BC]"
              : ""
          }`}
        >
          {tab}
        </button>
      ))}
    </div>
  );
};

export default Tabs;
