"use client";
import { useState, useEffect } from "react";
import TestimonialCard from "./Card/TestimonialCard";
import Heading from "./Heading/Heading";

function TestimonialCarousel() {
  const [activeStep, setActiveStep] = useState(0);
  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchTestimonials = async () => {
      try {
        setLoading(true);
        const response = await fetch("https://api.valueans.com/api/testimonials/");
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        const normalizedData = data.map((item) => ({
          review: item.review,
          name: item.client_name,
          title: item.company,
          imageSrc: item.client_image,
        }));

        setReviews(normalizedData);
        setError(null);
      } catch (err) {
        console.error("Error fetching testimonials:", err);
        setError("Failed to load testimonials. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchTestimonials();
  }, []);

  const handleNext = () => {
    setActiveStep((prev) =>
      prev + 1 < reviews.length ? prev + 1 : 0
    );
  };

  const handleBack = () => {
    setActiveStep((prev) =>
      prev - 1 >= 0 ? prev - 1 : reviews.length - 1
    );
  };

  return (
    <section className="bg-pink-100 testimonial-section p-6 md:p-8 my-12 md:my-24">
      <div className="flex flex-col md:flex-row container my-6 md:my-8">
        <div className="w-full md:w-1/3 p-4 md:mr-16">
          <Heading>What our clients say about us</Heading>

          <div className="flex justify-center items-center mt-4">
            <button
              onClick={handleBack}
              className={`p-2 md:p-4 rounded-full mr-2 md:mr-4 flex items-center justify-center ${
                reviews.length <= 1
                  ? "bg-white text-gray-500 cursor-not-allowed"
                  : "bg-pink-500 hover:bg-pink-600 text-white"
              }`}
              disabled={reviews.length <= 1}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 md:h-6 w-5 md:w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                strokeWidth={2}
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>

            <button
              onClick={handleNext}
              className={`p-2 md:p-4 rounded-full flex items-center justify-center ${
                reviews.length <= 1
                  ? "bg-white text-gray-500 cursor-not-allowed"
                  : "bg-pink-500 hover:bg-pink-600 text-white"
              }`}
              disabled={reviews.length <= 1}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 md:h-6 w-5 md:w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                strokeWidth={2}
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          </div>
        </div>

        <div className="w-full md:w-2/3 mt-8 md:mt-0">
          {reviews.length > 0 && (
            <TestimonialCard
              key={activeStep}
              review={reviews[activeStep].review}
              name={reviews[activeStep].name}
              title={reviews[activeStep].title}
              imageSrc={reviews[activeStep].imageSrc}
            />
          )}
        </div>
      </div>
    </section>
  );
}

export default TestimonialCarousel;
