import React from "react";
import ServiceCount from "../Services/ServiceCount_2";
import ServiceLifecycle from "../Services/ServiceLifecycleCard_2";

const talkaboutneeds = [
  "We'll talk about your company's objectives, financial constraints, schedule, and requirements for quality control services. We'll assess if you require a <a href='/Dedicated_Deployment_teams' class='text-[#7716BC] hover:underline'>dedicated development team</a> or one of our other engagement types during your first conversation.",
];

const criteria = [
  "Based on your criteria and engagement model, we will develop a strategy that outlines our QA testing method. Additionally, we will put together your team of QA experts.",
];

const startworking = [
  "Our testers and QA engineers will go to work. To keep you updated, we will monitor metrics and update you on our progress during the software testing process.",
];

const Section6 = () => {
  return (
    <section className="bg-pink-100 mb-10 md:mb-24 p-5 md:p-10">
      <div className="w-[85%] mx-auto">
        <h2 className="text-2xl md:text-[38px] md:leading-[57px] text-center font-semibold">
          Our QA and Testing <span className="text-[#F245A1]">Process</span>
        </h2>

        <div>
          <div className="flex flex-col md:flex-row justify-center md:justify-between gap-5 items-center my-5 md:my-10">
            <div className="flex justify-center  items-center gap-1 ">
              <ServiceCount>1</ServiceCount>
              <ServiceLifecycle
                title={"Talk about your needs"}
                items={talkaboutneeds}
              />
            </div>
            <div className="flex justify-center items-center gap-1">
              {" "}
              <ServiceCount>2</ServiceCount>
              <ServiceLifecycle
                title={"Talk about your needs"}
                items={startworking}
              />
            </div>
            <div className="flex justify-center items-center gap-1">
              {" "}
              <ServiceCount>3</ServiceCount>
              <ServiceLifecycle title={"Start working"} items={criteria} />
            </div>
          </div>
          <div className="flex justify-center items-center"></div>
        </div>
      </div>
    </section>
  );
};

export default Section6;
