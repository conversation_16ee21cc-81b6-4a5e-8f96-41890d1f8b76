import React from "react";

const AI_serviceflow = () => {
  const discoveryConsultationItems = [
    "In-depth exploration of your business challenges and objectives",
    "Perform proper research and analysis",
    "Recommend tailored solutions accordingly",
  ];

  const strategyFormulationItems = [
    "Step-by-step strategizing and planning",
    "Define clear goals and success metrics",
    "Establish a timeline",
  ];

  const solutionDesignItems = [
    "Select the right algorithms and tools to meet your specific needs",
    "Ensure that the solution aligns perfectly with your business requirements",
  ];

  const developmentIntegrationItems = [
    "Build and integrate the AI system into your existing infrastructure",
    "Include rigorous testing to ensure smooth operation and minimal disruption",
  ];

  const testingRefinementItems = [
    "Comprehensive testing is performed to validate the AI solution’s performance",
    "Make necessary adjustments and optimizations based on real-world feedback and performance metrics",
  ];

  const developmentSupportItems = [
    "Provide continuous support and maintenance",
    "Ensure your AI solution remains efficient and up-to-date",
  ];

  return (
    <div>
      <section className="bg-pink-100 py-8 mb-24">
        <div className="w-[85vw] mx-auto">
          <h3 className="text-4xl text-center my-4">
            Our AI Development Process
          </h3>
          <div className="w-[70%] mx-auto">
            <p className="text-xl text-center leading-7 mb-4">
              At Valueans, we excel as one of the leading AI software
              development companies with a structured and strategic approach to
              AI solutions development. Our process ensures your AI project is
              executed seamlessly and delivers impactful results:
            </p>
          </div>
          
        </div>
      </section>
    </div>
  );
};

export default AI_serviceflow;
