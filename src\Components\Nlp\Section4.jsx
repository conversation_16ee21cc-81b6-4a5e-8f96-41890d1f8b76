import Image from "next/image";

const Card = ({ imgsrc, altsrc, title, description }) => {
  return (
    <div className="max-w-lg bg-white border border-gray-200 rounded-xl shadow-lg">
      <a href="#">
        <Image
          className="rounded-t-lg"
          src="/Images/tech_img.png"
          alt=""
          width={587}
          height={416}
        />
      </a>
      <div className="p-5">
        <a href="#">
          <h5 className="mb-2 text-base md:text-lg text-[#7716BC] font-medium tracking-tight">
            {title}
          </h5>
        </a>
        <p className="mb-3 text-sm md:text-base font-normal">{description}</p>
      </div>
    </div>
  );

};
const Section4 = () => {
  return (
    <div className="mx-[75px] mb-10 md:mb-24">
      <h2 className="text-xl md:text-[28px] text-center font-semibold mb-1">
        NLP Solutions at
        <span className="text-[#7716BC]">Valueans</span>
      </h2>

      <div className="flex flex-col justify-center items-center gap-8 mb-10 md:mb-24 mt-6 md:mt-[42px]">
        <div className="flex flex-col md:flex-row justify-center items-center gap-4 md:gap-8">
          <Card
            title={"E-Commerce & Retail"}
            description={
              "We create mobile responsive single page applications for e-commerce stores that allow users to browse offline, receive push alerts, and order products without any added clicks. All of these components boost business performance which leads to increased revenue."
            }
          />
          <Card
            title={"Healthcare"}
            description={
              "Our self-service progressive web applications enable secure mobile video conferencing for clinics, telemedicine providers, and hospitals that are HIPAA compliant. They consist of basic record management, real-time notifications, as well as scheduling of appointments."
            }
          />
        </div>
        <div className="flex flex-col md:flex-row justify-center items-center gap-4 md:gap-8">
          {" "}
          <Card
            title={"Education & e-Learning"}
            description={
              "Through our e-learning platforms, we allow students prompt access to courses as well as quizzes and educational materials. Our PWA technology allows teaching and learning in offline mode and provides video lectures alongside real-time collaboration."
            }
          />
          <Card
            title={"Finance and Banking"}
            description={
              "We specialize in developing self-service progressive web applications for banks, fintech companies and other financial institutions that allow clients to perform digital transactions and manage accounts while receiving alerts, all care of non-disclosure password protection against fraud."
            }
          />
        </div>
        <div className="flex flex-col md:flex-row justify-center items-center gap-4 md:gap-8">
          {" "}
          <Card
            title={"Travel & Hospitality"}
            description={
              "Our services in PWA development allow for the rapid establishment of systems related to itinerary and ticket purchases for travels with hotels, airlines, and travel agencies. This facilitates offline access to travel which greatly increases satisfaction levels.  "
            }
          />
          <Card
            title={"Media & Entertainment "}
            description={
              "We create PWA solutions for video streaming services, online news portals, and online gaming that are easy to load, usable offline, and pulsating with user engagement through push notifications.  "
            }
          />
        </div>
        <div className="flex justify-center items-center">
          <Card
            title={"Logistics and Transportation"}
            description={
              "Companies in the logistics and transportation sector employ the use of PWAs to increase customer satisfaction and productivity with features that enhance engagement tracking, route optimization, shipment notifications, and offline access.  "
            }
          />
        </div>
      </div>
    </div>
  );
};

export default Section4;
