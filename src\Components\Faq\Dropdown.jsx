"use client";

import { useState } from "react";
import Image from "next/image";

const Dropdown = ({ title, children }) => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className="bg-white border border-gray-300 rounded-md mb-4 mt-6 shadow-md">
      {/* Dropdown Header */}
      <div
        onClick={toggleDropdown}
        className="flex justify-between items-center cursor-pointer p-4"
      >
        <h3 className="text-xl font-semibold text-[#232536]">{title}</h3>
        <div className="text-pink-500">
          {isOpen ? (
            <div className="rotate-180 transform transition-transform duration-300">
              <Image
                src="/Images/dropdown-icon.png"
                alt="arrow"
                width={32.8}
                height={32.8}
              />
            </div>
          ) : (
            // <span className="rotate-180 transform transition-transform duration-300">
            //   ▲
            // </span>
            // <span className="transition-transform duration-300">▼</span>
            <div className="transition-transform duration-300">
              <Image
                src="/Images/dropdown-icon.png"
                alt="arrow"
                width={32.8}
                height={32.8}
              />
            </div>
          )}
        </div>
      </div>

      {/* Dropdown Content */}
      <div
        className={`overflow-hidden transition-all duration-500 ease-in-out ${
          isOpen ? "max-h-[1000px] p-4" : "max-h-0 p-0"
        }`}
      >
        <div className="text-[#232536] text-lg">{children}</div>
      </div>
    </div>
  );
};

export default Dropdown;
