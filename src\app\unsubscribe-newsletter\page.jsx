"use client";
import React, { useState } from "react";
import { toast } from "react-hot-toast";

const UnsubscribeNewsletter = () => {
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch("https://api.valueans.com/api/newsletter/unsubscribe/", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      });

      if (response.ok) {
        toast.success("Successfully unsubscribed from newsletter");
        setEmail("");
      } else {
        toast.error("Failed to unsubscribe. Please try again.");
      }
    } catch (error) {
      toast.error("An error occurred. Please try again later.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <main className="min-h-screen">
      {/* Hero Section with Background Image */}
      <section
        className="pt-20 px-7 md:px-20 pb-20 bg-cover bg-center bg-no-repeat h-[50vh] w-full flex items-center justify-center"
        style={{
          backgroundImage: `url('/Images/HomePage-bg.png')`,
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      >
        <div className="text-center max-w-3xl">
          <h1 className="text-white text-4xl md:text-5xl font-trirong mb-6">
            Unsubscribe from Our Newsletter
          </h1>
          <p className="text-white text-lg md:text-xl">
            We're sorry to see you go! If you decide to unsubscribe, you'll miss out on the latest updates and exclusive offers from Valueans. Confirm below if you wish to stop receiving our newsletters.
          </p>
        </div>
      </section>

      {/* Email Input Section */}
      <section className="py-20 px-7 md:px-20">
        <div className="max-w-xl mx-auto">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                Email
              </label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email"
                required
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#F245A1] focus:border-transparent outline-none"
              />
            </div>
            <button
              type="submit"
              disabled={isLoading}
              className={`w-full bg-[#F245A1] text-white py-3 px-6 rounded-lg font-medium hover:opacity-90 transition-opacity ${
                isLoading ? "opacity-70 cursor-not-allowed" : ""
              }`}
            >
              {isLoading ? "Processing..." : "SUBMIT"}
            </button>
          </form>
        </div>
      </section>
    </main>
  );
};

export default UnsubscribeNewsletter; 