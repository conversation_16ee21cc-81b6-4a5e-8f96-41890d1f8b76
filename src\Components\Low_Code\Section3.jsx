import React from "react";

const Section3 = () => {
  return (
    <section className="bg-white mb-10 md:mb-24 p-4 md:p-8">
      <div className="md:my-[32px] md:mx-[75px] flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6">
        <div className="flex-1">
          <h2 className="text-xl md:text-[28px] md:leading-7 font-semibold mb-1">
            Work Smarter and Faster with Valueans Low Code Website Builder
          </h2>
        </div>
        <div className="flex-1">
          <section className="w-full  mx-auto p-4 border border-pink-500 rounded-2xl shadow-md">
            <h3 className="font-semibold text-base md:text-lg ">
              With low code mobile app development at Valueans, we: 
            </h3>
            <div className="py-5 flex flex-col gap-3  ">
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className="text-sm md:text-base">
                  Drag-and-drop widgets allow you to easily put together
                  responsive user interfaces.
                </p>
              </div>

              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className="text-sm md:text-base">
                  Use JS, HTML, and CSS to create your unique widgets.
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className="text-sm md:text-base">
                  Leverage JS to change look, data, and business logic.
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className="text-sm md:text-base">
                  Use and import third-party libraries into your applications.
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className="text-sm md:text-base">
                  Use Git to combine changes and manage versions. 
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className="text-sm md:text-base">
                  Deploy merged changes from specified environment branches   
                  automatically.
                </p>
              </div>
            </div>
          </section>
        </div>
      </div>
    </section>
  );
};

export default Section3;
