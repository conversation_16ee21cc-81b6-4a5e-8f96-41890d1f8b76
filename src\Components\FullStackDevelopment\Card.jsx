import React from "react";

const Card = ({ title, description, bgColor = "bg-white" }) => {
  return (
    <div
      className={`block max-w-sm p-6 ${bgColor} border border-gray-200 rounded-lg shadow-md`}
    >
      <h5 className="mb-2 text-2xl font-bold tracking-tight text-gray-900 dark:text-white">
        {title}
      </h5>
      <p
        className="font-normal text-gray-700 dark:text-gray-400"
        dangerouslySetInnerHTML={{ __html: description }}
      />
    </div>
  );
};

export default Card;
