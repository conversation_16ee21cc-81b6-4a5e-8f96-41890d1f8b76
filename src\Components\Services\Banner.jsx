import React from "react";
import Image from "next/image";

const Banner = ({
  title,
  titleColor,
  titleHighlight,
  highlightColor,
  description,
  descriptionHighlight,
  Imagesrc,
  ImageAlt,
}) => {
  return (
    <>
      <section className="relative">
        <div className="relative min-h-screen flex justify-end items-center">
          <div className="absolute top-0 w-[465px] h-[550px]">
            <Image
              src="/Images/purple_frame.png"
              alt="purple-block"
              width={465}
              height={550}
              className="object-fill"
            />
          </div>
        </div>

        <div className="flex justify-center items-center space-x-4 mt-8 gap-6 max-w-[90%] mx-auto z-10 absolute top-8 left-[188px]">
          <div className="w-[40%]">
            <h2 className="text-6xl leading-[75px] font-bold capitalize">
              <span className={`text-[${highlightColor}]`}>
                {titleHighlight} <br />
              </span>{" "}
              <span className={`text-[${titleColor}]`}>{title}</span>
            </h2>
          </div>
          <div className=" flex-shrink-0 w-[465px] h-[378px] z-10">
            <Image
              src={Imagesrc}
              alt={ImageAlt}
              width={465}
              height={378}
              layout="intrinsic"
              objectFit="cover"
              className="w-full h-full"
            />
          </div>
        </div>

        <div className="bg-[#350668] w-[60%] py-7 px-12  absolute bottom-[93px]">
          <div className="max-w-[85%] mx-auto">
            <p className={`text-3xl text-white`}>
              {description}{" "}
              <span className={`text-[${highlightColor}]`}>
                {descriptionHighlight}
              </span>
            </p>
          </div>
        </div>
      </section>
      <section className="max-w-[90%] mx-auto flex justify-center items-center mb-20"></section>
    </>
  );
};

export default Banner;
