"use client";
import React, { useState, useEffect } from "react";
import BlogCard from "../Blog/BlogCard";
import Button from "../Buttons/Button";
import Heading from "../Heading/Heading";


const Blog = ({ title, heading, description }) => {
  const [showAll, setShowAll] = useState(false); // State for "See More/Less"
  const [currentSlide, setCurrentSlide] = useState(0); // Track active slide for mobile
  const [blogs, setBlogs] = useState([]);

  // Function to clean HTML content and extract meaningful text
  const cleanHtmlContent = (html) => {
    if (!html) return "";

    // Remove CSS styles (everything between <style> tags)
    let cleaned = html.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, "");

    // Remove script tags
    cleaned = cleaned.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, "");

    // Remove HTML comments
    cleaned = cleaned.replace(/<!--[\s\S]*?-->/g, "");

    // Remove all HTML tags
    cleaned = cleaned.replace(/<[^>]*>/g, "");

    // Remove CSS rules that might be inline (like body { ... })
    cleaned = cleaned.replace(/[a-zA-Z-]+\s*\{[^}]*\}/g, "");

    // Remove extra whitespace and line breaks
    cleaned = cleaned.replace(/\s+/g, " ").trim();

    // Remove any remaining CSS-like syntax
    cleaned = cleaned.replace(/[a-zA-Z-]+:\s*[^;]+;/g, "");

    return cleaned;
  };

  // Function to truncate text for description
  const truncateText = (text, maxLength = 100) => {
    if (!text) return "Read about the reasons to start an online business";
    const plainText = cleanHtmlContent(text);
    if (plainText.length <= maxLength) return plainText;
    return plainText.substring(0, maxLength) + "...";
  };

  // Function to truncate title
  const truncateTitle = (title, maxLength = 60) => {
    if (!title) return "10 Inspiring Reasons to Start an Online Business in 2024";
    if (title.length <= maxLength) return title;
    return title.substring(0, maxLength) + "...";
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  // Fetch blogs from API
  useEffect(() => {
    const fetchBlogs = async () => {
      try {
        const response = await fetch("https://api.valueans.com/api/blogs/");

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        // Get blogs starting from the 7th blog (skip first 6 blogs)
        const blogsData = data.results ? data.results.slice(6) : [];
        setBlogs(blogsData);
      } catch (err) {
        console.error("Error fetching blogs:", err);
        // If API fails, we'll use default data (no need to set error state)
      }
    };

    fetchBlogs();
  }, []);

  // Default card data for fallback
  const defaultCardData = [
    {
      id: 1,
      title: "10 Inspiring Reasons to Start an Online Business in 2024",
      description: "Read about the reasons to start an online business",
      date: "27 Jan 2021",
      imageUrl: "/Images/laptops-use.png",
      slug: "default-blog-1"
    },
    {
      id: 2,
      title: "10 Inspiring Reasons to Start an Online Business in 2024",
      description: "Read about the reasons to start an online business",
      date: "5 Feb 2024",
      imageUrl: "/Images/laptops-use.png",
      slug: "default-blog-2"
    },
    {
      id: 3,
      title: "10 Inspiring Reasons to Start an Online Business in 2024",
      description: "Read about the reasons to start an online business",
      date: "12 Mar 2024",
      imageUrl: "/Images/laptops-use.png",
      slug: "default-blog-3"
    },
    {
      id: 4,
      title: "10 Inspiring Reasons to Start an Online Business in 2024",
      description: "Read about the reasons to start an online business",
      date: "12 Mar 2024",
      imageUrl: "/Images/laptops-use.png",
      slug: "default-blog-4"
    },
  ];

  // Transform API data to match the expected format, or use default data
  const cardData = blogs.length > 0 ? blogs.map(blog => ({
    id: blog.id,
    title: truncateTitle(blog.title),
    description: truncateText(blog.content, 100),
    date: formatDate(blog.created_at),
    imageUrl: blog.image || "/Images/laptops-use.png",
    slug: blog.slug_field
  })) : defaultCardData;

  // Handle carousel scroll
  const handleScroll = (e) => {
    const slideIndex = Math.round(e.target.scrollLeft / e.target.clientWidth);
    setCurrentSlide(slideIndex);
  };

  // Cards to display based on state
  const visibleCards = showAll ? cardData : cardData.slice(0, 3);



  return (
    <div className="container my-24">
      {/* Heading Section */}
      <div className="text-center">
        <h2 className="text-[#F245A1] text-lg md:text-2xl font-medium mb-2">
          {heading}
        </h2>
        <Heading>{title}</Heading>
        <h2 className="text-xl md:text-2xl text-[#232536] font-semibold leading-9 md:leading-[57px]"></h2>
        <p className="text-xl text-[#232222]">{description}</p>
      </div>

      {/* Carousel for Mobile Screens */}
      <div
        className="flex gap-5 overflow-x-scroll md:hidden snap-x snap-mandatory scrollbar-hide"
        onScroll={handleScroll}
      >
        {cardData.map((card, index) => (
          <div
            key={card.id || index}
            className={`flex-shrink-0 w-[85%] px-4 mx-auto ${
              index === 0 ? "ml-[7.5%]" : "" // Adjust margin for the first card
            }`}
          >
            <BlogCard
              title={card.title}
              description={card.description}
              date={card.date}
              imageUrl={card.imageUrl}
              slug={card.slug}
            />
          </div>
        ))}
      </div>

      {/* Carousel Dots for Mobile */}
      <div className="flex justify-center mt-4 md:hidden">
        {cardData.map((_, index) => (
          <div
            key={index}
            className={`w-3 h-3 mx-1 rounded-full ${
              currentSlide === index ? "bg-[#7716BC]" : "bg-gray-300"
            }`}
          ></div>
        ))}
      </div>

      {/* Grid for Larger Screens */}
      <div className="hidden md:flex justify-evenly gap-4 items-center my-10 flex-wrap">
        {visibleCards.map((card, index) => (
          <BlogCard
            key={card.id || index}
            title={card.title}
            description={card.description}
            date={card.date}
            imageUrl={card.imageUrl}
            slug={card.slug}
          />
        ))}
      </div>

      {/* "See More/Less" Button */}
      {cardData.length > 3 && (
        <div className="w-fit mx-auto hidden md:block">
          <Button
            paddingX="px-6"
            paddingY="py-2"
            bgColor="bg-[#F245A1]"
            hoverColor="opacity-70"
            onClick={() => setShowAll(!showAll)}
          >
            {showAll ? "Show Less" : "See All"}
          </Button>
        </div>
      )}
    </div>
  );
};

export default Blog;
