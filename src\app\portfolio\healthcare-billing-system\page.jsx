import React from "react";
import Section1 from "@/Components/Portfolio/WebDev/PaintReady/Section1";
import Section2 from "@/Components/Portfolio/WebDev/PaintReady/Section2";
import Section4 from "@/Components/Portfolio/WebDev/PaintReady/Section4";
import Section5 from "@/Components/Portfolio/WebDev/PaintReady/Section5";
export const metadata = {
  title: "Appointment & Insurance Management | Valueans",
  description: "A Healthcare Billing System. Enabling online doctor appointments, accurate billing, and insurance processing. The system streamlines patient workflows and improves operational efficiency.",
};
const page = () => {
  const images = [
  '/Images/HBS3.png',
  '/Images/HBS4.png',
  '/Images/HBS5.png',
];
  return (
    <div>
      <Section1 backgroundImage={"/Images/HBS-bg.png"} />
      <Section2
        image={"/Images/HBS2.png"}
        heading={"Healthcare Billing System"}
        paragraph1={
          "Valueans has provided digital solutions to several businesses helping them to streamline their operations and processes that result in their growth. We’ve dealt with many industries and healthcare is not an exception. "
        }
        paragraph2={
          "A healthcare entity in Saudi Arabia reached out to us to provide them with a Healthcare Billing System that improves the process of scheduling an appointment with doctors, gives correct billing statements, and processes their insurance data. With the Healthcare Billing System, patients can easily book an appointment online without the hassle of waiting in the hospital for their turn. Moreover, our system helped them save their insurance information so they could easily pay the bills without going through a manual process that demanded so much effort and time. "
        }
      />
     
      <Section4
        paragraph1={
          "To make this system efficient, we leveraged the latest technology stack which includes React for the frontend development and Django for the backend development. It has helped us leverage a system that carries out different processes without any delay or interruptions that result in frustration and long queues of complaints. "
        }
      />
      <Section5 images={images} />
    </div>
  );
};

export default page;
