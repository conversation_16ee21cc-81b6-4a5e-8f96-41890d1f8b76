import React from "react";
import Card from "../Cloud Services/Card";

const Section5 = () => {
  const cardData = [
    {
      title: "High-End Consulting",
      description:
        "At Valueans, you get high-quality business-critical application management services, robust application lifecycle management, and corporate app strategies developed by our professionals through a carefully cultivated consultative approach.",
    },
    {
      title: "Structured Process",
      description:
        "You will get value and sustainability from our highly structured established methods of app management for your company if you put a clear emphasis on integrating agile approaches into the development and management that follows.",
    },
    {
      title: "Skilled Developers",
      description:
        "We use our best application management to deploy <a href='/Dedicated_Deployment_teams' class='text-[#7716BC] hover:underline'>dedicated development teams</a>  of specialist developers for a high-value bespoke product or to mobilize similarly competent full-stack developers for a quick turnaround time.",
    },
    {
      title: "Good Communication",
      description:
        "Our developers efficiently code and develop software indefinitely. But at the same time, we dedicate special time to hearing your project specifications and understanding your company’s goals. As a result, we respond incredibly quickly.",
    },
    {
      title: "Domain Expertise",
      description:
        "Get the most up-to-date technology for empowered application lifecycle management, which will provide apps and application management developed by skilled subject matter experts that are incredibly competent, intuitive, engaging, and efficient.",
    },
    {
      title: "Multi-Tier Support",
      description:
        "We provide support services for all-inclusive application administration assistance, starting with the fundamentals of app management such as server configuration, performance monitoring, and troubleshooting.",
    },
    {
      title: "Application Modernization",
      description:
        "By updating your legacy systems, you may reduce inefficient IT expenditures from excessive maintenance to controlled expenses while keeping corporate business models, investments, and business goals through adaptive application modernization.",
    },
    {
      title: "Maintenance Of Applications",
      description:
        "Use <a href='/Maintenance_and_Support' class='text-[#7716BC] hover:underline'>customized application maintenance</a>  to stabilize, optimize, grow, and enhance application functionality at a high and consistent level to reduce risk, cut expenses, and increase return on investment.",
    },
  ];

  return (
    <section className="bg-pink-100 my-10 md:my-24 p-4">
      <div className="w-[85%] mx-auto">
        <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold my-1 md:my-3">
          What You Get with <span className="text-[#7716BC]">Valueans</span>{" "}
          Enterprise Application Integration Services 
        </h2>
        <div className="flex flex-col items-center">
          {/* First two rows (3 cards each) */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full max-w-5xl">
            {cardData.slice(0, 6).map((card, index) => (
              <Card
                key={index}
                title={card.title}
                description={card.description}
              />
            ))}
          </div>

          {/* Last row (2 cards, centered) */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 w-full max-w-4xl mt-6 justify-center">
            {cardData.slice(6).map((card, index) => (
              <Card
                key={index}
                title={card.title}
                description={card.description}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Section5;
