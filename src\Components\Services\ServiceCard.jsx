import Image from "next/image";
import React from "react";

const ServiceCard = ({ imageSrc, altText, title, description }) => {
  return (
    <div className="max-w-sm p-6 bg-white border border-gray-200 rounded-lg shadow ">
      <Image
        src={imageSrc}
        alt={altText}
        width={42}
        height={42}
        className="mx-auto"
      />

      <h5 className="my-2 text-2xl font-semibold tracking-tight text-[#232536] text-center">
        {title}
      </h5>

      <p className="mb-3 font-normal text-[#232222] text-center">
        {description}
      </p>
    </div>
  );
};

export default ServiceCard;
