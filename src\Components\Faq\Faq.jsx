import React from "react";
import Accordion from "./Accordion";
import Heading from "../Heading/Heading";

const Faq = ({bgColor, content}) => {
  

  return (
    <div className={`max-w-[85%] mx-auto my-12 py-4 md:mt-24 ${bgColor ? bgColor : "bg-[#F4F5F6]"} rounded-lg  shadow-lg`}>
      <Heading>Frequently Asked Questions</Heading>
      <div className="p-4">
        {content?.map((item, index) => (
          <Accordion key={index} title={item.title} content={item.content} />
        ))}
      </div>
    </div>
  );
};

export default Faq; 


