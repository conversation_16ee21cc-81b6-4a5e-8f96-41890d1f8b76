import React from "react";
import ServiceCard_2 from "@/Components/Card/ServiceCard_2";

const Section8 = ({ cardData, heading, spanHeading, bluespanHeading, cardHeight }) => {
  return (
    <div className="w-[90%] mx-auto mb-10 md:mb-24 my-12">
      <div className="mb-4">
        <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold capitalize">
          <span className="text-[#F245A1]">{spanHeading}</span> {heading}{" "}
          <span className="text-[#7716BC]">{bluespanHeading}</span>
        </h2>
      </div>
      <div className="grid grid-cols-1 md:w-[70%] md:grid-cols-2 gap-6 mx-auto">
        {cardData.map((card, index) => (
          <div key={index} className=" flex justify-center items-center">
            <ServiceCard_2 title={card.title} content={card.content} cardHeight={cardHeight}/>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Section8;
