import Image from "next/image";
import Button from "../Buttons/Button";

const Section3 = () => {
  return (
    <div className="mb-10 md:mb-24 md:py-8 md:px-[75px]">
      <div className="flex flex-col md:flex-row justify-center md:justify-between items-center">
        <div className="">
          <h3 className="text-[#7716BC] text-2xl md:text-[28px] font-semibold mb-4 md:mb-8">
            NLP Technology Platform Solutions
          </h3>
          <Image src={"/Images/tech.png"} alt="AI" width={400} height={400} />
        </div>
        <div className="w-full md:max-w-2xl">
          <section className="w-full  mx-auto p-4 border border-pink-500 rounded-2xl shadow-md mb-4 md:mb-8">
            <div className="py-5 flex flex-col gap-3  ">
              <h3 className="font-semibold text-base md:text-lg">
                With NLP Software Development Services at Valueans, we:
              </h3>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>

                <p className="text-sm md:text-base">
                  Use NLP technology to create smarter chatbots and virtual
                  assistants that offer real-time, conversational responses for
                  you.
                </p>
              </div>

              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className="text-sm md:text-base">
                  Offer data-driven insights to improve decision making derived
                  from the hidden insights that we get from tons of unstructured
                  text data.
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className="text-sm md:text-base">
                  Handle the heavy workload manually when you can automate text
                  classification, extract, and summarize tasks with Valueans.
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className="text-sm md:text-base">
                  Have a track record of delivering successful NLP solutions
                  across diverse industries.
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className="text-sm md:text-base">
                  Deliver real-time responses and real-time insights. We help
                  you decode customer feedback at the moment, making decisions
                  faster.
                </p>
              </div>
            </div>
          </section>
          <Button bgColor="bg-[#7716BC]" hoverColor="hover:bg-purple-600">
            Get an Estimate
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Section3;
