import React from "react";
import Image from "next/image";

const Section1 = () => {
  return (
    <section className="relative h-[60vh] md:h-screen">
      <div className="relative h-fit flex justify-center md:justify-end items-center mb-0 md:mb-10">
        <div className="absolute top-0 w-[430px] h-[375px] md:w-[465px] md:h-[550px]">
          <div className="w-full h-full">
            <Image
              src="/Images/purple_frame.png"
              alt="purple-block"
              layout="fill" // Fills the parent container
              className="object-cover" // Adjust the image to cover the container dimensions
            />
          </div>
        </div>
      </div>

      <div className="flex flex-col-reverse md:flex-row justify-center items-center space-x-4 mt-0 md:mt-8 gap-3 md:gap-6 max-w-[90%] mx-auto z-10 absolute top-8 left-3 md:left-[188px]">
        <div className="block w-[90vw] md:w-[40%] my-10">
          <h2 className="text-white md:text-[#000000] text-[28px] md:text-6xl  leding-[42px] md:leading-[75px] text-center md:text-left font-bold capitalize">
            Turn Your Data into Smarter Decisions with{" "}
            <span className="text-[#F245A1]">Business Intelligence</span>{" "}
          </h2>
        </div>
        <div className="relative z-10">
          <div className="w-[239.5px] h-[200px] md:w-[465px] md:h-[378px]">
            <Image
              src="/Images/business_frame.png"
              alt="App Integration"
              layout="fill"
              objectFit="cover"
              className="rounded-md "
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default Section1;
