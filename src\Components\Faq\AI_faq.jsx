import React from "react";
import Accordion from "./Accordion";

const AI_Faq = () => {
  const accordionData = [
    {
      title: "What types of businesses benefit most from AI solutions?",
      content: "This is the content for item 1.",
    },
    {
      title: "What is the cost of AI development services?",
      content: "This is the content for item 2.",
    },
    {
      title: "How long does it take to develop an AI solution?",
      content: "This is the content for item 3.",
    },
    {
      title: "How can AI benefit my business?",
      content: "This is the content for item 3.",
    },
    {
      title:
        "Can Valueans help with integrating AI solutions into existing systems?",
      content: "This is the content for item 3.",
    },
    {
      title: "How does Valueans ensure the quality of its AI solutions?",
      content: "This is the content for item 3.",
    },
  ];

  return (
    <div className="max-w-[75%] mx-auto mt-24 pb-24">
      <h2 className="text-[38px] text-center font-medium text-[#232536] mb-[42px] ">
        Frequently Asked Questions
      </h2>
      <div className="p-6">
        {accordionData.map((item, index) => (
          <Accordion key={index} title={item.title} content={item.content} />
        ))}
      </div>
    </div>
  );
};

export default AI_Faq;
