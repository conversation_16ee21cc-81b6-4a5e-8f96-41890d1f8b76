import { HelpCircleIcon } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { FiArrowRight } from "react-icons/fi";
import ProjectsRow from "./ProjectsRow";

const Portfolio_Featured = () => {
  return (
    <section className="">
      <ProjectsRow
        image={"/Images/Semantic_fitness.png"}
        Heading={"Somatic Fitness App"}
        paragrph={
          "At Valuenas, we love to deliver user-friendly mobile solutions that can make a difference. We recently worked on Somatics Fitness & Nutrition (SFN) software. It’s a smartphone application to help..."
        }
        link={"/Portfolio/mobile-app/SemanticFitness"}
      />
     
    </section>
  );
};

export default Portfolio_Featured;
