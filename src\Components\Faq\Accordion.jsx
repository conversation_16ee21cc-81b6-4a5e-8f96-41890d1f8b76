"use client";
import React, { useState } from "react";

const Accordion = ({ title, content }) => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleAccordion = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className="bg-white border border-gray-300 rounded-md mb-4 mt-6 shadow-md">
      <button
        className="flex justify-between items-center w-full px-2 md:px-4 py-2 md:py-3 text-base md:text-lg font-medium text-left text-gray-700 focus:outline-none"
        onClick={toggleAccordion}
      >
        <span className="text-base md:text-lg font-semibold text-[#232536]">
          {title}
        </span>
        <div
          className={`w-5 md:w-10 h-5 md:h-10 flex items-center flex-shrink-0 justify-center rounded-full transition-transform duration-300 ${
            isOpen ? "bg-pink-500 rotate-180" : "bg-pink-500 rotate-0"
          }`}
        >
          <svg
            className="w-3 md:w-5 h-3 md:h-5 text-white"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M19 9l-7 7-7-7"
            ></path>
          </svg>
        </div>
      </button>
      {isOpen && (
        <div className="p-4 text-[#232536] text-sm text-justify">
          <p >{content}</p>
        </div>
      )}
    </div>
  );
};

export default Accordion;
