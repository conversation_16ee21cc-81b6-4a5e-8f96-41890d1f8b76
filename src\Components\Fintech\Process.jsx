import React from "react";
import ServiceCount from "../Services/ServiceCount";
import ServiceLifecycleCard from "../Services/ServiceLifecycleCard";
import Connector7 from "../Connectors/Connector7";
import Connector8 from "../Connectors/Connector8";
import Connector10 from "../Connectors/Connector10";
import Connector11 from "../Connectors/Connector11";

const Process = () => {
  const discoveryAndPlanningItems = [
    "In-depth consultation and planning",
    "Marketing research to identify trends, strategies, and opportunities",
    "Technical and functional requirements analysis",
    "Project Roadmap",
  ];

  const conceptualizationAndDesignItems = [
    "Wireframing and prototyping",
    "Ask for your feedback",
    "Design UX",
  ];

  const developmentItems = [
    "Developing and designing frontend",
    "Building the backend",
    "API integration for payments, data analytics, and authentication",
    "Incorporate Blockchain and AI integration",
    "Ask for your approval/feedback",
  ];

  const qualityAssuranceAndTestingItems = [
    "Conduct comprehensive testing, including unit testing, integration testing, system testing, and user acceptance testing (UAT)",
    "Perform rigorous security testing",
  ];

  const deploymentItems = [
    "Deploy the solution in a staging environment and perform final checks",
    "Ask for your feedback",
    "Product deployment after approval",
  ];

  const postLaunchSupportAndMaintenanceItems = [
    "Continuous performance monitoring",
    "Regular updates and enhancements",
    "Comprehensive support and maintenance services, including troubleshooting, performance optimization, and feature enhancements",
  ];

  return (
    <div className="w-[90vw] mx-auto mb-24">
      <h2 className="text-center text-4xl mb-10">
        Our Step-by-Step{" "}
        <span className="text-[#F245A1]">
          FinTech <br /> Development Process
        </span>{" "}
      </h2>
      <div className="relative">
        <div className="flex justify-center items-center gap-8 mb-20 ">
          <div className="flex-col justify-center items-center">
            <ServiceLifecycleCard
              title="Discovery and Planings"
              items={discoveryAndPlanningItems}
            />
            <ServiceCount count={"1"} />
          </div>
          <div className="flex-col justify-center items-center">
            <ServiceLifecycleCard
              title="Development"
              items={developmentItems}
            />
            <ServiceCount count={"2"} className="z-10" />
          </div>
          <div className="flex-col justify-center items-center">
            <ServiceLifecycleCard title="Deployment" items={deploymentItems} />
            <ServiceCount count={"3"} />
          </div>
        </div>
        <div className="absolute top-[51%] left-[22%]">
          <Connector7 />
        </div>
        <div className="absolute top-[54%] left-[37%]">
          <Connector8 />
        </div>
        <div className="absolute top-[51%] left-[55%]">
          <Connector10 />
        </div>
        <div className="absolute top-[51%] left-[69%]">
          <Connector11 />
        </div>
        <div className="absolute top-[47%] left-[84%]">
          <div
            className="w-[60px] h-[120px] border-dashed border-t-2 border-r-2 border-gray-700 absolute"
            style={{
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
            }}
          />
        </div>
        <div className=" ml-[15%] flex justify-center items-center gap-8 mt-20 relative">
          <div className="flex-col justify-center items-center absolute top-[5%] -left-[4%]">
            <ServiceCount count={"4"} />
            <ServiceLifecycleCard
              title="Conceptualization and Design"
              items={conceptualizationAndDesignItems}
            />
          </div>
          <div className="flex-col justify-center items-center ml-[35%]">
            <ServiceCount count={"5"} />
            <ServiceLifecycleCard
              title="Quality Assurance and Testing"
              items={qualityAssuranceAndTestingItems}
            />
          </div>
          <div className="flex-col justify-center items-center ">
            <ServiceCount count={"6"} />
            <ServiceLifecycleCard
              title="Post-Launch support and maintenance"
              items={postLaunchSupportAndMaintenanceItems}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Process;
