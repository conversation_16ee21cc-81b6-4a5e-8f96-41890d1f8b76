import React from "react";

const Section1 = ({ backgroundImage, heading,className }) => {
  return (
    <section
      className={`relative ${className ? className : "h-[40vh] md:min-h-screen"}  `}
      style={{
        backgroundImage: `url(${backgroundImage})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
      }}
    >
      {/* Overlay div */}
      <div className="absolute inset-0 bg-[rgba(119,22,188,0.6)] z-0"></div>

      {/* Content */}
      <div className="flex flex-col justify-center items-center gap-6 h-full relative z-10 ">
        <h2 className="font-titillium text-3xl md:mb-10 font-bold md:text-5xl text-white text-center">
          {heading}
        </h2>
      </div>
    </section>
  );
};

export default Section1;
