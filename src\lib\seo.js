// SEO Configuration and Utilities for Kapoor Software Solutions

export const siteConfig = {
  name: "Kapoor Software Solutions",
  description: "Kapoor Software Solutions provides custom software development, mobile app development, web development, AI/ML solutions, and digital transformation services.",
  url: "https://kapoorsoftwaresolutions.com",
  ogImage: "https://kapoorsoftwaresolutions.com/Images/logo/logo1.svg",
  links: {
    twitter: "https://twitter.com/kapoorsoftware",
    linkedin: "https://www.linkedin.com/company/kapoor-software-solutions",
  },
};

export const defaultMetadata = {
  metadataBase: new URL(siteConfig.url),
  title: {
    default: siteConfig.name,
    template: `%s | ${siteConfig.name}`,
  },
  description: siteConfig.description,
  keywords: [
    "Custom Software Development",
    "Mobile App Development",
    "Web Development",
    "AI/ML Solutions",
    "Digital Transformation",
    "Software Solutions",
    "Technology Services",
    "Kapoor Software Solutions",
  ],
  authors: [
    {
      name: "Kapoor Software Solutions",
      url: siteConfig.url,
    },
  ],
  creator: "Kapoor Software Solutions",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: siteConfig.url,
    title: siteConfig.name,
    description: siteConfig.description,
    siteName: siteConfig.name,
    images: [
      {
        url: siteConfig.ogImage,
        width: 1200,
        height: 630,
        alt: siteConfig.name,
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: siteConfig.name,
    description: siteConfig.description,
    images: [siteConfig.ogImage],
    creator: "@kapoorsoftware",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
    yandex: "your-yandex-verification-code",
    yahoo: "your-yahoo-verification-code",
  },
};

// Generate metadata for service pages
export function generateServiceMetadata({
  title,
  description,
  path,
  keywords = [],
}) {
  return {
    title,
    description,
    keywords: [...defaultMetadata.keywords, ...keywords],
    alternates: {
      canonical: `${siteConfig.url}${path}`,
    },
    openGraph: {
      title,
      description,
      url: `${siteConfig.url}${path}`,
      type: "website",
      siteName: siteConfig.name,
      images: [
        {
          url: siteConfig.ogImage,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: [siteConfig.ogImage],
    },
  };
}

// Generate metadata for portfolio pages
export function generatePortfolioMetadata({
  title,
  description,
  slug,
  image,
}) {
  return {
    title,
    description,
    alternates: {
      canonical: `${siteConfig.url}/portfolio/${slug}`,
    },
    openGraph: {
      title,
      description,
      url: `${siteConfig.url}/portfolio/${slug}`,
      type: "article",
      siteName: siteConfig.name,
      images: [
        {
          url: image || siteConfig.ogImage,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: [image || siteConfig.ogImage],
    },
  };
}

// Generate metadata for blog pages
export function generateBlogMetadata({
  title,
  description,
  slug,
  image,
  publishedTime,
  modifiedTime,
  authors = [],
}) {
  return {
    title,
    description,
    alternates: {
      canonical: `${siteConfig.url}/blog/${slug}`,
    },
    openGraph: {
      title,
      description,
      url: `${siteConfig.url}/blog/${slug}`,
      type: "article",
      siteName: siteConfig.name,
      publishedTime,
      modifiedTime,
      authors: authors.length > 0 ? authors : [siteConfig.name],
      images: [
        {
          url: image || siteConfig.ogImage,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: [image || siteConfig.ogImage],
    },
  };
}
