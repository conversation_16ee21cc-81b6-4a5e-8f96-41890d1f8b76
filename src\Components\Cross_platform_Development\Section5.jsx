import React from "react";
import Image from "next/image";

const InfoCard = ({ title, description }) => {
  return (
    <div className="w-full md:max-w-md flex items-start gap-1 border border-pink-500 p-2  rounded-lg shadow-sm">
      <div className="w-[20px] h-[20px] shrink-0">
        <Image
          src="/Images/service_frame.png"
          alt="arrow"
          width={50}
          height={50}
          className="object-contain"
        />
      </div>
      <div>
        <h3
        className="font-semibold  text-base"
        dangerouslySetInnerHTML={{ __html: title }}
      />
        
        <p
        className="text-sm text-justify"
        dangerouslySetInnerHTML={{ __html: description }}
      />
      </div>
    </div>
  );
};

const Section5 = ({ cardData, heading, paragraph, spanLeft, spanRight }) => {
  return (
    <div className="max-w-[90%] mx-auto text-justify my-10 md:my-20">
      <div className="mb-[42px]">
        <h2 className="text-xl md:text-3xl text-center font-semibold ">
          <span className="text-[#7716BC]">{spanLeft}</span> {heading}{" "}
          <span className="text-[#7716BC]">{spanRight} </span>
        </h2>
        <p className="text-xl md:w-[70%] mx-auto text-center">{paragraph}</p>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Change to md:grid-cols-3 */}
        {cardData.map((card, index) => (
          <InfoCard
            key={index}
            title={card.title}
            description={card.description}
          />
        ))}
      </div>
    </div>
  );
};

export default Section5;
