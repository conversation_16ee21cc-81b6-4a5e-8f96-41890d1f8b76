import React from "react";
import ServiceCount from "../Services/ServiceCount_2";
import ServiceLifecycleCard from "../Services/ServiceLifecycleCard_2";

const Process = () => {
  const RequirementsAnalysis = [
    "With our data engineering consulting services, we start by ascertaining the specific requirements and expectations of consumers for a new or altered product. It serves as a blueprint for all ensuing data-related procedures. ",
  ];

  const dataArchitectureDesign = [
    "We provide a structure that illustrates the information's origins as well as its storage, security, and transportation methods. At Valueans, the data strategy is managed by the data architecture. ",
  ];

  const DataIngestion = [
    "We either import the data for instant usage or move it to a storage medium. The data must first be cleansed before it can enter the pipeline. We fix or eliminate any inaccurate or superfluous information from the records. ",
  ];

  const datalakebuilding = [
    "We create data lakes to cheaply store unstructured, structured, and raw data files in an individual location. It is possible to construct data lakes using Hadoop, GCS, or Azure. This covers intricate tasks like using Python for data engineering. ",
  ];

  const pipeline = [
    "The ETL engineer begins data processing after preparing the stored data. Because it transforms raw data into pertinent information, it is the most crucial step in the data pipeline. ",
  ];

  const datamodeling = [
    "We investigate and display the data-oriented structures at this stage. The objective is to depict the categories of data and how they might be organized, as well as the relationships that exist within the data. ",
  ];

  const qualityAssurance = [
    "The data must be validated and verified for quality before being sent further. For validating and verifying every component of the data architecture, our experts develop test cases. ",
    "Providing detailed documentation and guides for system usage and troubleshooting.",
    "Offering ongoing support to address any questions or issues that arise post-implementation.",
  ];

  const automationDeployment = [
    "Throughout the entire procedure, this is one of the most crucial tasks. The DevOps technique that automates the data pipeline is developed by our team. The time, money, and effort spent on pipeline management are greatly reduced by this method.",
  ];

  return (
    <div className="bg-pink-100 my-10 md:my-24 py-10">
      <div className="w-[85%] mx-auto">
        <h2 className="text-2xl md:text-[38px] md:leading-[57px] text-center font-semibold">
          Our Engineering
          <span className="text-[#7716BC]">Process</span>
        </h2>
        <p className="w-full md:w-[75%] mx-auto text-base md:text-xl text-center">
          We treat each client as an individual! We work together to define data
          engineering tools, advanced technologies, infrastructure, and
          solutions that meet your architecture and address certain business
          concerns.    
        </p>
        <div>
          <div className="flex flex-col md:flex-row justify-center items-center gap-6">
            <div className="flex justify-center items-center gap-2 my-5">
              <ServiceCount>1</ServiceCount>
              <ServiceLifecycleCard
                title="Requirements Analysis"
                items={RequirementsAnalysis}
              />
            </div>

            <div className="flex justify-center items-center gap-2 my-5">
              <ServiceCount>2</ServiceCount>
              <ServiceLifecycleCard
                title="Data Architecture Design"
                items={dataArchitectureDesign}
              />
            </div>
            <div className="flex justify-center items-center gap-2 my-5">
              <ServiceCount>3</ServiceCount>
              <ServiceLifecycleCard
                title="Data Ingestion & Cleaning"
                items={DataIngestion}
              />
            </div>
          </div>
          <div className="flex flex-col md:flex-row justify-center items-center gap-6 my-6">
            <div className="flex justify-center items-center gap-2 my-5">
              <ServiceCount>4</ServiceCount>
              <ServiceLifecycleCard
                title="Data Lake Building"
                items={datalakebuilding}
              />
            </div>

            <div className="flex justify-center items-center gap-2 my-5">
              <ServiceCount>5</ServiceCount>
              <ServiceLifecycleCard
                title="Implementation of ETL/ELT Pipelines"
                items={pipeline}
              />
            </div>
            <div className="flex justify-center items-center gap-2 my-5">
              <ServiceCount>6</ServiceCount>
              <ServiceLifecycleCard
                title="Data Modeling"
                items={datamodeling}
              />
            </div>
          </div>

          <div className="flex flex-col md:flex-row justify-center items-center gap-6">
            <div className="flex justify-center items-center gap-2 my-5">
              <ServiceCount>7</ServiceCount>
              <ServiceLifecycleCard
                title="Quality Assurance"
                items={qualityAssurance}
              />
            </div>
            <div className="flex justify-center items-center gap-2 my-5">
              <ServiceCount>8</ServiceCount>
              <ServiceLifecycleCard
                title="Automation and Deployment"
                items={automationDeployment}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Process;
