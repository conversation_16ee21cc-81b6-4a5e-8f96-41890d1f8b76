"use client";
import React from "react";
import Link from "next/link";

const Button = ({
  children,
  onClick,
  to,
  bgColor = "bg-[#7716BC]",
  hoverColor = "hover:bg-[#5E0D9F]",
  paddingX = "px-2",
  paddingY = "py-1",
  disabled = false,
}) => {
  const classNames = `text-white text-base md:text-lg font-semibold ${paddingY} ${paddingX} rounded-md text-center transition duration-300 whitespace-nowrap ${bgColor} ${hoverColor} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`;

  return to ? (
    <Link href={to} className={classNames}>
      {children}
    </Link>
  ) : (
    <button onClick={onClick} className={classNames} disabled={disabled}>
      {children}
    </button>
  );
};

export default Button;
