"use client";

import React, { useState, useEffect  } from "react";
import Button from "../Buttons/Button";
import MultiStepForm from "./MultiStepForm";

const Section2 = ({
  paragraph1,
  paragraph2,
  paragraph3,
  heading,
  paragraph4,
  paragraph5,
}) => {
  const [showMultiStepForm, setShowMultiStepForm] = useState(false);

  const scrollToExpertForm = () => {
    // Find the form section and scroll to it
    const formSection = document.getElementById("expert-form-section");
    if (formSection) {
      formSection.scrollIntoView({ behavior: "smooth" });
    }
  };

 const toggleMultiStepForm = () => {
    setShowMultiStepForm((prev) => !prev);
  };

  useEffect(() => {
    if (showMultiStepForm) {
      // Wait for the form to be in the DOM, then scroll
      const multiStepEl = document.getElementById("multi-step-form-section");
      if (multiStepEl) {
        multiStepEl.scrollIntoView({ behavior: "smooth" });
      }
    }
  }, [showMultiStepForm]);

  return (
    <div className="w-[90%] mx-auto mb-10 md:my-24 mt-10">
      <div className="flex flex-col md:flex-row">
        <div className=" space-y-6 text-justify">
          <div className="">
            <p className="text-base md:text-lg">{paragraph1}</p>
            <p className="text-base md:text-lg">{paragraph2}</p>
            <p className="text-base md:text-lg">{paragraph3}</p>
          </div>
          <div className="flex flex-col md:flex-row justify-around gap-4 mb-5 ">
            <Button
              bgColor="bg-[#F245A1]"
              paddingX="px-4"
              paddingY="py-3"
              onClick={scrollToExpertForm}
            >
              Estimate With an expert
            </Button>
            <Button
              bgColor="bg-[#F245A1]"
              paddingX="px-4"
              paddingY="py-3"
              onClick={toggleMultiStepForm}
            >
              Use our Cost Estimator
            </Button>
          </div>
          {/* Multi-step form section */}
          <MultiStepForm isVisible={showMultiStepForm} />
          <div className="">
            <h1 className="text-xl md:text-3xl font-semibold">{heading}</h1>
            <p className="text-base md:text-lg"> {paragraph4}</p>
            <p className="text-base md:text-lg">{paragraph5}</p>
          </div>
        </div>
        <div className="md:w-[30%]"></div>
      </div>
    </div>
  );
};

export default Section2;
