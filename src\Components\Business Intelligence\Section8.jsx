import React from "react";
import <PERSON>radient_card from "./Gradient_Card";
import Card from "./Card_3";

const Section7 = () => {
  return (
    <div className="w-[85%] mx-auto mb-10 md:mb-24">
      <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
        Why is Valueans a Top Choice for{" "}
        <span className="text-[#F245A1]">
          Business Intelligence Solutions?
        </span>
      </h2>
      <p className="text-base md:text-xl text-center">
        We treat each client as an individual! Through business intelligence service, we work together to define <a href='/DataEngineering' class='text-[#7716BC] hover:underline'>data engineering</a> tools, advanced technologies, infrastructure, and solutions that meet your architecture and address certain business concerns. Here’s how we help businesses:
      </p>
      <div className="flex flex-col justify-center items-center gap-3 md:gap-10 my-3 md:my-10">
        <div className="flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6">
          <Gradient_card 
            title={"Smarter, Data-Driven Decisions "}
            description={
              "With Instant Insights from BI solutions, you can make faster, smarter decisions. Relying on real data, it helps you reduce risks and confidently act on opportunities. This approach also allows you to stay ahead of the competition by spotting trends early. In addition, it helps you optimize resources, ensuring better outcomes for your business."
            }
            height={"h-auto md:h-[330px]"}
          />
          <Gradient_card
            title={"Streamlined Operations and Higher Efficiency "}
            description={
              "Our BI tools are specially designed to manage the growing complexity and volume of data. By optimizing workflows and resources, we help businesses boost productivity and minimize waste. This is done by understanding and resolving resource heavy systems so that there are no bottlenecks or inefficiencies."
            }
            height={"h-auto md:h-[330px]"}
          />
          <Gradient_card 
            title={"Better Customer Understanding and Personalization"}
            description={
              "In the constantly changing BI solutions ecosystem, take advantage of cutting-edge technology, industry best practices, and the newest tools available to deliver best-in-class customer experiences and a final product that satisfies all specifications, requirements, and business objectives."
            }
            height={"h-auto md:h-[330px]"}
          />
        </div>
        <div className="flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6">
          <Gradient_card
            title={"Revenue Growth and New Opportunities"}
            description={
              "Our BI solutions help you find new opportunities, fine-tune pricing, and improve financial planning, helping you boost profits and achieve long-term growth."
            }
            height={"h-auto md:h-[250px]"}
          />
          <Gradient_card
            title={"Competitive Edge with initiative-taking insights "}
            description={
              "With our advanced forecasting tools, you can anticipate market trends and customer needs, giving you a competitive edge."
            }
            height={"h-auto md:h-[250px]"}
          />
        </div>
      </div>
    </div>
  );
};

export default Section7;
