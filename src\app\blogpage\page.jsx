import Button from "@/Components/Buttons/Button";
import Image from "next/image";
import React from "react";

const page = () => {
  return (
    <section>
      {/* Image Section */}
      <div className="relative max-w-screen h-[250px] sm:h-[350px] md:h-[400px] lg:h-[458px]">
        <Image
          src={"/Images/blogspageimage.png"}
          alt="blogspage"
          layout="fill"
          objectFit="cover"
        />
      </div>

      {/* Main Content Section */}
      <section>
        <div className="w-[90%] mx-auto">
          <div className="mt-10 flex flex-col lg:flex-row gap-10 lg:gap-20">
            {/* Left Content Section */}
            <div className="lg:w-[65%]">
              <h1 className="font-titillium text-3xl sm:text-4xl md:text-5xl">
                10 Inspiring Reasons to Start an Online Business in 2024
              </h1>
              <p className="text-justify mt-4 text-base sm:text-lg md:text-xl">
                If you’ve been thinking about leaving your 9 to 5 and starting
                something of your own in 2024, you’re at the right place.
                Millions of people are exploring this exciting avenue, driven by
                the allure of independence, flexibility, and the potential for
                substantial financial rewards. Statistically speaking, The
                global e-commerce market is expected to total $6.3 trillion in
                2024 (According to Forbes), underscoring the immense potential
                for businesses operating in the digital realm. There is no doubt
                that the digital revolution has irrevocably transformed how we
                live, work, and consume. Moreover, a growing number of consumers
                are turning to the Internet for their shopping needs, creating a
                vast and captive audience for online entrepreneurs.
              </p>
            </div>

            {/* Right Sidebar Section */}
            <div className="lg:w-[25%]">
              <div className="bg-[#7716BC] rounded-md p-4 bg-[url('/Images/community-bg.png')] bg-center bg-no-repeat">
                <h3 className="text-white font-semibold text-base">
                  Share with your community!
                </h3>
                <div className="flex gap-4">
                  <Image
                    src={"/Images/fb-vector.png"}
                    width={20}
                    height={20}
                    alt="facebook"
                  />
                  <Image
                    src={"/Images/x-vector.png"}
                    width={20}
                    height={20}
                    alt="twitter"
                  />
                  <Image
                    src={"/Images/linkedin-vector.png"}
                    width={20}
                    height={20}
                    alt="linkedin"
                  />
                </div>
              </div>
              <div className="mt-8 space-y-3">
                <h3 className="font-semibold text-xl">In this article</h3>
                <h5 className="border-l-2 border-[#F245A1] text-[#F245A1] pl-2 text-base">
                  Exploring Generative AI in Content Creation
                </h5>
                <h5 className="text-base pl-2">
                  Steering Clear of Common AI Writing Pitfalls
                </h5>
                <h5 className="text-base pl-2">
                  Understanding ChatGPT Capabilities - Define Your Style
                </h5>
              </div>
              <div className="mt-8 space-y-3">
                <h4 className="text-lg font-semibold">Written by</h4>
                <div className="flex gap-4 items-center">
                  <Image
                    src={"/Images/profile.png"}
                    width={50}
                    height={50}
                    className="rounded-full"
                    alt="author"
                  />
                  <div>
                    <h5 className="text-lg font-semibold">John Doe</h5>
                    <p className="text-sm">CEO, Valueans</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Call to Action Section */}
          <div className="md:w-[65%] ">
            <div className="bg-[#EE75CD1A] rounded-md my-6 flex-col justify-center items-center p-4">
              <p className="text-center text-2xl text-[#8B4AD1] font-semibold p-4">
                What exactly makes starting an online business so compelling in
                2024?
              </p>
              <div className="max-w-fit mx-auto">
                <Button bgColor="bg-[#F245A1]" hoverColor="opacity-80">
                  Get a Free Consultation!
                </Button>
              </div>
            </div>

            {/* Reason Sections */}
            <div className="mb-4">
              <h1 className="text-2xl font-semibold">1. Be your own boss</h1>
              <p className="text-justify mt-2">
                The dream of being your boss is a universal aspiration. An
                online business offers unprecedented freedom and control. You
                have the liberty to set your own hours, choose your projects,
                and build a business that aligns with your passions and values.
                Studies have consistently shown that entrepreneurship is linked
                to higher levels of job satisfaction and overall life
                fulfillment. By taking the plunge and starting your own online
                venture, you’re on a journey of self-discovery and personal
                growth.
              </p>
            </div>

            {/* Repeat this section for additional reasons as needed */}
            <div className="mb-4">
              <h1 className="text-2xl font-semibold">1. Be your own boss</h1>
              <p className="text-justify mt-2">
                The dream of being your boss is a universal aspiration. An
                online business offers unprecedented freedom and control. You
                have the liberty to set your own hours, choose your projects,
                and build a business that aligns with your passions and values.
                Studies have consistently shown that entrepreneurship is linked
                to higher levels of job satisfaction and overall life
                fulfillment. By taking the plunge and starting your own online
                venture, you’re on a journey of self-discovery and personal
                growth.
              </p>
            </div>
            <div className="mb-4">
              <h1 className="text-2xl font-semibold">1. Be your own boss</h1>
              <p className="text-justify mt-2">
                The dream of being your boss is a universal aspiration. An
                online business offers unprecedented freedom and control. You
                have the liberty to set your own hours, choose your projects,
                and build a business that aligns with your passions and values.
                Studies have consistently shown that entrepreneurship is linked
                to higher levels of job satisfaction and overall life
                fulfillment. By taking the plunge and starting your own online
                venture, you’re on a journey of self-discovery and personal
                growth.
              </p>
            </div>
            <div className="mb-4">
              <h1 className="text-2xl font-semibold">1. Be your own boss</h1>
              <p className="text-justify mt-2">
                The dream of being your boss is a universal aspiration. An
                online business offers unprecedented freedom and control. You
                have the liberty to set your own hours, choose your projects,
                and build a business that aligns with your passions and values.
                Studies have consistently shown that entrepreneurship is linked
                to higher levels of job satisfaction and overall life
                fulfillment. By taking the plunge and starting your own online
                venture, you’re on a journey of self-discovery and personal
                growth.
              </p>
            </div>
          </div>
          {/* Repeat for additional sections */}
        </div>
      </section>
    </section>
  );
};

export default page;
