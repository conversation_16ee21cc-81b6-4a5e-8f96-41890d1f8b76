"use client";
import { useState, useEffect } from "react";
import Link from "next/link";

export default function ExploreServices() {
  // Route mapping for bullet points
  const routeMapping = {
    "Custom Software Development": "/custom-software-development",
    "SaaS Development": "/saas-app-development",
    "Full Stack Development": "/Full_Stack_Development_Services",
    "Custom Website Development": "/custom-website-development",
    "Financial App Development": "/financial-app-development",
    "Dedicated Development Teams": "/Dedicated_Deployment_teams",
    "Healthcare Development": "/health-care-development",
    "Cloud Application Development": "/Cloud_Services",
    "Technology Strategy": "/product-management",
    "System Integration": "/App_Integration",
    "Cloud Solutions": "/Cloud_Services",
    "IT Infrastructure Consulting": "/product-management",
    "Quality assurance and testing": "/Quality_Assurance",
    "AI": "/AI",
    "ML": "/ML",
    "Business Intelligence": "/Business_Intelligence",
    "Data Engineering": "/DataEngineering",
    "Cloud Services": "/Cloud_Services",
    "Generative AI": "/AI",
    "UIUX Design": "/ui-ux",
    "Mobile First Design": "/ui-ux",
    "Mobile Application Development": "/mobile-app-development",
    "Application Integration": "/App_Integration",
  };

  // Content data
  const contentData = {
    "Software Development": {
      heading: "Software Development",
      paragraph:
        "Valueans transforms your most complex concepts into profitable software applications by combining state-of-the-art technology with in-depth subject knowledge. We believe enterprise software development projects need to be well thought out and based on good business concepts. Get a custom software application developed by our skilled teams that perfectly align with your users’ needs and preferences.",
      bulletPoints: [
        "Custom Software Development",
        "SaaS Development",
        "Full Stack Development",
        "Custom Website Development",
        "Financial App Development",
        "Dedicated Development Teams",
        "Healthcare Development",
        "Cloud Application Development",
      ],
    },
    "IT Consulting": {
      heading: "IT Consulting",
      paragraph:
        "Valuenas is your trusted partner for providing IT consulting services to offer you strategic guidance for your business. Our IT consulting managed services optimize your technology stack and processes for delivering results that guarantee success. We offer strategic planning, system optimization, and operational alignment for long-term success.",
      bulletPoints: [
        "Technology Strategy",
        "System Integration",
        "Cloud Solutions",
        "IT Infrastructure Consulting",
      ],
    },
    "Testing & QA": {
      heading: "Testing & QA",
      paragraph:
        "Our quality assurance and testing services are based on our extensive experience and expertise. Valueans QA and testing services offer quality, efficiency, and peace of mind. We promise that your product is secure, user-friendly, bug-free, and able to handle high traffic levels. We test your software solutions from the viewpoints of end users and stakeholders.",
      bulletPoints: ["Quality assurance and testing"],
    },
    "Data Analytics": {
      heading: "Data Analytics",
      paragraph:
        "It's critical to choose the best partner for your data analytics services requirements. We at Valueans are distinguished by our aptitude, customer-focused methodology, and dedication to producing quantifiable outcomes. It's critical to choose the right data analytics service provider for your business. ",
      bulletPoints: [
        "AI",
        "ML",
        "Business Intelligence",
        "Data Engineering",
        "Cloud Services",
        "Generative AI",
      ],
    },
    "Design Services": {
      heading: "Design Services",
      paragraph:
        "At Valueans, get user interface design services that give customers a smooth experience by partnering with Valueans. We are among the best UI/UX design firms in the US because of our many years of expertise in this industry. Innovation and user-centered design are our main priorities. You will always stand out in the market since, unlike others, we keep up with all the latest trends and innovations.",
      bulletPoints: ["UIUX Design", "Mobile First Design"],
    },
    "Application Services": {
      heading: "Application Services",
      paragraph:
        "For delivering our application development services, we take an innovation-led approach to implement a flexible strategy that delivers improved user experiences. With our application management services, we can help you deploy and upgrade your applications securely, efficiently, and cost effectively.",
      bulletPoints: [
        "Mobile Application Development",
        "Application Integration",
      ],
    },
  };

  // State to manage active option
  const [activeOption, setActiveOption] = useState("Software Development");

  // State for accordion open status on smaller screens
  const [openAccordion, setOpenAccordion] = useState("");

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 768) {
        setOpenAccordion(""); // Close the accordion on larger screens
      }
    };

    window.addEventListener("resize", handleResize);
    handleResize(); // Call it once to ensure the state is correct on initial render

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  return (
    <div className=" relative flex flex-col md:flex-row">
      {/* Left Sidebar */}
      <div className="bg-[#7716BC] w-full md:w-1/3 p-8 md:h-[715px] shadow-lg">
        {Object.keys(contentData).map((option) => (
          <div
            key={option}
            onClick={() => {
              if (typeof window !== "undefined" && window.innerWidth < 768) {
                // Accordion for smaller screens
                setOpenAccordion((prev) => (prev === option ? "" : option));
              } else {
                // Standard behavior for larger screens
                setActiveOption(option);
              }
            }}
            className={`p-4 cursor-pointer text-white  text-xl font-medium mt-8 ${
              (typeof window !== "undefined" &&
                window.innerWidth < 768 &&
                openAccordion === option) ||
              (typeof window !== "undefined" &&
                window.innerWidth >= 768 &&
                activeOption === option)
                ? "bg-purple-600"
                : "hover:bg-[#C67FF9]"
            }`}
          >
            {option}

            {/* Accordion content for smaller screens */}
            <div
              className={`transition-all duration-300 overflow-hidden ${
                typeof window !== "undefined" &&
                openAccordion === option &&
                window.innerWidth < 768
                  ? "max-h-[1000px] bg-white text-black p-4 rounded-lg shadow-md mt-4"
                  : "max-h-0"
              }`}
            >
              {typeof window !== "undefined" &&
                openAccordion === option &&
                window.innerWidth < 768 && (
                  <div>

                    <p className="mb-4 text-sm">{contentData[option].paragraph}</p>
                    <ul className="list-disc list-inside">
                      {contentData[option].bulletPoints.map((point, index) => (
                        <li key={index} className="mt-2 text-sm">
                          {routeMapping[point] ? (
                            <Link
                              href={routeMapping[point]}
                              className="text-gray-800 hover:text-[#7716BC] hover:underline cursor-pointer transition-colors duration-200"
                            >
                              {point}
                            </Link>
                          ) : (
                            point
                          )}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
            </div>
          </div>
        ))}
      </div>

      {/* Right Content Panel */}
      <div className="hidden md:block bg-white w-full md:w-2/3 p-8 shadow-lg">
        <h2 className=" text-left mb-3 md:mb-8 mt-2  mx-auto p-2 text-2xl md:text-2xl md:leading-normal text-[#232536] font-semibold underline underline-offset-2 decoration-purple-600 md:px-[30px]">
          {contentData[activeOption].heading}
        </h2>
        <p className="text-[#232222] text-sm md:text-lg md:leading-[30px] px-4 md:px-[30px]">
          {contentData[activeOption].paragraph}
        </p>

        {/* Bullet Points Grid */}
        <div className="grid grid-cols-2 gap-y-6 gap-x-10 mt-6 md:px-[30px]">
          {contentData[activeOption].bulletPoints.map((point, index) => (
            <div key={index} className="flex items-center">
              {/* Red Arrow Symbol */}
              <span className="text-red-500 mr-2 text-lg">›</span>

              {/* Bullet Text */}
              {routeMapping[point] ? (
                <Link
                  href={routeMapping[point]}
                  className="font-semibold text-sm md:text-lg md:leading-[30px] text-gray-800 hover:text-[#7716BC] hover:underline cursor-pointer transition-colors duration-200"
                >
                  {point}
                </Link>
              ) : (
                <span className="font-semibold text-sm md:text-lg md:leading-[30px] text-gray-800">
                  {point}
                </span>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
