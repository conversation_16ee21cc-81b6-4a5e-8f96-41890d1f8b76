const Section3 = () => {
  return (
    <section className="max-w-[90%] mx-auto flex flex-col md:flex-row justify-between items-center  my-10 md:my-24">
      <div className="p-1 md:p-10 flex-1">
        <p className="text-[#7716BC] text-lg md:text-2xl font-semibold md:leading-[30px]">
          What is Fintech Software Development?
        </p>
      </div>
      <div className="p-1 md:p-10 border-t-2 md:border-t-0 md:border-l-4 border-[#F245A1] flex-1">
        <p className=" text-base md:text-lg">
          Enhancing the security of financial transactions through mobile
          banking apps, digital wallets, blockchain systems, and AI powered
          financial analytics is the focus of Fintech software development. The
          best mobile banking apps focus on improving customer experience which
          in turn makes the provision of financial services more user friendly
          and efficient.  We increase the value of your business with
          tailor-made fintech software development services for businesses of
          any size. From startups to well-established businesses, we provide
          scalable, robust and secure fintech solutions.
        </p>
      </div>
    </section>
  );
};

export default Section3;
