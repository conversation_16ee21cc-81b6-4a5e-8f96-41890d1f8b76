// hooks/useSocialLinks.js
import { useEffect, useState } from "react";

const useSocialLinks = () => {
  const [socialLinks, setSocialLinks] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchSocialLinks = async () => {
      try {
        const res = await fetch("https://api.valueans.com/api/socialinks/");
        const data = await res.json();
        setSocialLinks(data);
      } catch (err) {
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    fetchSocialLinks();
  }, []);

  return { socialLinks, loading, error };
};

export default useSocialLinks;
