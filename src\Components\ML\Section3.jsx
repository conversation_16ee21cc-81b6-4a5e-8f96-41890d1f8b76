import React from "react";
import Image from "next/image";
const Section3 = () => {
  return (
    <section className="bg-[#350668] my-3 md:my-24">
      <div className="w-[80%] mx-auto flex flex-col md:flex-row justify-center items-center  py-10">
        <div className="flex-1">
          <h2 className="w-[80%] text-white text-2xl md:text-3xl font-semibold md:leading-10">
            <span className="text-[#F245A1]">
              Valueans Machine Learning Services
            </span>{" "}
            Help Drive Innovation
          </h2>
        </div>
        <div className="flex-1">
          <p className="text-base md:text-xl text-justify font-normal text-white">
            With years of experience in development, we understand the importance of data quality and effective model training which is why we offer 100% effective machine learning solutions. We’re a machine learning solutions company that leverages personalized user experiences from systems that predict market trends with precision.
          </p>
          <p className="text-base md:text-xl font-normal text-white mt-4 md:mt-5">
            With Machine Learning App Development Services at Valueans, we
            offer:
          </p>
          <div className="flex justify-center  mt-5">
            <div className="flex flex-col gap-2 justify-center ">
              {[
                "3x faster, Better, And Cheaper Solutions",
                "Cutting-Edge Machine Learning Solutions",
                "Agile Development",
              ].map((text, index) => (
                <div key={index} className="flex items-start gap-2">
                  <Image
                    src={"/Images/Tick.png"}
                    alt="Tick"
                    width={24}
                    height={24}
                    className="w-6 h-6"
                  />
                  <p className="text-sm md:text-lg text-white">{text}</p>
                </div>
              ))}
            </div>

            <div className="flex flex-col gap-2 justify-center ">
              {[
                "Cross-Industry ML Expertise",
                "Flexible Engagement Models",
                "Process Transparency",
              ].map((text, index) => (
                <div key={index} className="flex items-start gap-2">
                  <Image
                    src={"/Images/Tick.png"}
                    alt="Tick"
                    width={24}
                    height={24}
                    className="w-6 h-6 mt-2"
                  />
                  <p className="text-sm md:text-lg text-white">{text}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Section3;
