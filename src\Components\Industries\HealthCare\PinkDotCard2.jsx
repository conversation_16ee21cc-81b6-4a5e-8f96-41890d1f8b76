import React from "react";

const PinkDotCard2 = ({ cardContent, paragraph }) => {
  return (
    <section className="w-full mx-auto p-4 border border-pink-500 rounded-2xl shadow-md">
      {cardContent.map((item, idx) => (
        <div className="w-full  flex items-start gap-1 mb-4" key={idx}>
          <div className="flex-shrink-0 w-4 md:w-5 h-4 mt-1 md:h-5 bg-[#F245A1]"></div>
          <div className="w-[100%]">

            {item.title && (
              <p
                className="font-semibold text-base"
                dangerouslySetInnerHTML={{ __html: item.title }}
              />
            )}
            {/* Feature title */}
            <p
              className="text-sm text-justify"
              dangerouslySetInnerHTML={{ __html: item.feature }}
            />
            {/* Feature description */}
          </div>
        </div>
      ))}

      <p className="font-semibold text-base md:text-lg">{paragraph}</p>
    </section>
  );
};

export default PinkDotCard2;
