import React from "react";
import Image from "next/image";
import Link from "next/link";

// Array of emerging trends content
const trends = [
  {
    title: "Low-Code & No-Code Development",
    description: `More and more companies are utilizing <a href='/Technologies/Low_Code_Development' class='text-[#7716BC] hover:underline'>low-code and no-code</a> platforms to improve their software development process while minimizing the need for extensive writing. Non-technical personnel can create and edit applications using these very powerful platforms with far more ease than they are used to.`
  },
  {
    title: "AI-Driven Automation",
    description: `AI is changing software applications through automating processes, conducting predictive analytics, and giving customized attention to customers. The development of AI chatbots, intelligent automation systems, and machine learning solutions is improving efficiency in businesses.`
  },
  {
    title: "Cloud-Native Development",
    description: `Cloud-native applications are the new normal as they allow businesses to create and maintain software systems that can be scaled, are robust, and high-performing. This shift is made possible by modern cloud strategies that use microservices architecture and serverless.`
  },
  {
    title: "Blockchain Integration",
    description: `Blockchain technology improves the security, transparency, and trust of business transactions. Enterprises are adopting blockchain for secure sharing of information, supply chain management, and reliable digital identities.`
  },
  {
    title: "Edge Computing",
    description: `With the increase of IoT devices, edge computing is gaining popularity. When data is processed closer to the source, it enhances real-time decision making and security within the connected environment while reducing latency.`
  },
  {
    title: "Improvements in Cybersecurity",
    description: `The transformation in frameworks that businesses embrace now is a result of rising cyberattacks. Predictably, enterprise software will incorporate zero trust architecture, AI threat detection, powerful encryption, and other security measures as the norm.`
  }
];

// InfoCard component
const InfoCard = ({ title, description }) => (
  <div className="w-full bg-white border border-pink-500 p-2 md:p-4 rounded-md shadow-md">
    <div className="flex items-center gap-2 mb-2">
      <div className="flex-shrink-0 w-6 h-6 sm:w-8 sm:h-8">
        <Image
          src="/Images/service_frame.png"
          alt="Tick"
          width={32}
          height={32}
          className="w-full h-full"
        />
      </div>
      <h3 className="text-base md:text-lg font-semibold">{title}</h3>
    </div>
    <p
      className="text-sm md:text-base text-justify"
      dangerouslySetInnerHTML={{ __html: description }}
    />
  </div>
);

// Section5 component with grid layout
const Section5 = () => (
  <section className="bg-blue-100 mb-10 md:mb-24 py-4 md:py-10">
    <div className="container">
      <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
        Emerging Trends in <span className="text-[#F245A1]">Enterprise Software Development</span>
      </h2>
      <p className="w-full md:w-3/5 md:mx-auto text-base md:text-xl text-center my-4">
        The area of enterprise software development is shifting quite rapidly. Here are the trends that are changing the industry:
      </p>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-6 my-6 md:my-[42px]">
        {trends.map((trend, idx) => (
          <InfoCard key={idx} title={trend.title} description={trend.description} />
        ))}
      </div>

      <div className="w-full md:w-4/5 mx-auto bg-[#350668] p-6 rounded-md shadow-sm mt-6">
        <p className="text-white text-justify text-base md:text-xl">
          Development of software in a more sustainable and environmentally friendly manner. Companies seek more efficient software, sustainable development, and optimized cloud use to lower power consumption and carbon emissions.
        </p>
      </div>
    </div>
  </section>
);

export default Section5;
