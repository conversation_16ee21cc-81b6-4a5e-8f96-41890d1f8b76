const ServiceLifecycleCard = ({ title, items = [] }) => {
  return (
    <div className="block w-[289px] md:max-w-sm h-auto md:h-[40vh] p-3 md:p-6 bg-white border border-gray-200 rounded-lg shadow overflow-hidden">
      <h5 className="mb-1 md:mb-2 text-base md:text-2xl font-bold tracking-tight text-gray-900 ">
        {title}
      </h5>
      <ul className="list-disc list-inside text-gray-700 text-sm md:text-base mt-1 md:mt-2 space-y-1">
        {items.map((item, index) => (
          <li key={index}>{item}</li>
        ))}
      </ul>
    </div>
  );
};

export default ServiceLifecycleCard;
