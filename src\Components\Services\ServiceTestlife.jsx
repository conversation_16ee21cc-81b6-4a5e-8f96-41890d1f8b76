import React from "react";
import ServiceLifecycleCard from "./ServiceLifecycleCard";
import ServiceCount from "./ServiceCount";
import Connector1 from "../Connectors/Connector1";
import Connector2 from "../Connectors/Connector2";
import Connector3 from "../Connectors/Connector3";
const ServiceTestlife = () => {
  const planningItems = [
    "Collect all relevant information",
    "Plan and communicate the best custom solutions for you",
  ];

  const designingItems = [
    "Define overall system architecture",
    "Prepare the designs as per the requirements",
  ];

  const definingAndDevelopingItems = [
    "Define and document software needs",
    "Coding and building the software",
    "Ask for your feedback",
  ];

  const testingItems = ["Evaluate the software", "Fix bugs and errors"];

  const deploymentItems = [
    "Check for deployment issues",
    "Ask for your approval",
    "Release the final software",
  ];

  return (
    <div className="relative">
      <div className="flex justify-center items-center gap-8 mb-20 ">
        <div className="flex-col justify-center items-center">
          <ServiceLifecycleCard title="Planning" items={planningItems} />
          <ServiceCount count={"1"} />
        </div>
        <div className="flex-col justify-center items-center">
          <ServiceLifecycleCard
            title="Planning"
            items={definingAndDevelopingItems}
          />
          <ServiceCount count={"2"} />
        </div>
        <div className="flex-col justify-center items-center">
          <ServiceLifecycleCard title="Planning" items={deploymentItems} />
          <ServiceCount count={"3"} />
        </div>
      </div>
      <div className="absolute top-[51%] left-[36%]">
        <Connector1 />
      </div>
      <div className="absolute top-[55%] left-[48%]">
        <Connector2 />
      </div>
      <div className="absolute top-[50%] left-[61%]">
        <Connector1 />
      </div>
      <div className="absolute top-[55%] left-[71%]">
        <Connector3 />
      </div>
      <div className="w-[80%] mx-auto flex justify-center items-center gap-8 mt-20 relative">
        <div className="flex-col justify-center items-center absolute left-[25%]">
          <ServiceCount count={"4"} />
          <ServiceLifecycleCard title="Planning" items={designingItems} />
        </div>
        <div className="flex-col justify-center items-center ml-[40%]">
          <ServiceCount count={"5"} />
          <ServiceLifecycleCard title="Planning" items={testingItems} />
        </div>
      </div>
    </div>
  );  
};

export default ServiceTestlife;
