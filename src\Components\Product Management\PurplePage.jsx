import Image from "next/image";
import React from "react";

const PurplePage = () => {
  return (
    <section className="bg-[#350668] my-24 p-20">
      <h2 className="text-white text-center text-4xl font-semibold">
        Why Valueans?
      </h2>
      <p className="text-white text-center text-xl mb-10">
        At Valueans, we make sure that our unique approach and commitment to
        quality distinguish us from our competitors:
      </p>
      <div className="flex justify-center items-center gap-20">
        <div className="flex-col gap-10">
          <div className="my-5">
            <div className="flex gap-2">
              <Image
                src={"/Images/Tick.png"}
                alt="Tick"
                width={31.5}
                height={31.5}
              />
              <h3 className="text-white text-2xl font-medium">
                Holistic Approach
              </h3>
            </div>
            <p className="text-white text-xl">
              We combine market research, user experience design, agile
              development, and continuous improvement into a managed approach
              that ensures our products' success from beginning to end.
            </p>
          </div>
          <div className="my-5">
            <div className="flex gap-2">
              <Image
                src={"/Images/Tick.png"}
                alt="Tick"
                width={31.5}
                height={31.5}
              />
              <h3 className="text-white text-2xl font-medium">
                Holistic Approach
              </h3>
            </div>
            <p className="text-white text-xl">
              We combine market research, user experience design, agile
              development, and continuous improvement into a managed approach
              that ensures our products' success from beginning to end.
            </p>
          </div>
          <div className="my-5">
            <div className="flex gap-2">
              <Image
                src={"/Images/Tick.png"}
                alt="Tick"
                width={31.5}
                height={31.5}
              />
              <h3 className="text-white text-2xl font-medium">
                Holistic Approach
              </h3>
            </div>
            <p className="text-white text-xl">
              We combine market research, user experience design, agile
              development, and continuous improvement into a managed approach
              that ensures our products' success from beginning to end.
            </p>
          </div>
        </div>
        <div className="flex-col gap-10">
          <div className="my-5">
            <div className="flex gap-2">
              <Image
                src={"/Images/Tick.png"}
                alt="Tick"
                width={31.5}
                height={31.5}
              />
              <h3 className="text-white text-2xl font-medium">
                Holistic Approach
              </h3>
            </div>
            <p className="text-white text-xl">
              We combine market research, user experience design, agile
              development, and continuous improvement into a managed approach
              that ensures our products' success from beginning to end.
            </p>
          </div>
          <div className="my-5">
            <div className="flex gap-2">
              <Image
                src={"/Images/Tick.png"}
                alt="Tick"
                width={31.5}
                height={31.5}
              />
              <h3 className="text-white text-2xl font-medium">
                Holistic Approach
              </h3>
            </div>
            <p className="text-white text-xl">
              We combine market research, user experience design, agile
              development, and continuous improvement into a managed approach
              that ensures our products' success from beginning to end.
            </p>
          </div>
          <div className="my-5">
            <div className="flex gap-2">
              <Image
                src={"/Images/Tick.png"}
                alt="Tick"
                width={31.5}
                height={31.5}
              />
              <h3 className="text-white text-2xl font-medium">
                Holistic Approach
              </h3>
            </div>
            <p className="text-white text-xl">
              We combine market research, user experience design, agile
              development, and continuous improvement into a managed approach
              that ensures our products' success from beginning to end.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PurplePage;
