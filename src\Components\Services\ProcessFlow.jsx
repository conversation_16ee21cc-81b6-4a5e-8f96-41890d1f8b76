import React from "react";

const ProcessBox = ({ number, title, items, className }) => (
  <div
    className={`border border-purple-200 rounded-lg p-4 bg-white shadow-sm w-64 ${className}`}
  >
    <div className="absolute -mt-6 -ml-6">
      <div className="w-12 h-12 rounded-full bg-pink-500 flex items-center justify-center text-white font-bold">
        {number}
      </div>
    </div>
    <h3 className="font-medium mb-2 mt-4">{title}</h3>
    <ul className="text-sm space-y-2">
      {items.map((item, index) => (
        <li key={index} className="flex items-start">
          <span className="mr-2">•</span>
          {item}
        </li>
      ))}
    </ul>
  </div>
);

const Connector = ({ className }) => (
  <div className={`border-dashed border-2 border-gray-300 ${className}`}></div>
);

export default function SDLCDiagram() {
  return (
    <div className="relative w-full h-[600px] bg-pink-50 p-8">
      <div className="relative">
        {/* Planning */}
        <ProcessBox
          number="1"
          title="Planning"
          items={[
            "Collect all relevant information",
            "Plan and communicate the best custom solutions for you",
          ]}
          className="absolute top-0 left-0"
        />

        {/* Designing */}
        <ProcessBox
          number="2"
          title="Designing"
          items={[
            "Define overall system architecture",
            "Prepare the designs as per the requirements",
          ]}
          className="absolute top-48 left-32"
        />

        {/* Defining & Developing */}
        <ProcessBox
          number="3"
          title="Defining & Developing"
          items={[
            "Define and document software needs",
            "Coding and building the software",
            "Ask for your feedback",
          ]}
          className="absolute top-0 left-[400px]"
        />

        {/* Testing */}
        <ProcessBox
          number="4"
          title="Testing"
          items={["Evaluate the software", "Fix bugs and errors"]}
          className="absolute top-48 right-32"
        />

        {/* Deployment */}
        <ProcessBox
          number="5"
          title="Deployment"
          items={[
            "Check for deployment issues",
            "Ask for approval",
            "Release the final software",
          ]}
          className="absolute top-0 right-0"
        />

        {/* Connectors */}
        <Connector className="absolute top-24 left-48 w-32 rotate-45" />
        <Connector className="absolute top-24 left-[320px] w-32 -rotate-45" />
        <Connector className="absolute top-24 right-[320px] w-32 rotate-45" />
        <Connector className="absolute top-24 right-48 w-32 -rotate-45" />
      </div>
    </div>
  );
}
