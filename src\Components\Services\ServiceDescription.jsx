import React from "react";

const ServiceDescription = ({
  primaryText,
  primaryTextHighlight,
  secondaryText,
}) => {
  return (
    <div className="max-w-[90%] mx-auto flex justify-center items-center mb-20">
      <div className="p-10 flex-1">
        <p className="text-lg">
          {primaryText}{" "}
          <span className="font-bold">{primaryTextHighlight}</span>
        </p>
      </div>
      <div className="p-10 border-l-4 border-[#F245A1] flex-1">
        <p className="text-[#7716BC] text-2xl font-semibold leading-[30px]">
          {secondaryText}
        </p>
      </div>
    </div>
  );
};

export default ServiceDescription;
