"use client";
import React from "react";
import { useState } from "react";
import Card_holder from "./Card/Card_holder";

const Carousel = () => {
  const handlePrev = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? cards.length - 1 : prevIndex - 1
    );
  };

  const handleNext = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === cards.length - 1 ? 0 : prevIndex + 1
    );
  };
  const [currentIndex, setCurrentIndex] = useState(0);
  const [activeStep, setActiveStep] = useState(0);
  return (
    <div>
      <div className="flex justify-center items-center">
        <Card_holder />
        <Card_holder />
        <Card_holder />
      </div>

      <div className="flex justify-center items-center mt-4">
        <button
          onClick={handlePrev}
          className={`p-4 rounded-full mr-4 flex items-center justify-center ${
            activeStep === 0
              ? "bg-white text-gray-500 cursor-not-allowed"
              : "bg-pink-500 hover:bg-pink-600 text-white"
          }`}
          disabled={activeStep === 0}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className={`h-6 w-6 ${activeStep === 0 ? "text-gray-500" : "text-white"}`}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            strokeWidth={2}
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M15 19l-7-7 7-7"
            />
          </svg>
        </button>

        <button
          onClick={handleNext}
          className={`p-4 rounded-full flex items-center justify-center ${
            activeStep === reviews.length - 1
              ? "bg-white text-gray-500 cursor-not-allowed"
              : "bg-pink-500 hover:bg-pink-600 text-white"
          }`}
          disabled={activeStep === reviews.length - 1}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className={`h-6 w-6 ${
              activeStep === reviews.length - 1 ? "text-gray-500" : "text-white"
            }`}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            strokeWidth={2}
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M9 5l7 7-7 7"
            />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default Carousel;
