import React from "react";
import ServiceLifecycleCard from "./ServiceLifecycleCard";
import ServiceCount from "./ServiceCount";

const ServiceLifecycle = () => {
  const planningItems = [
    "Collect all relevant information ",
    "Plan and communicate the best custom solutions for you",
  ];
  const devItems = [
    "Collect all relevant information ",
    "Plan and communicate the best custom solutions for you",
  ];
  const deployItems = [
    "Collect all relevant information ",
    "Plan and communicate the best custom solutions for you",
  ];
  const designItems = [
    "Collect all relevant information ",
    "Plan and communicate the best custom solutions for you",
  ];
  const testItems = [
    "Collect all relevant information ",
    "Plan and communicate the best custom solutions for you",
  ];

  // Conditional rendering to avoid any undefined issues
  if (!planningItems || !devItems || !deployItems || !designItems || !testItems) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      <div>
        <div className="flex-col">
          <ServiceLifecycleCard title="Planning" items={planningItems} />{" "}
          <ServiceCount />
        </div>
        <div className="flex-col">
          <ServiceLifecycleCard
            title="Defining & Developing"
            items={devItems}
          />{" "}
          <ServiceCount />
        </div>
        <div className="flex-col">
          <ServiceLifecycleCard title="Deployment" items={deployItems} />{" "}
          <ServiceCount />
        </div>
      </div>
      <div>
        <div className="flex-col">
          <ServiceCount />{" "}
          <ServiceLifecycleCard title="Designing" items={designItems} />
        </div>
        <div className="flex-col">
          <ServiceCount />{" "}
          <ServiceLifecycleCard title="Testing" items={testItems} />
        </div>
      </div>
    </div>
  );
};

export default ServiceLifecycle;
