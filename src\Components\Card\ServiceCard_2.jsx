import React from "react";

const ServiceCard_2 = ({ title, content, cardHeight }) => {
  // helper to render either HTML, list, or React node
  const renderContent = () => {
    // 1) if they passed an array, treat each entry as a list item
    if (Array.isArray(content)) {
      return (
        <ul className="list-disc pl-5 space-y-1 font-normal text-sm md:text-base text-[#232222]">
          {content.map((item, idx) => (
            <li key={idx} dangerouslySetInnerHTML={{ __html: item }} />
          ))}
        </ul>
      );
    }

    // 2) if it's a string, use your existing HTML fallback
    if (typeof content === "string") {
      return (
        <div
          className="font-normal text-sm md:text-base text-[#232222]"
          dangerouslySetInnerHTML={{
            __html: content,
          }}
        />
      );
    }

    // 3) otherwise assume it's a React node (e.g. someone passed in <ul>…</ul> themselves)
    return (
      <div className="font-normal text-sm md:text-base text-[#232222]">
        {content}
      </div>
    );
  };

  return (
    <div
      className={`block max-w-sm p-6 text-justify bg-white border border-gray-200 rounded-lg shadow ${
        cardHeight || ""
      }`}
    >
      <h5 className="mb-1 md:mb-2 text-base md:text-lg font-bold tracking-tight text-[#7716BC]">
        {title}
      </h5>
      {renderContent()}
    </div>
  );
};

export default ServiceCard_2;
