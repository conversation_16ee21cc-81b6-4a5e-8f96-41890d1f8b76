import React from "react";

const Card = ({ title, description }) => {
  return (
    <div className="block w-full md:max-w-md h-auto md:h-[220px] p-3 md:p-5 bg-white border  rounded-md shadow-md overflow-hidden">
      <h2 className="text-[#7716BC] text-base md:text-lg font-semibold mb-1 md:mb-2">
        {title}
      </h2>
      <p className="text-sm md:text-base text-justify">{description}</p>
    </div>
  );
};

const Section7 = () => {
  return (
    <div className="w-[85%] mx-auto mb-10 md:mb-24">
      <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
        Our Approach to{" "}
        <span className="text-[#7716BC]">
          Custom Machine Learning Solutions
        </span>
      </h2>
      <div className="flex flex-col justify-center items-center gap-3 md:gap-6 my-3 md:my-6">
        <div className="flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6">
          <Card
            title={"Consultation"}
            description={
              "At Valueans, our team of experts is always there to listen to your queries and offer the most suitable solution for your business. We ensure that you’re fully satisfied right from the first call to the deployment of an ML solution."
            }
          />
          <Card
            title={"Modeling"}
            description={
              "Our team is trained to prepare datasets for effective ML modeling and algorithm training by taking data collection, cleansing, and structuring off of your hands so your business gets what it deserves without any hassle."
            }
          />
        </div>

        <div className="flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6">
          <Card
            title={"Implementation"}
            description={
              "Whether it’s a new model or existing enterprise software, our skilled engineers are always ready to provide you with the latest and high-performing custom machine-learning solutions or integrate them to the next level."
            }
          />
          <Card
            title={"Support"}
            description={
              "At Valueans, we support businesses in maintaining and improving the effectiveness of machine learning models, we keep an eye on key performance indicators, carry out testing, and evaluate the outcomes to guarantee ongoing progress."
            }
          />
        </div>
      </div>
    </div>
  );
};

export default Section7;
