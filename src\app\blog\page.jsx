
import SecondBlogDisplaySection from "@/Components/Blog/SecondBlogDisplaySection";
import TopBlogDisplay from "@/Components/Blog/TopBlogDisplay";
import Blog from "@/Components/pages/Blog";

import React from "react";
export const metadata = {
  title: "Stay Informed of engaging Articles and Trends with Our Blog",
  description: "Dive into our blog to get insights into trends and solutions for custom software development. Stay tuned with our extensive research and creative solutions.",
};
const Blogpage = () => {
  const blogPosts = [
    {
      imageSrc: "/Images/blogcard1.png",
      title: "10 Inspiring Reasons to Start an Online Business in 2024",
      slug: "10-inspiring-reasons",
      date: "27 Jan 2021",
    },
    {
      imageSrc: "/Images/blogcard1.png",
      title: "How to Be Your Own Boss in 2024",
      slug: "how-to-be-your-own-boss",
      date: "15 Feb 2021",
    },
    {
      imageSrc: "/Images/blogcard1.png",
      title: "Top Online Business Trends for 2024",
      slug: "top-business-trends-2024",
      date: "01 Mar 2021",
    },
    {
      imageSrc: "/Images/blogcard1.png",
      title: "Building an Online Business: A Step-by-Step Guide",
      slug: "building-online-business",
      date: "10 Apr 2021",
    },
    ,
    {
      imageSrc: "/Images/blogcard1.png",
      title: "Building an Online Business: A Step-by-Step Guide",
      slug: "building-online-business",
      date: "10 Apr 2021",
    },
  ];
  return (
    <div>
      <TopBlogDisplay />
      <SecondBlogDisplaySection />
      {/* <BlogDisplaySection blogs={blogPosts} /> */}
      <Blog title="Technology related news/Blogs" />
    </div>
  );
};

export default Blogpage;
