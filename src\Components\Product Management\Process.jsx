import React from "react";
import ServiceCount from "../Services/ServiceCount";
import ServiceLifecycleCard from "../Services/ServiceLifecycleCard";
import Connector7 from "../Connectors/Connector7";


const Process = () => {
  const discoveryAndResearch = [
    "Conduct market research and analysis",
    "Identify customer needs and pain points",
    "Define product vision and goals",
  ];

  const strategyAndPlanning = [
    "Make a product roadmap",
    "Understand key milestones and deliverables",
    "Resource allocation and budgeting",
  ];

  const designAndPrototyping = [
    "Create wireframes and mockups",
    "Develop prototypes for user testing",
    "Refine the design based on feedback",
  ];

  const developmentAndTesting = [
    "Build the product using the latest technologies",
    "Conduct rigorous testing to ensure quality and functionality",
    "Iterate based on testing results",
  ];

  const launchAndGoToMarket = [
    "Develop a go-to-market strategy",
    "Coordinate marketing and promotional activities",
    "Provide training and support to the sales team",
  ];

  const postLaunchAndIteration = [
    "Monitor product performance and user feedback",
    "Release updates and improvements",
    "Adapt the product strategy as needed to maintain market relevance",
  ];

  return (
    <div className="w-[90vw] mx-auto mb-24">
      <h2 className="text-center text-4xl mb-10">
        Our Step-by-Step{" "}
        <span className="text-[#F245A1]">
          UI/UX <br /> Development Process
        </span>{" "}
      </h2>
      <div className="relative">
        <div className="flex justify-center items-center gap-8 mb-20 ">
          <div className="flex-col justify-center items-center">
            <ServiceLifecycleCard
              title="Discovery and Research"
              items={discoveryAndResearch}
            />
            <ServiceCount count={"1"} />
          </div>
          <div className="flex-col justify-center items-center">
            <ServiceLifecycleCard
              title="Design and Prototyping"
              items={designAndPrototyping}
            />
            <ServiceCount count={"2"} className="z-10" />
          </div>
          <div className="flex-col justify-center items-center">
            <ServiceLifecycleCard
              title="Launch and Go-To-Market"
              items={launchAndGoToMarket}
            />
            <ServiceCount count={"3"} />
          </div>
        </div>
        <div className="absolute top-[48%] left-[26%]">
          <Connector7 />
        </div>
        <div className="absolute top-[50%] left-[38%]">
          <div
            className="w-[280px] h-[125px] border-dashed border-b-2 border-r-2 border-gray-700 absolute"
            style={{
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
            }}
          />
        </div>
        <div className="absolute top-[46%] left-[54%]">
          <div
            className="w-[130px] h-[130px] border-dashed border-t-2 border-r-2 border-gray-700 absolute"
            style={{
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
            }}
          />
        </div>
        <div className="absolute top-[48%] left-[67%]">
          <div
            className="w-[230px] h-[110px] border-dashed border-b-2 border-r-2 border-gray-700 absolute"
            style={{
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
            }}
          />
        </div>
        <div className="absolute top-[46%] left-[82%]">
          <div
            className="w-[140px] h-[120px] border-dashed border-t-2 border-r-2 border-gray-700 absolute"
            style={{
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
            }}
          />
        </div>
        <div className=" ml-[15%] flex justify-center items-center gap-8 mt-20 relative">
          <div className="flex-col justify-center items-center absolute top-[13%] -left-[1%]">
            <ServiceCount count={"4"} />
            <ServiceLifecycleCard
              title="Strategy and Planning"
              items={strategyAndPlanning}
            />
          </div>
          <div className="flex-col justify-center items-center ml-[35%]">
            <ServiceCount count={"5"} />
            <ServiceLifecycleCard
              title="Development and Testing"
              items={developmentAndTesting}
            />
          </div>
          <div className="flex-col justify-center items-center ">
            <ServiceCount count={"6"} />
            <ServiceLifecycleCard
              title="Post-Launch and Iteration"
              items={postLaunchAndIteration}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Process;
