"use client";
import React from "react";
import Image from "next/image";

const Industries = ({ onNavigate }) => {
  return (
    <div className="md:container md:mx-auto md:p-10 md:border md:rounded-lg">
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        {/* Image Section */}
        <div className="col-span-1 md:col-span-2">
          <Image className="hidden md:block" src={"/Images/industries-nav.jpeg"} width={400} height={400} alt="Industries" />
        </div>
        
        {/* Links Section */}
        <div className="col-span-1 md:col-span-3 space-y-4 md:space-y-0 md:flex md:justify-between">
          <div className="flex flex-col gap-3 md:w-[30%]">
            <button onClick={() => onNavigate("/Industries/Healthcare")} className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Healthcare
            </button>
            <button onClick={() => onNavigate("/Industries/Gaming")} className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Gaming
            </button>
            <button onClick={() => onNavigate("/Industries/E-Commerce")} className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              E-commerce
            </button>
            <button onClick={() => onNavigate("/Industries/Agriculture")} className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Agriculture
            </button>
            <button onClick={() => onNavigate("/Industries/Travel")} className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Travel
            </button>
            <button onClick={() => onNavigate("/Industries/Automotive")} className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Automotive
            </button>
          </div>

          <div className="flex flex-col gap-3 md:w-[30%]">
            <button onClick={() => onNavigate("/Industries/Finance")} className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Finance
            </button>
            <button onClick={() => onNavigate("/Industries/Education")} className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Education
            </button>
            <button onClick={() => onNavigate("/Industries/Social_Networking")} className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Social Networking
            </button>
            <button onClick={() => onNavigate("/Industries/Oil_And_Gas")} className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Oil and Gas
            </button>
            <button onClick={() => onNavigate("/Industries/Real_Estate")} className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Real Estate
            </button>
            <button onClick={() => onNavigate("/Industries/Insurance")} className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Insurance
            </button>
          </div>

          <div className="flex flex-col gap-3 md:w-[30%]">
            <button onClick={() => onNavigate("/Industries/Telecom")} className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Telecom
            </button>
            <button onClick={() => onNavigate("/Industries/Construction")} className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Construction
            </button>
            <button onClick={() => onNavigate("/Industries/Manufacturing")} className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Manufacturing
            </button>
            <button onClick={() => onNavigate("/Industries/Logistics")} className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Logistics
            </button>
            <button onClick={() => onNavigate("/Industries/Banking")} className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Banking
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Industries;
