import React from "react";

const Section3 = () => {
  return (
    <div className="w-[85%] mx-auto my-10 md:my-24  gap-6 md:gap-10 flex flex-col md:flex-row justify-center md:justify-between items-center">
      <div className="w-full md:w-[40%]">
        <h2 className="text-xl md:text-3xl font-semibold">
          {" "}
          <span className="text-[#7716BC]">
            Agile, Scalable, and Secure
          </span>{" "}
          Web Application Integration
        </h2>
        <p className="text-base md:text-xl text-justify mt-2">
          Our enterprise application integration services connect your applications, processes, and data. In addition to providing real-time data access, our solutions boost operational efficiency, encourage well-informed decision-making, and optimize the value of your present systems without incurring further expenses.  
        </p>
      </div>
      <div className="w-full md:w-[45%]">
        <section className="w-full  mx-auto p-4 border border-pink-800 rounded-xl shadow-md">
          <h3 className="text-lg md:text-xl font-semibold">
            With App Integration Services at Valueans, we:
          </h3>
          <div className="py-5 flex flex-col gap-3 md:gap-5">
            <div className="flex items-center gap-4">
              <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
              <p className="text-sm md:text-lg">Build custom applications</p>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
              <p className="text-sm md:text-lg">
                Extend and connect those applications
              </p>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
              <p className="text-sm md:text-lg">
                Deliver comprehensive support
              </p>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
              <p className="text-sm md:text-lg">Integrate</p>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
              <p className="text-sm md:text-lg">Upgrade</p>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
              <p className="text-sm md:text-lg">
                Automate end-to-end processes
              </p>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default Section3;
