import Image from "next/image";
import React from "react";

const ServiceNeed = () => {
  return (
    <div>
      <section className="bg-pink-100">
        <div className="max-w-[80%] mx-auto py-8 flex justify-between items-center gap-10">
          <div className="flex-1">
            <h2 className="text-4xl font-semibold">We Know What You Need!</h2>
            <p className="text-lg leading-7 font-normal">
              Unlike other software development companies, Valueans goes above
              and beyond to meet your requirements because we understand issues
              like high costs, inadequate customization, and outdated frameworks
              can hinder your business from growing which is why we offer
              customized solutions for iOS, Android, and Cross-platform
              applications that help you build a scalable and futuristic product
              without wasting millions.
            </p>
          </div>
          <div className="flex-1 w-[438px] h-[314px] relative">
            <Image
              src="/Images/Md-1.png"
              alt="custom"
              width={438}
              height={314}
              // layout="fill"
              objectFit="contain" // or "contain" based on your requirement
            />
          </div>
        </div>
      </section>
    </div>
  );
};

export default ServiceNeed;
