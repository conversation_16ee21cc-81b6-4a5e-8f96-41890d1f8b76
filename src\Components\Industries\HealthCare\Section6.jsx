import React from "react";
import Image from "next/image";
import IconTextCard from "./IconTextCard";

const Section6 = ({
  cardData,
  headingLeft,
  headingRight,
  spanHeading,
  image,
  paragraph
}) => {
  return (
    <div className="bg-[#1F62EA26] py-3">
      <div className="w-[90%] mx-auto mb-10 md:my-24 ">
        <div className="my-2  flex flex-col md:items-end">
          <h1 className="text-xl md:text-3xl md:leading-[40px] md:pl-4 md:w-[50%] font-semibold">
            {headingLeft} <span className="text-[#F245A1]">{spanHeading}</span>{" "}
            {headingRight}
          </h1>
          <p className="text-base md:text-lg md:pl-4 md:w-[50%]">{paragraph}</p>
        </div>
        <div className="flex flex-col md:flex-row justify-between gap-8">
          <div className="md:w-[50%] min-h-[250px] md:min-h-0 w-full relative">
            {" "}
            <Image
              src={image}
              alt="AI"
              layout="fill" // Use layout="fill" to take up the entire space of the div
              objectFit="cover" // Ensures the image covers the entire space
              objectPosition="center" // Centers the image within the div
            />
          </div>
          <div className="md:w-[50%]">
            <IconTextCard cardData={cardData} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Section6;
