import React from "react";
import Heading from "../Heading/Heading";

const Card = ({ title, description }) => {
  return (
    <div className="block max-w-lg h-auto md:h-[280px] overflow-hidden bg-white p-4 border border-gray-200 shadow-md rounded-lg">
      <h3 className="text-[#7716BC] text-base md:text-lg font-medium">
        {title}
      </h3>
      <p className="text-sm md:text-base">{description}</p>
    </div>
  );
};

const Section5 = () => {
  return (
    <div className="mx-auto md:mx-[75px] mb-10 md:mb-24">
      <Heading>
        What Does Valueans’{" "}
        <span className="text-[#F245A1]">
          Low Code Mobile App <br /> Development
        </span>{" "}
        Offer?
      </Heading>
      <div className="flex flex-col md:flex-row justify-center items-center gap-4 md:gap-8 mt-6 md:mt-[42px]">
        <Card
          title={"Rapid Application Composition"}
          description={
            "Create and deliver scalable, web and mobile apps quickly with the Low-Code Application Development platform. You may quickly create dynamic, rich apps that are customized to your specific business requirements by using pre-made templates and components. Simplify development by abstracting away intricate code. "
          }
        />
        <Card
          title={"Unified Data Management"}
          description={
            "Easy access to data is ensured by the smooth interaction with external databases and applications made possible by low-code application development platforms. Utilize integrated data modeling tools to create both basic and sophisticated data objects. By using masking and encryption to ensure data security and integrity, you enable your company to handle data effectively. "
          }
        />
        <Card
          title={"Effortless Deployment"}
          description={
            "The Low-Code Application Development platform makes it easier for operations teams and developers to work together. It serves many advantages for businesses such as easily combining with current systems, quicker deployment, speeding the time to market, increasing output, lowering expenses, and providing superior software solutions with little time or money."
          }
        />
      </div>
    </div>
  );
};

export default Section5;
