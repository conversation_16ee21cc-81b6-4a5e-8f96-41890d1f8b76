const Gradient_card = ({ title, description }) => {
  return (
    <>
      <div className="block w-full md:max-w-md h-auto md:h-[450px] overflow-hidden bg-gradient-to-br from-pink-500 to-purple-700 text-white p-6 rounded-lg">
        <h2 className="text-lg md:text-2xl md:leading-8 font-bold">
          {title}
        </h2>
        <p className="text-base md:text-lg my-1 md:my-3">{description}</p>
      </div>
    </>
  );
};

const Section8 = () => {
  return (
    <div className="w-[85%] mx-auto mb-10 md:mb-24">
      <h2 className="text-xl md:text-3xl text-center font-semibold">
        Case Studies
      </h2>
      <div className="flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6 mt-6 md:mt-[42px]">
        <Gradient_card
          title={"1.Digital Wallet Solution for Global Payment Provider"}
          description={
            "With Valueans’ help, a global payment firm was able to build a digital wallet that supports different currencies, performs transactions in real time, and connects to many financial service institutions. Enhanced transaction speed and sophisticated security blockchain technology."
          }
        />
        <Gradient_card
          title={"2.AI-Powered Risk Assessment for Fintech Startup"}
          description={
            "As one of the fintech startups, you’ve teamed up with Valueans to develop an AI-based tool for risk assessment that scrutinizes financial transactions and, in real time, detects fraudulent activities. This modernization eliminated 40% of fraud and increased the confidence of the clients."
          }
        />
        <Gradient_card
          title={"3.Blockchain-Based Smart Contract Platform"}
          description={
            "Valueans designed and developed a blockchain based smart contract platform that automated the conclusion of financial contracts and other agreements, which cut the processing time from weeks to minutes. This invention fostered greater operational efficiency and lower costs."
          }
        />
      </div>
    </div>
  );
};

export default Section8;
