import React from "react";
import Image from "next/image";

const Appservice = () => {
  return (
    <div>
      <section className="bg-pink-100 py-24">
        <div className="max-w-[80%] mx-auto">
          <h2 className="text-4xl text-center font-semibold">
            Why is{" "}
            <span className="text-[#F245A1]">
              custom mobile app development
            </span>{" "}
            important for Business?
          </h2>
          <p className="text-center text-xl">
            According to a survey, mobile users spent an average of 4.8 hours on
            their devices in 2021, which shows a significant opportunity to
            reach and engage potential customers through mobile apps.
          </p>
        </div>
        <div className="max-w-[90%] mx-auto py-8 flex justify-center items-center gap-16">
          <div className="flex-1">
            <p className="text-lg leading-7 font-normal">
              Custom Mobile App Development refers to the process of creating a
              tailored, well-thought, and developed mobile app for a business
              that caters to their specific needs and requirements, unlike other
              off-the-shelf apps that are generic and offer a one-size-fits-all
              solution, customized applications perfectly fulfill the company's
              goals and form an image of brand and trust in the user's mind. 
              Moreover, if crafted correctly, custom mobile applications can
              greatly emphasize your business. The $170 billion worldwide app
              store spending in 2021, with a 30% year-over-year increase,
              showcases the revenue potential of mobile{" "}
              <span className="font-bold">
                Here are some reasons you should consider having a custom mobile
                app for your business:
              </span>
            </p>
          </div>
          <div className="flex-1 w-[536px] h-[450px] relative">
            <Image
              src="/Images/app-dev.png"
              alt="custom"
              layout="fill"
              objectFit="contain" // or "contain" based on your requirement
            />
          </div>
        </div>
      </section>
    </div>
  );
};

export default Appservice;
