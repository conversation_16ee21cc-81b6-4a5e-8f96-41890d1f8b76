'use client'

import React, { useState } from "react";
import Image from "next/image";
import Button from "../Buttons/Button";
import Toast from '../ui/Toast';
import useToast from '../../hooks/useToast';

const Section1 = ({ backgroundImage, heading, bannerText }) => {
  // State to handle form data
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone_number: '',
    message: ''
  });

  // State for form submission status
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Toast notification hook
  const { toast, showSuccess, showError, hideToast } = useToast();

  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prevState => ({
      ...prevState,
      [name]: value
    }));
  }

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    console.log(formData)
    try {
      const response = await fetch('https://api.valueans.com/api/contact-us/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        showSuccess("Thank you for your submission! We will contact you shortly.");
        setFormData({
          name: '',
          email: '',
          phone_number: '',
          message: ''
        });
      } else {
        showError("Something went wrong. Please try again.");
      }
    } catch (error) {
      console.error("Error submitting the form:", error);
      showError("An error occurred. Please try again later.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    
    <section
      className=" relative min-h-screen md:min-h-screen"
      style={{
        backgroundImage: `url(${backgroundImage})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
      }}
    >
      {/* Overlay div */}
      <div className="absolute inset-0 bg-[rgba(119,22,188,0.6)] z-0"></div>

      {/* Content */}
      <div className="flex flex-col md:flex-row justify-center items-start md:items-center py-8 md:py-0 h-full container md:h-screen relative z-10">
        <div className="flex flex-col items-start w-full md:w-[50%] gap-3 px-8 mb-8 md:mb-0">
          <h2 className="font-titillium text-3xl md:mb-4 font-bold md:text-5xl text-white ">
            {heading}
          </h2>

          {/* Full width h3 */}
          <h3 className="w-full font-semibold font-titillium text-lg md:text-2xl py-3 md:py-4 text-white">
            {bannerText}
          </h3>
          <Button to={"/contact"} bgColor="bg-[#F245A1] hidden md:block" paddingX="px-8" paddingY="py-2" hoverColor="opacity-90">
            Book your Consultation
          </Button>
        </div>
        <div className="w-full md:w-[50%] px-8 pb-8 md:pb-0">
          <form onSubmit={handleSubmit} className="flex flex-col gap-4 bg-[#350668] p-4 md:p-8 rounded-md">
            <input
              type="text"
              className="p-3 rounded-md"
              name="name"
              id="name"
              placeholder="Name"
              value={formData.name}
              onChange={handleInputChange}
              required
            />
            <input
              type="email"
              className="p-3 rounded-md"
              name="email"
              id="email"
              placeholder="Email"
              value={formData.email}
              onChange={handleInputChange}
              required
            />
            <input
              type="tel"
              className="p-3 rounded-md"
              name="phone_number"
              id="phone_number"
              placeholder="Phone Number"
              value={formData.phone_number}
              onChange={handleInputChange}
              required
            />
            <textarea
              name="message"
              id="message"
              className="p-3 rounded-md mb-8"
              rows={4}
              placeholder="Message"
              value={formData.message}
              onChange={handleInputChange}
              required
            ></textarea>
            <Button bgColor="bg-[#F245A1]" paddingY="py-2" hoverColor="opacity-90" disabled={isSubmitting}>
              {isSubmitting ? 'Sending...' : 'Send Message'}
            </Button>
          </form>
        </div>
      </div>

      {/* Toast Notification */}
      <Toast
        message={toast.message}
        type={toast.type}
        isVisible={toast.isVisible}
        onClose={hideToast}
      />
    </section>
  );
};

export default Section1;
