import Image from "next/image";

const FeatureCard = ({ heading, description, iconSrc }) => {
  return (
    <div className="w-[90%] md:w-[70%] flex items-center justify-center gap-4 border border-pink-500 bg-white p-2 md:p-4">
      <div>
        <div className="flex justify-center items-center gap-3">
          <div className="flex-shrink-0 w-6 h-6 sm:w-8 sm:h-8">
            <Image
              src={iconSrc}
              alt={`${heading} Icon`}
              width={32} // Next.js will handle this as the max size
              height={32}
              className="w-full h-full"
            />
          </div>
          <h3 className="font-bold text-center text-lg md:text-2xl">{heading}</h3>
        </div>

        <p className="font-medium text-base md:text-xl">{description}</p>
      </div>
    </div>
  );
};

export default FeatureCard;
