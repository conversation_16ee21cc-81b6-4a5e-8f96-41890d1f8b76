import React from "react";
import ServiceCount from "../Services/ServiceCount_2";
import ServiceLifecycleCard from "../Services/ServiceLifecycleCard_2";

const Process = () => {
  const requirementAnalysisAndConsultation = [
    "Understand the business needs, existing systems, and integration requirements.",
    "Detailed consultations with stakeholders to identify integration goals.",
    "Analyze the current application architecture and data flows.",
    "Identify key apps for integration, such as SaaS applications, mobile apps, cloud apps, and on-premise systems.",
    "Define scalability, security, and performance requirements.",
  ];

  const integrationStrategyAndPlanning = [
    "Design an optimal integration plan based on the specific needs of the business.",
    "Create a custom application integration solution using the best-suited technologies and platforms.",
    "Identify integration types: app-to-app integration, cloud-to-cloud application integration, or hybrid models.",
    "Plan the data flow, API integrations, security protocols, and real-time synchronization methods.",
    "Define timelines, milestones, and resource allocation.",
  ];

  const architectureAndDesign = [
    "Develop a scalable and secure architecture for integration.",
    "Create detailed integration blueprints that outline communication protocols between apps.",
    "Design a robust framework for web app integration, mobile app integration, and cloud app integration.",
    "Ensure compatibility between different applications, APIs, and platforms.",
    "Integrate security measures, compliance, and authentication protocols to protect data during transfer.",
  ];

  const developmentAndIntegrationImplementation = [
    "Build and implement the integration solution.",
    "Develop connectors and APIs to facilitate SaaS application integration, web application integration, and app-to-app communication.",
    "Implement data migration & real-time synchronization between different applications.",
    "Utilize middleware solutions if necessary to bridge different systems and ensure smooth communication.",
    "Set up monitoring tools to track performance, data flow, and identify potential bottlenecks.",
  ];

  const testingAndQualityAssurance = [
    "Ensure that the integration is flawless and meets all performance criteria.",
    "Conduct functional and non-functional testing to ensure seamless connectivity between applications.",
    "Perform security testing to ensure data integrity during cloud-to-cloud application integration and other data flows.",
    "Conduct load testing to validate system performance under high usage scenarios.",
    "Run API validation tests to confirm compatibility and error handling between different systems.",
  ];

  const deploymentAndGoLive = [
    "Deploy the integrated solution in the live environment without disruption.",
    "Deploy the application integration solution in a phased approach to minimize risks.",
    "Monitor the initial performance post-deployment to ensure smooth functioning.",
    "Provide training and support to teams to understand the new integrated workflows.",
    "Implement a fallback or rollback mechanism to handle unforeseen issues during deployment.",
  ];

  const postDeploymentSupportAndMaintenance = [
    "Ensure the ongoing success and optimization of the integrated systems.",
    "Provide continuous monitoring and maintenance to identify and resolve potential issues.",
    "Optimize performance and resource usage for enhanced efficiency.",
    "Offer regular updates to the integration setup based on changing business needs.",
    "Extend support for scaling the integration as new applications or systems are added.",
  ];

  return (
    <div className="bg-blue-100 py-5 md:py-10 my-10 md:my-24">
      <div className="w-[90%] md:w-[75%] mx-auto">
        <h2 className="text-2xl md:text-[38px] md:leading-[57px] font-medium text-center">
          App Integration Development <br /> Process by{" "}
          <span className="text-[#7716BC]">Valueans</span>
        </h2>
        <p className="text-base md:text-xl text-center">
          At Valueans, we follow a systematic and comprehensive approach to
          deliver seamless and efficient application integration solutions. Our
          goal is to ensure that your applications—whether web-based, mobile, or
          cloud—communicate smoothly and enhance your business processes. 
          <br />
          <span className="font-semibold">
            Here’s a breakdown of our app integration development process:
          </span>
        </p>
        <div className="flex flex-col md:flex-row justify-between items-center ">
          <div className="flex justify-center items-center gap-2 my-5">
            <ServiceCount>1</ServiceCount>
            <ServiceLifecycleCard
              title="Requirement Analysis and Consultation"
              items={requirementAnalysisAndConsultation}
            />
          </div>
          <div className="flex justify-center items-center gap-2 my-5">
            <ServiceCount>2</ServiceCount>
            <ServiceLifecycleCard
              title="Integration Strategy and Planning"
              items={integrationStrategyAndPlanning}
            />
          </div>
        </div>
        <div className="flex flex-col md:flex-row  justify-between items-center">
          <div className="flex justify-center items-center gap-2 my-5">
            <ServiceCount>3</ServiceCount>
            <ServiceLifecycleCard
              title="Architecture and Design"
              items={architectureAndDesign}
            />
          </div>
          <div className="flex justify-center items-center gap-2 my-5">
            <ServiceCount>4</ServiceCount>
            <ServiceLifecycleCard
              title="Development and Integration Implementation"
              items={developmentAndIntegrationImplementation}
            />
          </div>
        </div>
        <div className="flex flex-col md:flex-row justify-between items-center ">
          <div className="flex justify-center items-center gap-2 my-5">
            <ServiceCount>5</ServiceCount>
            <ServiceLifecycleCard
              title="Testing and Quality Assurance"
              items={testingAndQualityAssurance}
            />
          </div>
          <div className="flex justify-center items-center gap-2 my-5">
            <ServiceCount>6</ServiceCount>
            <ServiceLifecycleCard
              title="Deployment and Go-Live"
              items={deploymentAndGoLive}
            />
          </div>
        </div>
        <div className="w-full md:w-[50%] mx-auto mt-10 flex justify-center items-center gap-2">
          <ServiceCount>7</ServiceCount>
          <ServiceLifecycleCard
            title="Post-Deployment Support and Maintenance"
            items={postDeploymentSupportAndMaintenance}
          />
        </div>
      </div>
    </div>
  );
};

export default Process;
