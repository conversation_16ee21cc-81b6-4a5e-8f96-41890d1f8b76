import Image from "next/image";
import React from "react";
import { FaCheck } from "react-icons/fa";
import Heading from "../Heading/Heading";

const HomeP7 = () => {
  return (
    <div className="container flex flex-col md:flex-row justify-between items-center gap-10 my-24">
      <div className="flex-1">
        <h2 className="capitalize text-2xl md:text-[28px] leading-9 text-left text-[#232536] font-extrabold">
          Why Choose <span className="text-[#7716BC]">Valueans</span> for your{" "}
          <br />
          development services?
        </h2>
        <p className="text-base md:text-lg text-[#232222] font-normal text-left my-5">
          At Valueans, we are committed to providing top-quality app development
          services that drive growth and success. Here's why you should choose
          us as your partner in software Development.
        </p>
        <div className="flex flex-col items-start justify-center space-y-4">
          <div className="flex items-center gap-2">
            <div className="rounded-full bg-[#7716BC] text-white p-1 md:p-2">
              <FaCheck />
            </div>
            <p className="text-sm md:text-base">
              An experienced and dedicated team of professionals
            </p>
          </div>
          <div className="flex items-center gap-2">
            <div className="rounded-full bg-[#7716BC] text-white p-1 md:p-2">
              <FaCheck />
            </div>
            <p className="text-sm md:text-base">
              Customized solutions tailored to your specific needs
            </p>
          </div>
          <div className="flex items-center gap-2">
            <div className="rounded-full bg-[#7716BC] text-white p-1 md:p-2">
              <FaCheck />
            </div>
            <p className="text-sm md:text-base">
              Advanced technology and innovative solutions
            </p>
          </div>
          <div className="flex items-center gap-2">
            <div className="rounded-full bg-[#7716BC] text-white p-1 md:p-2">
              <FaCheck />
            </div>
            <p className="text-sm md:text-base">
              Timely delivery and cost-effective services
            </p>
          </div>
        </div>
      </div>
      <div className="max-w-[592.92px] max-h-[330.79px] relative ">
        <Image
          src="/Images/choose.svg"
          alt="choose"
          width={334}
          height={186}
          objectFit="fill"
          className="w-[334px] h-[186px] md:w-[592.92px] md:h-[330.79px]"
        />
      </div>
    </div>
  );
};

export default HomeP7;
