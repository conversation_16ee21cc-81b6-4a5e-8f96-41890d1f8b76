import React from "react";

const Card = ({ title, content, className }) => {
  return (
    <div
      className={`block w-full md:max-w-xl p-6 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden ${className}`}
    >
      <h5 className="text-[#7716BC] mb-2 text-lg md:text-xl font-bold tracking-tight">
        {title}
      </h5>
      <div className="font-normal text-sm px-4 md:text-base">
        {Array.isArray(content) ? (
          <ul style={{ listStyleType: "disc" }}>
            {content.map((item, index) => (
              <li key={index} dangerouslySetInnerHTML={{ __html: item || "" }} />
            ))}
          </ul>
        ) : (
          <p>{content}</p>
        )}
      </div>
    </div>
  );
};

export default Card;
