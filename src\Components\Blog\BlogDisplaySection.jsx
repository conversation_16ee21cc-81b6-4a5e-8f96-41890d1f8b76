import React from "react";
import BlogInfoDisplay from "@/Components/Blog/BlogInfoDisplay";
import BlogContainer from "@/Components/Blog/BlogContainer";

const BlogDisplaySection = ({ blogs }) => {
  return (
    <div className="flex justify-center align-center gap-16 my-24">
      {/* Render the first blog's info as a featured blog */}
      <BlogInfoDisplay
        imageSrc={blogs[0].imageSrc}
        title={blogs[0].title}
        description="Read about the reasons to start an online business, Read about the reasons to start an online businessRead about the reasons to start an online businessRead about the reasons to start an online businessRead about the reasons to start an online businessRead about the reasons to start an online businessRead about the reasons to start an online businessRead about the reasons to start an online businessRead about the reasons to start an online business" // Make this dynamic
        link={`/blog/${blogs[0].slug}`}
        date={blogs[0].date}
      />
      {/* Render the remaining blogs in BlogContainer */}
      <BlogContainer blogs={blogs.slice(1)} />
    </div>
  );
};

export default BlogDisplaySection;
