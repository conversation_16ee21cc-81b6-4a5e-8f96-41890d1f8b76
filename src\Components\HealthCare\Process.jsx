import React from "react";
import ServiceCount from "../Services/ServiceCount";
import ServiceLifecycleCard from "../Services/ServiceLifecycleCard";
import Connector7 from "../Connectors/Connector7";

const Process = () => {
  const discoveryAndAnalysis = [
    "Conducting in-depth consultations with stakeholders",
    "Analyzing current systems and workflows",
    "Identifying key challenges and opportunities",
  ];

  const strategyAndPlanning = [
    "Defining project scope and objectives",
    "Creating a detailed project roadmap",
    "Allocating resources and setting timelines",
  ];

  const designAndPrototyping = [
    "Creating wireframes and UI/UX designs",
    "Developing interactive prototypes for user feedback",
    "Refining designs based on stakeholder input",
  ];

  const developmentAndIntegration = [
    "Writing clean, efficient code",
    "Integrating with electronic health records (EHR) and other systems",
    "Conducting rigorous testing to ensure functionality and performance",
  ];

  const testingAndQA = [
    "Functional and performance testing",
    "Security and compliance checks",
    "User acceptance testing (UAT) with healthcare professionals",
  ];

  const deploymentAndTraining = [
    "Implementing the software in the client’s environment",
    "Providing comprehensive training to ensure smooth adoption",
    "Offering ongoing support and maintenance",
  ];

  const postLaunchAndIteration = [
    "Monitor product performance and user feedback",
    "Release updates and improvements",
    "Adapt the product strategy as needed to maintain market relevance",
  ];
  return (
    <div className="w-[90vw] mx-auto mb-24">
      <h2 className="text-center text-4xl mb-10">
        Our Custom{" "}
        <span className="text-[#F245A1]">Healthcare Software Development</span>
        Process{" "}
      </h2>
      <div className="relative">
        <div className="flex justify-center items-center gap-8 mb-20 ">
          <div className="flex-col justify-center items-center">
            <ServiceLifecycleCard
              title="Discovery and Analysis"
              items={discoveryAndAnalysis} 
            /> 
            <ServiceCount count={"1"} />
          </div> 
          <div className="flex-col justify-center items-center">
            <ServiceLifecycleCard
              title="Design and Prototyping"
              items={designAndPrototyping}
            />
            <ServiceCount count={"2"} className="z-10" />
          </div>
          <div className="flex-col justify-center items-center">
            <ServiceLifecycleCard title="Testing and QA" items={testingAndQA} />
            <ServiceCount count={"3"} />
          </div>
        </div>
        <div className="absolute top-[48%] left-[24%]">
          <div
            className="w-[75px] h-[120px] border-dashed border-t-2 border-r-2 border-gray-700 absolute"
            style={{
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
            }}
          />
        </div>
        <div className="absolute top-[50%] left-[38%]">
          <div
            className="w-[320px] h-[100px] border-dashed border-b-2 border-r-2 border-gray-700 absolute"
            style={{
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
            }}
          />
        </div>
        <div className="absolute top-[46%] left-[55%]">
          <div
            className="w-[90px] h-[110px] border-dashed border-t-2 border-r-2 border-gray-700 absolute"
            style={{
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
            }}
          />
        </div>
        <div className="absolute top-[48%] left-[69%]">
          <div
            className="w-[320px] h-[90px] border-dashed border-b-2 border-r-2 border-gray-700 absolute"
            style={{
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
            }}
          />
        </div>
        <div className="absolute top-[46%] left-[84%]">
          <div
            className="w-[75px] h-[120px] border-dashed border-t-2 border-r-2 border-gray-700 absolute"
            style={{
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
            }}
          />
        </div>
        <div className=" ml-[15%] flex justify-center items-center gap-8 mt-20 relative">
          <div className="flex-col justify-center items-center absolute top-[5%] -left-[1%]">
            <ServiceCount count={"4"} />
            <ServiceLifecycleCard
              title="Strategy and Planning"
              items={strategyAndPlanning}
            />
          </div>
          <div className="flex-col justify-center items-center ml-[35%]">
            <ServiceCount count={"5"} />
            <ServiceLifecycleCard
              title="Development and Integration"
              items={developmentAndIntegration}
            />
          </div>
          <div className="flex-col justify-center items-center ">
            <ServiceCount count={"6"} />
            <ServiceLifecycleCard
              title="Deployment and Training"
              items={deploymentAndTraining}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Process;
