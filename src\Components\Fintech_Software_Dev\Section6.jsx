import React from "react";

const Container = ({ children, bgColor = "bg-white", className = "" }) => {
  return (
    <div className={`w-full p-3 md:px-[42px] md:py-6 ${bgColor} ${className}`}>
      {children}
    </div>
  );
};
const Card = ({ title, description, bgColor = "bg-white" }) => {
  return (
    <div
      className={`block max-w-sm p-3 md:p-4 shadow-sm rounded-md ${bgColor}`}
    >
      <h3 className="text-xl md:text-xl font-semibold capitalize">
        {title}
      </h3>
      <p
        className="text-base md:text-lg text-justify"
        dangerouslySetInnerHTML={{ __html: description }}
      />
    </div>
  );
};

const Section6 = () => {
  return (
    <div className="w-[85vw] mx-auto mb-10 md:mb-24">
      <h2 className="text-xl md:text-3xl text-center font-semibold">
        Recent Developments Adopted in{" "}
        <span className="text-[#F245A1]">Fintech</span>  Software
        Development
      </h2>
      <p className="md:w-[75%] md:mx-auto text-base md:text-xl text-center">
       The rise of new technologies has given birth to new trends that are likely to dictate the future of custom fintech software development. These include the following:
      </p>
      <div className="w-full md:w-[85%] md:mx-auto mt-6 md:mt-[42px]">
        <Container
          className="flex flex-col md:flex-row justify-center md:justify-between items-center gap-3 p-3 md:p-6"
          bgColor="bg-pink-100"
        >
          <Card
            bgColor="bg-purple-200"
            title={"Artificial Intelligence and Machine Learning"}
            description={
              "Analytics are now being adopted into the world of business and <a href='/Technologies/AI_ML' class='text-[#7716BC] hover:underline'>AI & ML</a> are changing everything about it. Fintech uses these technologies to conduct <a href='/Technologies/Predictive_Analysis' class='text-[#7716BC] hover:underline'>predictive analysis</a> , estimate market developments, detect fraud, and make tailored financial suggestions, which is much better compared to traditional methods. "
            }
          />
          <Card
            bgColor="bg-pink-200"
            title={"Blockchain and Decentralized Finance (DeFi)"}
            description={
              "Fintech is increasingly utilizing blockchain technology because of its security features and the decentralization it offers. There are DeFi platforms where traditional financial intermediaries are no longer needed, meaning users can transact freely and securely at any time. "
            }
          />
        </Container>
        <Container
          className="flex flex-col md:flex-row justify-center md:justify-between items-center  gap-3 p-3 md:p-6 my-3 md:my-6"
          bgColor="bg-violet-100"
        >
          <Card
            bgColor="bg-violet-200"
            title={"Biometric Identification"}
            description={
              "Fingerprint and facial recognition have started becoming a standard feature in fintech applications alongside traditional passwords due to the increasing number of cybercrimes. This form of biometric authentication strengthens security and offers an easier alternative for users."
            }
          />
          <Card
            bgColor="bg-pink-200"
            title={"Embedded Finance"}
            description={
              "Non-financial platforms embed their financial services into e-commerce websites and ride-sharing applications due to the convenience they provide to consumers and the ability to reach more customers."
            }
          />
        </Container>
        <Container
          className="flex flex-col md:flex-row justify-center md:justify-between items-center my-3 md:my-6 gap-3 p-3 md:p-6"
          bgColor="bg-pink-100"
        >
          <Card
            bgColor="bg-purple-100"
            title={"Open Banking "}
            description={
              "Open banking is bettering financial solutions for users by allowing third party developers to create applications for financial institutions interacting through APIs. This method also fosters innovation and increases competitiveness within the market. "
            }
          />
          <Card
            bgColor="bg-pink-200"
            title={" Cloud Computing"}
            description={
              "The expansion of cloud-based fintech solutions allows customers to access greater scalability, security, and cost-efficiency. More businesses are implementing <a href='/Cloud_Services' class='text-[#7716BC] hover:underline'> cloud technology</a> to improve their financial operations and enhance the overall user experience."
            }
          />
        </Container>
        <Container
          className="flex flex-col md:flex-row justify-center md:justify-between items-center my-3 gap-3 p-3 md:p-6"
          bgColor="bg-violet-100"
        >
          <Card
            bgColor="bg-violet-200"
            title={" Assisted Customer Service "}
            description={
              "<a href='/Technologies/NLP' class='text-[#7716BC] hover:underline'>Natural language processing (NLP)</a> enables customer service. Accomplished through voice assistants, chatbots, and automated document analysis. It streamlines compliance reporting. "
            }
          />
          
        </Container>
      </div>
    </div>
  );
};

export default Section6;
