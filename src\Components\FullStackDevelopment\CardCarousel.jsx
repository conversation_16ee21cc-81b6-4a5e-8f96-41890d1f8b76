"use client";

import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/pagination";
import { Pagination } from "swiper/modules";
import Card from "../UI_UX/Gradient_card";

const cardData = [
  {
    title: "E-commerce and Retail",
    description:
      "We build powerful, scalable e-commerce platforms with robust back-end systems that handle high volumes of transactions, while our front-end designs ensure your users enjoy a smooth, frictionless shopping experience. Whether you need a simple online store or a complex multi-vendor platform, our full stack web development company makes sure that you retain your customers 100%. They’ll be coming back for seconds. ",
    items: [],
  },
  {
    title: "Education and E-Learning",
    description:
      "The next generation of leaders as we call them. We feel strongly about education and think that our young ones should get high quality learning which is accessible for the masses. Our full stack services include building e-learning portals, virtual classrooms, and content management systems that are scalable, secure, and user-friendly. From managing online courses to creating interactive learning modules, we provide comprehensive solutions to educational institutions and e-learning companies.",
    items: [],
  },
  {
    title: "Logistics and Supply Chain",
    description: 
      "For logistics and supply chain companies, tracking, automation, and real-time data are essential for optimizing operations. Our full stack development agency builds solutions that streamline the movement of goods, improve inventory management, and ensure real-time tracking of shipments. We integrate with IoT devices and GPS systems, providing seamless communication between various components of the supply chain.",
    items: [],
  },
  {
    title: "Real Estate and Property Management",
    description:
      "We develop full-stack solutions that enable property listings, virtual tours, and secure transactions, while our back-end systems handle complex data processing, CRM integration, and document management. Now you can have a property tour on any device. The world has changed. No more do you have to make the trip to the other side of town to see a property. Our platforms are designed to simplify property management, improve customer interaction, and enhance overall business efficiency.",
    items: [],
  },
  // Add more cards here if needed
];

const CardCarousel = () => {
  return (
    <Swiper
      spaceBetween={20}
      breakpoints={{
        640: { slidesPerView: 1 },
        768: { slidesPerView: 2 },
        1024: { slidesPerView: 3 },
      }}
      pagination={{ clickable: true }}
      modules={[Pagination]}
    >
      {cardData.map((item, index) => (
        <SwiperSlide key={index}>
          <Card
            title={item.title}
            description={item.description}
            items={item.items}
          />
        </SwiperSlide>
      ))}
    </Swiper>
  );
};

export default CardCarousel;
