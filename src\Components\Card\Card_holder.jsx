import Image from "next/image";
import React from "react";

const Card_holder = ({
  title1,
  description1,
  title2,
  description2,
  title3,
  description3,
  bgcolor_1,
  bgcolor_2,
}) => {
  return (
    <div
      className={`w-full max-w-2xl mx-10  ${bgcolor_1} p-4 border border-purple-700`}
    >
      {/* block1 */}
      <div
        className={`block w-[100%] h-[30%] mx-auto my-4 p-6 ${bgcolor_2} border border-gray-200 overflow-hidden`}
      >
        <h5 className="mb-2 text-2xl font-semibold tracking-tight ">
          {title1}
        </h5>
        <p className="font-normal text-xl">{description1}</p>
      </div>
      {/* block2 */}
      <div className="block w-[90%] mx-auto my-4 p-6 bg-white border border-purple-700 rounded-lg shadow ">
        <div className="flex items-center">
          <div>
            <Image src={"/Images/Tick.png"} alt="Tick" width={42} height={42} />
          </div>
          <h5 className="mb-2 text-2xl font-bold tracking-tight text-[#232222]">
            {title2}
          </h5>
        </div>

        <p className="font-normal text-xl text-[#232222]">{description2}</p>
      </div>
      {/* block3 */}
      <div className="block w-[90%] mx-auto my-4 p-6 bg-white border border-purple-700 rounded-lg shadow hover:bg-gray-100">
        <div className="flex items-center">
          <div>
            <Image src={"/Images/Tick.png"} alt="Tick" width={42} height={42} />
          </div>
          <h5 className="mb-2 text-2xl font-bold tracking-tight text-[#232222]">
            {title3}
          </h5>
        </div>

        <p className="font-normal text-xl text-[#232222]">{description3}</p>
      </div>
    </div>
  );
};

export default Card_holder;
