import Image from "next/image";
import PinkDotCard from "../PWA_development/PinkDotCard";

const Section9 = ({
  heading,
  spanHeading,
  paragrapgh,
  cardContent,
  afterSpanHeading,
  image,
}) => {
  return (
    <section className="bg-blue-100 px-4">
      <div className="py-10 md:mb-24 md:py-8 md:px-[75px]">
        <h2 className="text-xl md:text-3xl text-center font-semibold mb-1">
          {heading} <span className="text-[#7716BC]">{spanHeading}</span>{" "}
          {afterSpanHeading}
        </h2>
        <p className="md:w-[85%] md:mx-auto mb-4 text-base md:text-xl text-center">
          {paragrapgh}
        </p>
        <div className="flex flex-col md:flex-row justify-center gap-4 md:justify-between items-center">
          <div className="md:w-[50%]">
            <PinkDotCard cardContent={cardContent} />
          </div>
          <div className="relative w-full md:w-[50%] min-h-[250px] md:h-[400px]">
            <Image src={image} alt="AI" fill className="object-cover" />
          </div>
        </div>
      </div>
    </section>
  );
};

export default Section9;
