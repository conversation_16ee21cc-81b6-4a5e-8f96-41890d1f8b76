import React from 'react'

const Gradient_card = ({ title, description, items, height  }) => {
  return (
    <>
        <div className={` w-[90vw] ${height? height:"h-auto"} md:max-w-[380px]  bg-gradient-to-br from-pink-500 to-purple-700 text-white p-6 rounded-lg`}>
      <h2 className="text-xl font-bold">{title}</h2>
        <p
        className="mt-2 text-base text-justify"
        dangerouslySetInnerHTML={{ __html: description }}
      />
      <ul className="mt-1 space-y-1">
        {items.map((item, index) => (
          <li key={index} className="font-semibold text-base text-justify">• {item}</li>
        ))}
      </ul>
    </div>
    </>
  )
}

export default Gradient_card
